<template>
  <div class="product-view-container">
    <!-- 页面标题和返回按钮 -->
    <div class="page-header">
      <div class="header-left">
        <div class="title-info">
          <h1 class="page-title">积分商品详情</h1>
          <p class="page-description">查看积分商品的详细信息</p>
        </div>
      </div>
    </div>

    <!-- 详情内容 -->
    <el-card class="detail-card" shadow="never" v-loading="loading">
      <div class="detail-content">
        <!-- 基本信息 -->
        <div class="info-section">
          <h3 class="section-title">基本信息</h3>
          <div class="basic-info-grid">
            <div class="info-item">
              <span class="info-label">商品ID</span>
              <span class="info-value">{{ productData.productId }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">商品名称</span>
              <span class="info-value product-name">{{
                productData.productName
              }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">所需积分</span>
              <span class="info-value points-value">{{
                productData.requiredPoints
              }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">库存数量</span>
              <span
                class="info-value stock-value"
                :class="{ 'low-stock': productData.stockCount < 10 }"
              >
                {{ productData.stockCount }}
              </span>
            </div>
            <div class="info-item">
              <span class="info-label">上架状态</span>
              <el-tag
                :type="productData.isOnShelf ? 'success' : 'info'"
                effect="light"
              >
                {{ productData.isOnShelf ? '已上架' : '未上架' }}
              </el-tag>
            </div>
            <div class="info-item">
              <span class="info-label">最后修改人</span>
              <span class="info-value">{{ productData.lastModifier }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">最后修改时间</span>
              <span class="info-value">{{ productData.lastModifyTime }}</span>
            </div>
          </div>
        </div>

        <!-- 商品图片 -->
        <div class="info-section">
          <h3 class="section-title">商品图片</h3>
          <div class="image-section">
            <div class="main-image-container">
              <div class="image-title">商品主图</div>
              <div class="main-image">
                <el-image
                  :src="productData.productImage"
                  fit="cover"
                  :preview-src-list="[productData.productImage]"
                />
              </div>
            </div>

            <div
              class="gallery-container"
              v-if="
                productData.productGallery &&
                productData.productGallery.length > 0
              "
            >
              <div class="image-title">商品图集</div>
              <div class="gallery-images">
                <el-image
                  v-for="(image, index) in galleryImages"
                  :key="index"
                  :src="image"
                  fit="cover"
                  :preview-src-list="galleryImages"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 规格信息 -->
        <div
          class="info-section"
          v-if="
            productData.specifications && productData.specifications.length > 0
          "
        >
          <h3 class="section-title">规格信息</h3>
          <el-table
            :data="productData.specifications"
            style="width: 100%"
            border
          >
            <el-table-column prop="specName" label="规格名称" min-width="150" />
            <el-table-column
              prop="requiredPoints"
              label="所需积分"
              min-width="100"
            >
              <template #default="{ row }">
                <span class="points-text">{{ row.requiredPoints }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="stockCount" label="库存数量" min-width="100">
              <template #default="{ row }">
                <span :class="{ 'low-stock': row.stockCount < 10 }">{{
                  row.stockCount
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="规格图片" min-width="120">
              <template #default="{ row }">
                <el-image
                  v-if="row.image"
                  :src="row.image"
                  style="width: 60px; height: 60px; border-radius: 4px"
                  fit="cover"
                  :preview-src-list="[row.image]"
                />
                <span v-else>-</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 商品详情 -->
        <div class="info-section">
          <h3 class="section-title">商品详情</h3>
          <div
            class="product-detail-content"
            v-html="productData.productDetail"
          ></div>
          <div v-if="!productData.productDetail" class="empty-detail">
            暂无详情信息
          </div>
        </div>
      </div>
      <CustomBtn
        style="margin: 20px 0 20px 20px"
        type="back"
        @click="handleBack"
      />
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { getPointsProductDetail } from '@/api/points-mall';
import { ElMessage } from 'element-plus';
import CustomBtn from '@/components/custom-btn.vue';

// 接收参数
const props = defineProps({
  productId: {
    type: [String, Number],
    required: true,
  },
});

// 定义事件
const emit = defineEmits(['back']);

// 数据
const loading = ref(false);
const productData = ref({
  productId: '',
  productName: '',
  productImage: '',
  requiredPoints: 0,
  stockCount: 0,
  isOnShelf: 0,
  lastModifier: '',
  lastModifyTime: '',
  description: '',
  productDetail: '',
  productGallery: [],
  specifications: [],
});

// 计算图片集合
const galleryImages = computed(() => {
  if (
    !productData.value.productGallery ||
    productData.value.productGallery.length === 0
  ) {
    return [];
  }

  // 如果是字符串数组，直接返回
  if (typeof productData.value.productGallery[0] === 'string') {
    return productData.value.productGallery;
  }

  // 如果是对象数组，提取url属性
  return productData.value.productGallery
    .map((item) => {
      if (typeof item === 'string') return item;
      return item.url || item.response?.url || '';
    })
    .filter((url) => url);
});

// 获取商品详情
const fetchProductDetail = async () => {
  loading.value = true;
  try {
    const response = await getPointsProductDetail(props.productId);
    if (response.code === 200) {
      productData.value = response.data;

      // 如果没有图集数据，初始化为空数组
      if (!productData.value.productGallery) {
        productData.value.productGallery = [];
      }

      // 如果没有规格数据，初始化为空数组
      if (!productData.value.specifications) {
        productData.value.specifications = [];
      }
    } else {
      ElMessage.error(response.message || '获取商品详情失败');
    }
  } catch (error) {
    console.error('获取商品详情失败:', error);
    ElMessage.error('获取详情失败');
  } finally {
    loading.value = false;
  }
};

// 返回列表
const handleBack = () => {
  emit('back');
};

// 组件挂载时获取数据
onMounted(() => {
  fetchProductDetail();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables.scss' as *;

.product-view-container {
  min-height: 100vh;
  max-width: 1280px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;

  .header-left {
    .title-info {
      .page-title {
        font-size: 20px;
        font-weight: 600;
        color: $text-color;
        margin: 0 0 6px 0;
      }

      .page-description {
        font-size: 13px;
        color: $text-color-light;
        margin: 0;
      }
    }
  }
}

.detail-card {
  border-radius: 10px;

  .detail-content {
    padding: 20px;
    .info-section {
      margin-bottom: 32px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: $text-color;
        margin: 0 0 16px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;
      }

      .basic-info-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px 24px;

        .info-item {
          display: flex;
          align-items: center;

          .info-label {
            font-size: 14px;
            color: $search-label-color;
            min-width: 100px;
            margin-right: 12px;
          }

          .info-value {
            font-size: 14px;
            color: $text-color;
            flex: 1;

            &.product-name {
              font-weight: 600;
              color: #165dff;
            }

            &.points-value {
              font-weight: 600;
              color: #fa8c16;
            }

            &.stock-value {
              font-weight: 600;
              color: #52c41a;

              &.low-stock {
                color: #ff4d4f;
              }
            }
          }
        }
      }

      .image-section {
        display: flex;
        flex-wrap: wrap;
        gap: 24px;

        .main-image-container,
        .gallery-container {
          .image-title {
            font-size: 14px;
            color: $search-label-color;
            margin-bottom: 12px;
          }
        }

        .main-image-container {
          .main-image {
            width: 200px;
            height: 200px;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e0e0e0;

            :deep(.el-image) {
              width: 100%;
              height: 100%;
            }
          }
        }

        .gallery-container {
          .gallery-images {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;

            :deep(.el-image) {
              width: 120px;
              height: 120px;
              border-radius: 6px;
              border: 1px solid #e0e0e0;
            }
          }
        }
      }

      .product-detail-content {
        padding: 16px;
        background-color: #f9f9f9;
        border-radius: 6px;
        min-height: 100px;
      }

      .empty-detail {
        padding: 40px 0;
        text-align: center;
        color: $search-label-color;
        font-size: 14px;
        background-color: #f9f9f9;
        border-radius: 6px;
      }

      .points-text {
        color: #fa8c16;
        font-weight: 500;
      }

      .low-stock {
        color: #ff4d4f;
        font-weight: 500;
      }
    }
  }
}

:deep(.el-card) {
  border-radius: 10px;
  overflow: hidden;
}
</style>
