<template>
  <div class="redpacket-detail-container">
    <!-- 页面标题和返回按钮 -->
    <div class="page-header">
      <div class="header-left">
        <div class="title-info">
          <h1 class="page-title">红包详情</h1>
          <p class="page-description">查看用户红包领取详细信息</p>
        </div>
      </div>
    </div>

    <!-- 详情内容 -->
    <el-card class="detail-card" shadow="never" v-loading="loading">
      <div class="detail-content">
        <!-- 用户基本信息 -->
        <div class="info-section">
          <h3 class="section-title">用户信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">微信昵称</span>
              <span class="info-value">{{ detailData.wechatNickname }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">用户ID</span>
              <span class="info-value">{{ detailData.userId }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">手机号码</span>
              <span class="info-value">{{
                detailData.userInfo?.phone || '-'
              }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">地址</span>
              <span class="info-value">{{
                detailData.userInfo?.address || '-'
              }}</span>
            </div>
          </div>
        </div>

        <!-- 红包信息 -->
        <div class="info-section">
          <h3 class="section-title">红包信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">红包类型</span>
              <span
                :class="[
                  'custom-tag',
                  detailData.redpacketType === 'merchant'
                    ? 'merchant-tag'
                    : 'consumer-tag',
                ]"
              >
                {{ detailData.redpacketTypeText }}
              </span>
            </div>
            <div class="info-item">
              <span class="info-label">获得奖品</span>
              <span class="info-value prize-value">{{ detailData.prize }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">领取时间</span>
              <span class="info-value">{{ detailData.receiveTime }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">活动来源</span>
              <span class="info-value">{{
                detailData.redpacketInfo?.source || '-'
              }}</span>
            </div>
          </div>
        </div>

        <!-- 红包活动详情 -->
        <div class="info-section">
          <h3 class="section-title">活动详情</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">总金额</span>
              <span class="info-value amount-value">{{
                detailData.redpacketInfo?.totalAmount || '-'
              }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">剩余金额</span>
              <span class="info-value amount-value">{{
                detailData.redpacketInfo?.remainAmount || '-'
              }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">过期时间</span>
              <span class="info-value">{{
                detailData.redpacketInfo?.expiryTime || '-'
              }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">状态</span>
              <span class="status-tag active-status">已领取</span>
            </div>
          </div>
        </div>
      </div>
      <CustomBtn style="margin-top: 20px" type="back" @click="handleBack" />
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getRedpacketDetail } from '@/api/redpacket';
import { ElMessage } from 'element-plus';
import CustomBtn from '@/components/custom-btn.vue';

// 接收参数
const props = defineProps({
  redpacketId: {
    type: [String, Number],
    required: true,
  },
});

// 定义事件
const emit = defineEmits(['back']);

// 数据
const loading = ref(false);
const detailData = ref({});

// 获取详情数据
const fetchDetail = async () => {
  loading.value = true;
  try {
    const response = await getRedpacketDetail(props.redpacketId);
    if (response.code === 200) {
      detailData.value = response.data;
    }
  } catch (error) {
    console.error('获取红包详情失败:', error);
    ElMessage.error('获取详情失败');
  } finally {
    loading.value = false;
  }
};

// 返回列表
const handleBack = () => {
  emit('back');
};

// 组件挂载时获取数据
onMounted(() => {
  fetchDetail();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables.scss' as *;

.redpacket-detail-container {
  padding: 20px;
  min-height: 100vh;
  max-width: 1280px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 16px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;

    .title-info {
      .page-title {
        font-size: 20px;
        font-weight: 600;
        color: $text-color;
        margin: 0 0 6px 0;
      }

      .page-description {
        font-size: 13px;
        color: $text-color-light;
        margin: 0;
      }
    }
  }
}

.detail-card {
  border-radius: 10px;

  .detail-content {
    .info-section {
      margin-bottom: 32px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: $text-color;
        margin: 0 0 16px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px 24px;

        .info-item {
          display: flex;
          align-items: center;

          .info-label {
            font-size: 14px;
            color: $search-label-color;
            min-width: 80px;
            margin-right: 12px;
          }

          .info-value {
            font-size: 14px;
            color: $text-color;
            flex: 1;

            &.prize-value {
              font-weight: 600;
              color: #165dff;
            }

            &.amount-value {
              font-weight: 600;
              color: #00b42b;
            }
          }

          .custom-tag {
            display: inline-block;
            padding: 4px 12px;
            font-size: 14px;
            border-radius: 16px;
            font-weight: 500;
            text-align: center;
            min-width: 70px;

            &.merchant-tag {
              background-color: #e9eeff;
              color: #165dff;
              border: 1px solid #e9eeff;
            }

            &.consumer-tag {
              background-color: #ebf9f9;
              color: #11c6c2;
              border: 1px solid #ebf9f9;
            }
          }

          .status-tag {
            display: inline-block;
            padding: 4px 12px;
            font-size: 14px;
            border-radius: 16px;
            font-weight: 500;
            text-align: center;
            min-width: 60px;

            &.active-status {
              background-color: #e8f5e8;
              color: #00b42b;
              border: 1px solid #e8f5e8;
            }
          }
        }
      }
    }
  }
}

:deep(.el-card) {
  border-radius: 10px;
  overflow: hidden;
}
</style>
