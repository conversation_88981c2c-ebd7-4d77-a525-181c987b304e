<template>
  <div class="verification-detail-page">
    <div class="page-header">
      <h2>真伪数据详情</h2>
      <p>查看系统内特定真伪数据的详细记录</p>
    </div>

    <el-card class="detail-card" shadow="never" v-loading="loading">
      <div class="detail-content" v-if="detailData">
        <div class="detail-row">
          <div class="detail-item">
            <div class="detail-label">验证结果</div>
            <div class="detail-value">
              <p>{{ getScanResultText(detailData.scanResult) }}</p>
            </div>
          </div>
          <div class="detail-item">
            <div class="detail-label">验证时间</div>
            <div class="detail-value">
              {{ detailData.scanTime || '2024-10-01 10:00' }}
            </div>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <div class="detail-label">商品名称</div>
            <div class="detail-value">
              {{ detailData.productName || '黑色时尚眼镜框' }}
            </div>
          </div>
          <div class="detail-item">
            <div class="detail-label">防伪码</div>
            <div class="detail-value">
              {{ detailData.antiCounterfeitCode || '123456789' }}
            </div>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <div class="detail-label">经销商</div>
            <div class="detail-value">
              {{ detailData.dealer || 'XX眼镜经销商' }}
            </div>
          </div>
          <div class="detail-item">
            <div class="detail-label">出库时间</div>
            <div class="detail-value">
              {{ detailData.outboundTime || '2024-09-01 08:00' }}
            </div>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <div class="detail-label">终端门店</div>
            <div class="detail-value">
              {{ detailData.retailStore || 'XX眼镜店' }}
            </div>
          </div>
          <div class="detail-item">
            <div class="detail-label">核销时间</div>
            <div class="detail-value">
              {{ detailData.verificationTime || '2024-10-01 10:30' }}
            </div>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <div class="detail-label">扫码次数</div>
            <div class="detail-value">{{ detailData.scanCount || '5' }}</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">微信昵称</div>
            <div class="detail-value">
              {{ detailData.wechatNickname || '张三' }}
            </div>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <div class="detail-label">用户id</div>
            <div class="detail-value">{{ detailData.userId || '123456' }}</div>
          </div>
        </div>

        <!-- 返回按钮 -->
        <div class="return-button">
          <el-button type="primary" @click="handleReturn">
            <el-icon><Back /></el-icon>
            <div style="padding-left: 5px">返回列表</div>
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Back } from '@element-plus/icons-vue';
import { getVerificationDataDetail } from '@/api/service';

const route = useRoute();
const router = useRouter();

// 数据
const loading = ref(false);
const detailData = ref(null);

// 获取扫码结果文本
const getScanResultText = (scanResult) => {
  const textMap = {
    authentic: '正品',
    fake: '非正品',
    authorized: '已被授权',
  };
  return textMap[scanResult] || '未知';
};

// 获取详情数据
const fetchDetail = async () => {
  loading.value = true;
  try {
    const id = route.params.id;
    const response = await getVerificationDataDetail(id);
    detailData.value = response.data;
    loading.value = false;
  } catch (error) {
    console.error('获取详情失败:', error);
    ElMessage.error('获取详情失败');
    loading.value = false;
  }
};

// 返回列表
const handleReturn = () => {
  router.back();
};

// 组件挂载时获取数据
onMounted(() => {
  fetchDetail();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

.verification-detail-page {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: $spacing-xl;
}

.page-header {
  margin-bottom: $spacing-xl;

  h2 {
    margin: 0 0 $spacing-sm 0;
    color: $text-color-dark;
    font-size: 24px;
    font-weight: 600;
  }

  p {
    margin: 0;
    color: $text-color-light;
    font-size: 14px;
  }
}

.detail-card {
  border-radius: $border-radius;

  .detail-content {
    padding: $spacing-xl;
  }
}

.detail-row {
  display: flex;
  margin-bottom: $spacing-xl;
  gap: $spacing-xxl;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-item {
  flex: 1;

  &.full-width {
    flex: 0 0 100%;
    width: 100%;
  }

  .detail-label {
    font-size: 14px;
    color: $text-color-light;
    margin-bottom: $spacing-sm;
    font-weight: 500;
  }

  .detail-value {
    font-size: 16px;
    color: $text-color-dark;
    line-height: 1.5;

    .el-tag {
      font-weight: 500;
    }
  }
}

// 图片预览相关样式
.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 8px;

  .image-item {
    width: 100px;
    height: 100px;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    border: 1px solid $border-color;

    img,
    .el-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s;
    }

    &:hover img {
      transform: scale(1.05);
    }
  }
}

.return-button {
  margin-top: $spacing-xxl;
  padding-top: $spacing-xl;
  border-top: 1px solid $border-color;
  text-align: right;

  .el-button {
    display: inline-flex;
    align-items: center;
    gap: $spacing-sm;
    background-color: #165dff !important;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .detail-row {
    flex-direction: column;
    gap: $spacing-lg;
  }
}
</style>
