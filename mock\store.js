// 门店相关的mock接口

// 使用全局数据存储
let nextId = 6 // 新增门店置顶的起始ID
let auditNextId = 6 // 新增门店审核的起始ID
let tagNextId = 6 // 新增门店标签的起始ID

// 门店认证类型
const certifications = [
  { id: 1, name: '青少年验配专家', icon: 'youth' },
  { id: 2, name: '配镜中心', icon: 'center' },
  { id: 3, name: '标准门店', icon: 'standard' },
  { id: 4, name: '社区店', icon: 'community' }
]

// 可用门店标签列表
let availableTags = [
  '免费验光',
  '品牌集合',
  '24小时',
  '周末营业',
  '专家坐诊',
  '儿童区',
  '成人专区',
  '隐形眼镜',
  '角度塑形镜',
  'RGP',
  '青少年近视防控',
  '成人视力矫正',
  '老花镜',
  '防蓝光',
  '抗疲劳',
  '变色镜片',
  '太阳镜',
  '运动眼镜',
  '渐进镜片',
  '高端镜片',
  '豪华眼镜',
  '京东联名',
  '美团联名',
  '抖音联名'
]

// 可用门店认证列表
let availableCertifications = ['医学验光配镜', '青少年验光专家', '成人视力矫正中心', '儿童斜弱视防治', '近视防控示范店', '医学验光配镜']

// 生成门店标签数据
let storeTagList = [
  {
    id: 'ST-001',
    name: '李白眼镜三里屯旗舰店',
    areas: ['北京市-朝阳区'],
    phone: '010-12345678',
    certification: '青少年验配专家',
    level: 4,
    type: '李白智能版合作门店',
    tags: ['高端', '旗舰店', '新门店']
  },
  {
    id: 'ST-002',
    name: '李白眼镜南京东路店',
    areas: ['上海市-黄浦区'],
    phone: '021-23456789',
    certification: '青少年验配专家',
    level: 5,
    type: '李白智能版合作门店',
    tags: ['高端', '旗舰店', '新品店']
  },
  {
    id: 'ST-003',
    name: '李白眼镜天河城店',
    areas: ['广东省-广州市-天河区'],
    phone: '020-34567890',
    certification: '配镜中心',
    level: 3,
    type: '李白专业版合作门店',
    tags: ['热门', '折扣店']
  },
  {
    id: 'ST-004',
    name: '李白眼镜科技园店',
    areas: ['广东省-深圳市-南山区'],
    phone: '0755-45678901',
    certification: '标准门店',
    level: 2,
    type: '李白专业版合作门店',
    tags: ['科技园区', '新品店']
  },
  {
    id: 'ST-005',
    name: '李白眼镜中关村店',
    areas: ['北京市-海淀区'],
    phone: '010-56789012',
    certification: '社区店',
    level: 4,
    type: '李白智能版合作门店',
    tags: ['高端', '热门']
  }
]

// 生成门店审核数据
let storeAuditList = [
  {
    id: 'AU-001',
    name: '李白眼镜中关村旗舰店',
    areas: ['北京市-海淀区'],
    address: '北京市海淀区中关村大街15号',
    contactName: '张三',
    contactPhone: '010-12345678',
    businessName: '京东科技',
    businessPhone: '010-87654321',
    type: '李白智能版合作门店',
    level: '金牌门店',
    submitTime: '2025-06-20 10:30',
    auditStatus: '待审批',
    auditTime: '',
    auditor: ''
  },
  {
    id: 'AU-002',
    name: '李白眼镜西单购物中心店',
    areas: ['北京市-西城区'],
    address: '北京市西城区西单北大街120号',
    contactName: '李四',
    contactPhone: '010-23456789',
    businessName: '西单商场',
    businessPhone: '010-98765432',
    type: '李白专业版合作门店',
    level: '银牌门店',
    submitTime: '2025-06-18 14:15',
    auditStatus: '审批通过',
    auditTime: '2025-06-19 09:30',
    auditor: '管理员',
    auditComment: '门店资质齐全，符合开店要求，同意通过。'
  },
  {
    id: 'AU-003',
    name: '李白眼镜外滩店',
    areas: ['上海市-黄浦区'],
    address: '上海市黄浦区外滩5号',
    contactName: '王五',
    contactPhone: '021-34567890',
    businessName: '外滩商业',
    businessPhone: '021-09876543',
    type: '李白智能版合作门店',
    level: '金牌门店',
    submitTime: '2025-06-17 16:45',
    auditStatus: '审批驳回',
    auditTime: '2025-06-18 11:20',
    auditor: '李四',
    auditComment: '门店位置不符合要求，建议重新选址后再申请。'
  },
  {
    id: 'AU-004',
    name: '李白眼镜天河城店',
    areas: ['广东省-广州市-天河区'],
    address: '广州市天河区天河路208号',
    contactName: '赵六',
    contactPhone: '020-45678901',
    businessName: '天河城集团',
    businessPhone: '020-10987654',
    type: '李白智能版合作门店',
    level: '金牌门店',
    submitTime: '2025-06-15 09:15',
    auditStatus: '待审批',
    auditTime: '',
    auditor: ''
  },
  {
    id: 'AU-005',
    name: '李白眼镜科技园店',
    areas: ['广东省-深圳市-南山区'],
    address: '深圳市南山区科技园路15号',
    contactName: '钱七',
    contactPhone: '0755-56789012',
    businessName: '科技园物业',
    businessPhone: '0755-21098765',
    type: '李白专业版合作门店',
    level: '银牌门店',
    submitTime: '2025-06-12 14:50',
    auditStatus: '审批通过',
    auditTime: '2025-06-13 16:30',
    auditor: '王五',
    auditComment: '科技园区位置优越，周边客流量大，审核通过。'
  }
]

// 生成门店置顶数据
let storeTopList = [
  {
    id: 'BD-001',
    name: '李白眼镜旗舰店,\n李白眼镜中央店,\n李白眼镜王府井店',
    areas: ['北京市-朝阳区', '北京市-东城区'],
    updateTime: '2025-06-20 14:30',
    operator: '管理员'
  },
  {
    id: 'BD-002',
    name: '李白眼镜北京王府井店,\n李白眼镜望京店',
    areas: ['北京市-东城区', '北京市-朝阳区'],
    updateTime: '2025-06-18 09:15',
    operator: '李四'
  },
  {
    id: 'BD-003',
    name: '李白眼镜上海南京路店,\n李白眼镜静安寺店',
    areas: ['上海市-黄浦区', '上海市-静安区'],
    updateTime: '2025-06-15 16:45',
    operator: '王五'
  },
  {
    id: 'BD-004',
    name: '李白眼镜广州天河店,\n李白眼镜珠江新城店,\n李白眼镜越秀店',
    areas: ['广东省-广州市-天河区', '广东省-广州市-越秀区'],
    updateTime: '2025-06-10 11:20',
    operator: '赵六'
  },
  {
    id: 'BD-005',
    name: '李白眼镜深圳南山店,\n李白眼镜福田店',
    areas: ['广东省-深圳市-南山区', '广东省-深圳市-福田区'],
    updateTime: '2025-06-05 15:50',
    operator: '钱七'
  }
]

// 所有门店列表数据，用于选择置顶门店
const allStores = [
  { id: 'S001', name: '李白眼镜（朝阳店）', selected: false, area: '北京市-朝阳区' },
  { id: 'S002', name: '李白眼镜（海淀旗舰店）', selected: false, area: '北京市-海淀区' },
  { id: 'S003', name: '李白眼镜（西城店）', selected: false, area: '北京市-西城区' },
  { id: 'S004', name: '李白眼镜（东城店）', selected: false, area: '北京市-东城区' },
  { id: 'S005', name: '李白眼镜（黄浦店）', selected: false, area: '上海市-黄浦区' },
  { id: 'S006', name: '李白眼镜（徐汇店）', selected: false, area: '上海市-徐汇区' },
  { id: 'S007', name: '李白眼镜（浦东店）', selected: false, area: '上海市-浦东新区' },
  { id: 'S008', name: '李白眼镜（广州天河店）', selected: false, area: '广东省-广州市-天河区' },
  { id: 'S009', name: '李白眼镜（越秀店）', selected: false, area: '广东省-广州市-越秀区' },
  { id: 'S010', name: '李白眼镜（深圳南山店）', selected: false, area: '广东省-深圳市-南山区' },
  { id: 'S011', name: '李白眼镜（福田店）', selected: false, area: '广东省-深圳市-福田区' },
  { id: 'S012', name: '李白眼镜（罗湖店）', selected: false, area: '广东省-深圳市-罗湖区' }
]

// 生成门店列表数据
let storeList = [
  {
    id: 'BD-001',
    name: '李白眼镜旗舰店',
    areas: ['北京市-朝阳区'],
    phone: '010-12345678',
    address: '北京市朝阳区建国路88号',
    type: '李白智能版合作门店',
    typeIcon: true,
    level: '金牌门店',
    isOnline: 1
  },
  {
    id: 'BD-002',
    name: '李白眼镜北京王府井店',
    areas: ['北京市-东城区'],
    phone: '010-23456789',
    address: '北京市东城区王府井大街100号',
    type: '李白智能版合作门店',
    typeIcon: true,
    level: '金牌门店',
    isOnline: 1
  },
  {
    id: 'BD-003',
    name: '李白眼镜上海南京路店',
    areas: ['上海市-黄浦区'],
    phone: '021-34567890',
    address: '上海市黄浦区南京东路50号',
    type: '李白专业版合作门店',
    typeIcon: false,
    level: '银牌门店',
    isOnline: 1
  },
  {
    id: 'BD-004',
    name: '李白眼镜广州天河店',
    areas: ['广东省-广州市-天河区'],
    phone: '020-45678901',
    address: '广州市天河区天河路123号',
    type: '李白智能版合作门店',
    typeIcon: true,
    level: '金牌门店',
    isOnline: 0
  },
  {
    id: 'BD-005',
    name: '李白眼镜深圳南山店',
    areas: ['广东省-深圳市-南山区'],
    phone: '0755-56789012',
    address: '深圳市南山区科技园路456号',
    type: '李白专业版合作门店',
    typeIcon: false,
    level: '银牌门店',
    isOnline: 1
  }
]

// 省份数据
const provinces = [
  { value: '北京市', label: '北京市' },
  { value: '上海市', label: '上海市' },
  { value: '广东省', label: '广东省' }
]

// 城市数据
const cities = {
  北京市: [
    { value: '朝阳区', label: '朝阳区' },
    { value: '海淀区', label: '海淀区' },
    { value: '东城区', label: '东城区' },
    { value: '西城区', label: '西城区' }
  ],
  上海市: [
    { value: '黄浦区', label: '黄浦区' },
    { value: '徐汇区', label: '徐汇区' },
    { value: '浦东新区', label: '浦东新区' }
  ],
  广东省: [
    { value: '广州市', label: '广州市' },
    { value: '深圳市', label: '深圳市' }
  ]
}

// 区县数据
const districts = {
  广州市: [
    { value: '天河区', label: '天河区' },
    { value: '越秀区', label: '越秀区' },
    { value: '海珠区', label: '海珠区' }
  ],
  深圳市: [
    { value: '福田区', label: '福田区' },
    { value: '罗湖区', label: '罗湖区' },
    { value: '南山区', label: '南山区' }
  ]
}

// 门店草稿和详情数据
let storeFormDrafts = []
let storeDetails = [
  {
    id: 'STORE-001',
    contactName: '张三',
    contactPhone: '***********',
    bannerImages: ['https://picsum.photos/800/400?random=1', 'https://picsum.photos/800/400?random=2'],
    name: '李白眼镜旗舰店',
    tags: '高端,旗舰店,新门店',
    openTime: '09:00',
    closeTime: '21:00',
    province: '北京市',
    city: '朝阳区',
    district: '',
    address: '北京市朝阳区建国路88号',
    phone: '010-12345678',
    selectedEquipment: [1, 3, 5],
    teamMembers: [
      {
        name: '李白',
        description: '店长，拥有10年验光经验',
        image: 'https://picsum.photos/200/200?random=10'
      },
      {
        name: '杜甫',
        description: '资深验光师，专注青少年近视防控',
        image: 'https://picsum.photos/200/200?random=11'
      }
    ],
    environmentImages: ['https://picsum.photos/800/400?random=3', 'https://picsum.photos/800/400?random=4'],
    storeInfo: '李白眼镜旗舰店是集验光、配镜、眼健康管理为一体的综合性眼镜店，拥有先进的验光设备和专业的验光团队。',
    authorizationImage: 'https://picsum.photos/800/600?random=5',
    relatedActivity: 'activity1',
    relatedProduct: 'product2',
    status: 1 // 1: 已提交, 0: 草稿
  }
]

// 定义接口数组
export default [
  // 添加门店置顶
  {
    url: '/mock/store/top',
    method: 'post',
    response: (req) => {
      const { stores } = req.body

      if (!stores || !Array.isArray(stores) || stores.length === 0) {
        return {
          code: 400,
          message: '请选择要置顶的门店'
        }
      }

      // 检查数量限制
      if (stores.length > 3) {
        return {
          code: 400,
          message: '最多只能置顶3家门店'
        }
      }

      // 存储新的置顶门店
      const now = new Date()
        .toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
        .replace(/\//g, '-')

      const newTops = stores.map((store) => {
        // 生成新ID
        const newId = `BD-00${nextId++}`

        return {
          id: newId,
          name: store.name,
          areas: store.area ? [store.area] : [],
          updateTime: now,
          operator: '管理员'
        }
      })

      // 添加到列表前面
      storeTopList = [...newTops, ...storeTopList]

      return {
        code: 200,
        data: newTops,
        message: '添加成功'
      }
    }
  },

  // 更新门店置顶
  {
    url: new RegExp('/api/store/top/(.*)'),
    method: 'put',
    response: (req) => {
      const id = req.url.match(/\/api\/store\/top\/(.*)/)[1]
      const { name, areas } = req.body

      // 查找要更新的记录索引
      const index = storeTopList.findIndex((item) => item.id === id)

      if (index !== -1) {
        // 更新记录
        storeTopList[index] = {
          ...storeTopList[index],
          name,
          areas,
          updateTime: new Date()
            .toLocaleString('zh-CN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit'
            })
            .replace(/\//g, '-'),
          operator: '管理员'
        }

        return {
          code: 200,
          data: storeTopList[index],
          message: '更新成功'
        }
      }

      return {
        code: 404,
        message: '未找到要更新的记录'
      }
    }
  },

  // 删除门店置顶
  {
    url: new RegExp('/api/store/top/(.*)'),
    method: 'delete',
    response: (req) => {
      const id = req.url.match(/\/api\/store\/top\/(.*)/)[1]

      // 查找要删除的记录索引
      const index = storeTopList.findIndex((item) => item.id === id)

      if (index !== -1) {
        // 删除记录
        storeTopList.splice(index, 1)

        return {
          code: 200,
          message: '删除成功'
        }
      }

      return {
        code: 404,
        message: '未找到要删除的记录'
      }
    }
  },
  // 获取门店置顶列表
  {
    url: '/mock/stores/top',
    method: 'get',
    response: (req) => {
      const { page = 1, pageSize = 10, name, province, city, district } = req.query

      let filteredList = [...storeTopList]

      // 根据门店名称筛选
      if (name) {
        filteredList = filteredList.filter((item) => item.name.includes(name))
      }

      // 根据区域筛选
      if (province || city || district) {
        filteredList = filteredList.filter((item) => {
          return item.areas.some((area) => {
            if (district) {
              return area.includes(district)
            }
            if (city) {
              return area.includes(city)
            }
            if (province) {
              return area.includes(province)
            }
            return true
          })
        })
      }

      // 分页
      const startIndex = (page - 1) * pageSize
      const endIndex = startIndex + parseInt(pageSize)
      const pageData = filteredList.slice(startIndex, endIndex)

      return {
        code: 200,
        data: {
          list: pageData,
          total: filteredList.length,
          page: parseInt(page),
          pageSize: parseInt(pageSize)
        },
        message: '获取成功'
      }
    }
  },

  // 获取门店列表
  {
    url: '/mock/stores',
    method: 'get',
    response: ({ query }) => {
      const { page = 1, pageSize = 10, name, province, city, district, type, level, isOnline } = query

      let filteredList = [...storeList]

      // 根据查询条件过滤
      if (name) {
        filteredList = filteredList.filter((item) => item.name.includes(name))
      }

      if (province) {
        filteredList = filteredList.filter((item) => {
          const areas = item.areas || []
          return areas.some((area) => area.startsWith(province))
        })
      }

      if (city) {
        filteredList = filteredList.filter((item) => {
          const areas = item.areas || []
          return areas.some((area) => area.includes(city))
        })
      }

      if (district) {
        filteredList = filteredList.filter((item) => {
          const areas = item.areas || []
          return areas.some((area) => area.includes(district))
        })
      }

      if (type) {
        filteredList = filteredList.filter((item) => item.type === type)
      }

      if (level) {
        filteredList = filteredList.filter((item) => item.level === level)
      }

      if (isOnline !== undefined && isOnline !== '') {
        filteredList = filteredList.filter((item) => item.isOnline === Number(isOnline))
      }

      // 分页处理
      const startIndex = (page - 1) * pageSize
      const endIndex = startIndex + pageSize
      const pagedList = filteredList.slice(startIndex, endIndex)

      return {
        code: 200,
        message: 'success',
        data: {
          list: pagedList,
          total: filteredList.length
        }
      }
    }
  },

  // 获取省份列表
  {
    url: '/mock/provinces',
    method: 'get',
    response: () => {
      return {
        code: 200,
        data: provinces,
        message: '获取成功'
      }
    }
  },

  // 获取城市列表
  {
    url: '/mock/cities',
    method: 'get',
    response: (req) => {
      const { province } = req.query
      return {
        code: 200,
        data: cities[province] || [],
        message: '获取成功'
      }
    }
  },

  // 获取区县列表
  {
    url: '/mock/districts',
    method: 'get',
    response: (req) => {
      const { city } = req.query
      return {
        code: 200,
        data: districts[city] || [],
        message: '获取成功'
      }
    }
  },

  // 获取门店审核列表
  {
    url: '/mock/stores/audit',
    method: 'get',
    response: (req) => {
      const { page = 1, pageSize = 10, name, province, city, district, contactName, contactPhone, businessName, auditStatus } = req.query

      let filteredList = [...storeAuditList]

      // 根据门店名称筛选
      if (name) {
        filteredList = filteredList.filter((item) => item.name.includes(name))
      }

      // 根据区域筛选
      if (province || city || district) {
        filteredList = filteredList.filter((item) => {
          return item.areas.some((area) => {
            if (district) {
              return area.includes(district)
            }
            if (city) {
              return area.includes(city)
            }
            if (province) {
              return area.includes(province)
            }
            return true
          })
        })
      }

      // 根据联系人姓名筛选
      if (contactName) {
        filteredList = filteredList.filter((item) => item.contactName.includes(contactName))
      }

      // 根据联系人电话筛选
      if (contactPhone) {
        filteredList = filteredList.filter((item) => item.contactPhone.includes(contactPhone))
      }

      // 根据对接服务商/业务员筛选
      if (businessName) {
        filteredList = filteredList.filter((item) => item.businessName.includes(businessName))
      }

      // 根据审核状态筛选 (如果不为空才筛选)
      if (auditStatus) {
        filteredList = filteredList.filter((item) => item.auditStatus === auditStatus)
      }

      // 分页
      const startIndex = (page - 1) * pageSize
      const endIndex = startIndex + parseInt(pageSize)
      const pageData = filteredList.slice(startIndex, endIndex)

      return {
        code: 200,
        data: {
          list: pageData,
          total: filteredList.length,
          page: parseInt(page),
          pageSize: parseInt(pageSize)
        },
        message: '获取成功'
      }
    }
  },

  // 审核通过
  {
    url: new RegExp('/api/store/audit/approve/(.*)'),
    method: 'put',
    response: (req) => {
      const id = req.url.match(/\/api\/store\/audit\/approve\/(.*)/)[1]
      const index = storeAuditList.findIndex((item) => item.id === id)

      if (index === -1) {
        return {
          code: 404,
          message: '未找到对应门店'
        }
      }

      // 只允许对"待审批"状态的门店进行审批
      if (storeAuditList[index].auditStatus !== '待审批') {
        return {
          code: 400,
          message: '只能审批待审批状态的门店'
        }
      }

      // 更新审核状态
      storeAuditList[index].auditStatus = '审批通过'
      storeAuditList[index].auditTime = new Date()
        .toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
        .replace(/\//g, '-')
      storeAuditList[index].auditor = '管理员'

      return {
        code: 200,
        data: storeAuditList[index],
        message: '审批通过成功'
      }
    }
  },

  // 审核驳回
  {
    url: new RegExp('/api/store/audit/reject/(.*)'),
    method: 'put',
    response: (req) => {
      const id = req.url.match(/\/api\/store\/audit\/reject\/(.*)/)[1]
      const index = storeAuditList.findIndex((item) => item.id === id)

      if (index === -1) {
        return {
          code: 404,
          message: '未找到对应门店'
        }
      }

      // 只允许对"待审批"状态的门店进行审批
      if (storeAuditList[index].auditStatus !== '待审批') {
        return {
          code: 400,
          message: '只能审批待审批状态的门店'
        }
      }

      // 更新审核状态
      storeAuditList[index].auditStatus = '审批驳回'
      storeAuditList[index].auditTime = new Date()
        .toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
        .replace(/\//g, '-')
      storeAuditList[index].auditor = '管理员'

      return {
        code: 200,
        data: storeAuditList[index],
        message: '审批驳回成功'
      }
    }
  },

  // 获取门店审核详情
  {
    url: new RegExp('/api/store/audit/(.*)'),
    method: 'get',
    response: (req) => {
      const id = req.url.match(/\/api\/store\/audit\/(.*)/)[1]
      const store = storeAuditList.find((item) => item.id === id)

      if (!store) {
        return {
          code: 404,
          message: '未找到对应门店'
        }
      }

      return {
        code: 200,
        data: store,
        message: '获取成功'
      }
    }
  },

  // 获取门店类型标签列表
  {
    url: '/mock/stores/tag',
    method: 'get',
    response: (req) => {
      const { page = 1, pageSize = 10, name, province, city, district, certification, level, type } = req.query

      let filteredList = [...storeTagList]

      // 根据门店名称筛选
      if (name) {
        filteredList = filteredList.filter((item) => item.name.includes(name))
      }

      // 根据区域筛选
      if (province || city || district) {
        filteredList = filteredList.filter((item) => {
          return item.areas.some((area) => {
            if (district) {
              return area.includes(district)
            }
            if (city) {
              return area.includes(city)
            }
            if (province) {
              return area.includes(province)
            }
            return true
          })
        })
      }

      // 根据门店认证筛选
      if (certification) {
        filteredList = filteredList.filter((item) => item.certification === certification)
      }

      // 根据门店星级筛选
      if (level) {
        filteredList = filteredList.filter((item) => item.level === Number(level))
      }

      // 根据门店类型筛选
      if (type) {
        filteredList = filteredList.filter((item) => item.type === type)
      }

      // 分页
      const startIndex = (page - 1) * pageSize
      const endIndex = startIndex + parseInt(pageSize)
      const pageData = filteredList.slice(startIndex, endIndex)

      return {
        code: 200,
        data: {
          list: pageData,
          total: filteredList.length,
          page: parseInt(page),
          pageSize: parseInt(pageSize)
        },
        message: '获取成功'
      }
    }
  },

  // 获取门店认证类型列表
  {
    url: '/mock/store/certifications',
    method: 'get',
    response: () => {
      return {
        code: 200,
        data: certifications,
        message: '获取成功'
      }
    }
  },

  // 获取门店标签详情
  {
    url: new RegExp('/api/store/tag/(.*)'),
    method: 'get',
    response: (req) => {
      const id = req.url.match(/\/api\/store\/tag\/(.*)/)[1]
      const store = storeTagList.find((item) => item.id === id)

      if (!store) {
        return {
          code: 404,
          message: '未找到对应门店'
        }
      }

      return {
        code: 200,
        data: store,
        message: '获取成功'
      }
    }
  },

  // 获取可供选择的门店列表
  {
    url: '/mock/stores/available',
    method: 'get',
    response: (req) => {
      const { name, province, city, district } = req.query

      let filteredStores = [...allStores]

      // 根据门店名称筛选
      if (name) {
        filteredStores = filteredStores.filter((store) => store.name.includes(name))
      }

      // 根据区域筛选
      if (province || city || district) {
        filteredStores = filteredStores.filter((store) => {
          const area = store.area || ''
          if (district && !area.includes(district)) {
            return false
          }
          if (city && !area.includes(city)) {
            return false
          }
          if (province && !area.includes(province)) {
            return false
          }
          return true
        })
      }

      return {
        code: 200,
        data: filteredStores,
        message: '获取成功'
      }
    }
  },

  // 添加新门店
  {
    url: '/mock/store',
    method: 'post',
    response: ({ body }) => {
      const newId = `BD-${String(storeList.length + 1).padStart(3, '0')}`

      // 创建新门店数据
      const newStore = {
        id: newId,
        name: body.name,
        contactName: body.contactName,
        contactPhone: body.contactPhone,
        phone: body.phone,
        address: body.address,
        areas: [body.area],
        type: body.type || '李白专业版合作门店',
        typeIcon: false,
        level: body.level || '银牌门店',
        isOnline: body.status || 0,
        bannerUrls: body.bannerImages || [],
        tags: body.tags ? body.tags.split(',').map((tag) => tag.trim()) : [],
        businessHours: body.businessHours || ''
      }

      // 添加到列表
      storeList.push(newStore)

      return {
        code: 200,
        message: '新增门店成功',
        data: newStore
      }
    }
  },

  // 更新门店信息
  {
    url: '/mock/store/:id',
    method: 'put',
    response: ({ body, query }) => {
      const { id } = query
      const storeIndex = storeList.findIndex((item) => item.id === id)

      if (storeIndex === -1) {
        return {
          code: 400,
          message: '门店不存在',
          data: null
        }
      }

      // 更新门店信息
      const updatedStore = {
        ...storeList[storeIndex],
        name: body.name || storeList[storeIndex].name,
        contactName: body.contactName || storeList[storeIndex].contactName,
        contactPhone: body.contactPhone || storeList[storeIndex].contactPhone,
        phone: body.phone || storeList[storeIndex].phone,
        address: body.address || storeList[storeIndex].address,
        isOnline: body.isOnline !== undefined ? body.isOnline : storeList[storeIndex].isOnline
      }

      if (body.area) {
        updatedStore.areas = [body.area]
      }

      if (body.type) {
        updatedStore.type = body.type
      }

      if (body.level) {
        updatedStore.level = body.level
      }

      if (body.tags) {
        updatedStore.tags = body.tags.split(',').map((tag) => tag.trim())
      }

      if (body.businessHours) {
        updatedStore.businessHours = body.businessHours
      }

      if (body.bannerImages && body.bannerImages.length > 0) {
        updatedStore.bannerUrls = body.bannerImages
      }

      // 更新列表
      storeList[storeIndex] = updatedStore

      return {
        code: 200,
        message: '更新门店成功',
        data: updatedStore
      }
    }
  },

  // 删除门店
  {
    url: '/mock/store/:id',
    method: 'delete',
    response: ({ query }) => {
      const { id } = query
      const storeIndex = storeList.findIndex((item) => item.id === id)

      if (storeIndex === -1) {
        return {
          code: 400,
          message: '门店不存在',
          data: null
        }
      }

      // 从列表中移除
      storeList.splice(storeIndex, 1)

      return {
        code: 200,
        message: '删除门店成功',
        data: null
      }
    }
  },

  // 更新门店标签
  {
    url: '/mock/store/updateTag',
    method: 'post',
    response: (req) => {
      const { id, level, crown, type, tags, certifications } = req.body

      if (!id) {
        return {
          code: 400,
          message: '门店ID不能为空'
        }
      }

      // 查找门店
      const storeIndex = storeTagList.findIndex((store) => store.id === id)
      if (storeIndex === -1) {
        return {
          code: 404,
          message: '门店不存在'
        }
      }

      // 更新门店信息
      const store = storeTagList[storeIndex]

      // 更新基本信息
      store.level = parseInt(level) || store.level
      store.crown = crown || store.crown
      store.type = type || store.type

      // 更新标签
      if (tags && Array.isArray(tags)) {
        store.tags = [...tags]
      }

      // 更新认证 - 使用第一个认证作为门店认证
      if (certifications && Array.isArray(certifications) && certifications.length > 0) {
        store.certification = certifications[0]
      } else {
        store.certification = ''
      }

      console.log('更新后的门店数据:', store)

      return {
        code: 200,
        message: '更新门店标签成功',
        data: store
      }
    }
  },

  // 审核通过门店
  {
    url: '/mock/store/audit/approve/:id',
    method: 'put',
    response: ({ query, body }) => {
      const { id } = query
      const { comment } = body

      const index = storeAuditList.findIndex((store) => store.id === id)

      if (index !== -1) {
        storeAuditList[index].auditStatus = '审批通过'
        storeAuditList[index].auditTime = new Date().toLocaleString('zh-CN').replace(/\//g, '-')
        storeAuditList[index].auditor = '管理员'
        storeAuditList[index].auditComment = comment || ''

        return {
          code: 200,
          message: '审批通过成功',
          data: storeAuditList[index]
        }
      }

      return {
        code: 404,
        message: '未找到该门店',
        data: null
      }
    }
  },

  // 审核驳回门店
  {
    url: '/mock/store/audit/reject/:id',
    method: 'put',
    response: ({ query, body }) => {
      const { id } = query
      const { comment } = body

      const index = storeAuditList.findIndex((store) => store.id === id)

      if (index !== -1) {
        storeAuditList[index].auditStatus = '审批驳回'
        storeAuditList[index].auditTime = new Date().toLocaleString('zh-CN').replace(/\//g, '-')
        storeAuditList[index].auditor = '管理员'
        storeAuditList[index].auditComment = comment || ''

        return {
          code: 200,
          message: '审批驳回成功',
          data: storeAuditList[index]
        }
      }

      return {
        code: 404,
        message: '未找到该门店',
        data: null
      }
    }
  },

  // 添加新门店（提交）
  {
    url: '/mock/store/submit',
    method: 'post',
    response: ({ body }) => {
      const newId = `STORE-${String(storeDetails.length + 1).padStart(3, '0')}`

      // 创建新门店数据
      const newStore = {
        id: newId,
        ...body,
        status: 1, // 已提交状态
        createTime: new Date().toLocaleString('zh-CN').replace(/\//g, '-')
      }

      // 添加到列表
      storeDetails.push(newStore)

      return {
        code: 200,
        message: '提交门店成功',
        data: newStore
      }
    }
  },

  // 保存门店草稿
  {
    url: '/mock/store/draft',
    method: 'post',
    response: ({ body }) => {
      let draftId = body.id
      let isNew = false

      if (!draftId) {
        // 新草稿
        draftId = `DRAFT-${String(storeFormDrafts.length + 1).padStart(3, '0')}`
        isNew = true
      }

      // 创建或更新草稿数据
      const draftData = {
        id: draftId,
        ...body,
        status: 0, // 草稿状态
        updateTime: new Date().toLocaleString('zh-CN').replace(/\//g, '-')
      }

      if (isNew) {
        // 添加新草稿
        storeFormDrafts.push(draftData)
      } else {
        // 更新现有草稿
        const index = storeFormDrafts.findIndex((draft) => draft.id === draftId)
        if (index !== -1) {
          storeFormDrafts[index] = draftData
        } else {
          // 如果找不到草稿，添加为新草稿
          storeFormDrafts.push(draftData)
        }
      }

      return {
        code: 200,
        message: '保存草稿成功',
        data: draftData
      }
    }
  },

  // 获取门店草稿列表
  {
    url: '/mock/store/drafts',
    method: 'get',
    response: ({ query }) => {
      const { page = 1, pageSize = 10 } = query

      // 分页处理
      const startIndex = (page - 1) * pageSize
      const endIndex = startIndex + parseInt(pageSize)
      const pagedDrafts = storeFormDrafts.slice(startIndex, endIndex)

      return {
        code: 200,
        message: 'success',
        data: {
          list: pagedDrafts,
          total: storeFormDrafts.length
        }
      }
    }
  },

  // 获取门店详情
  {
    url: new RegExp('/mock/store/detail/(.*)'),
    method: 'get',
    response: (req) => {
      const id = req.url.match(/\/mock\/store\/detail\/(.*)/)[1]

      // 先查找正式门店
      let store = storeDetails.find((item) => item.id === id)

      // 如果没找到，查找草稿
      if (!store) {
        store = storeFormDrafts.find((item) => item.id === id)
      }

      // 如果还没找到，查找门店列表
      if (!store && id.startsWith('BD-')) {
        store = storeList.find((item) => item.id === id)

        // 如果找到了门店列表中的数据，进行格式转换
        if (store) {
          // 转换为门店详情格式
          store = {
            id: store.id,
            contactName: store.contactName || '联系人',
            contactPhone: store.contactPhone || '***********',
            bannerImages: store.bannerUrls || [],
            name: store.name,
            tags: Array.isArray(store.tags) ? store.tags.join(',') : '',
            openTime: store.businessHours ? store.businessHours.split(' - ')[0] : '09:00',
            closeTime: store.businessHours ? store.businessHours.split(' - ')[1] : '21:00',
            province: store.areas && store.areas[0] ? store.areas[0].split('-')[0] : '',
            city: store.areas && store.areas[0] && store.areas[0].split('-').length > 1 ? store.areas[0].split('-')[1] : '',
            district: store.areas && store.areas[0] && store.areas[0].split('-').length > 2 ? store.areas[0].split('-')[2] : '',
            address: store.address,
            phone: store.phone,
            selectedEquipment: [1, 2], // 默认选择一些设备
            teamMembers: [
              {
                name: '店员',
                description: '资深验光师',
                image: 'https://picsum.photos/200/200?random=20'
              }
            ],
            environmentImages: [],
            storeInfo: '门店详细介绍',
            status: store.isOnline
          }
        }
      }

      if (!store) {
        return {
          code: 404,
          message: '未找到门店信息',
          data: null
        }
      }

      return {
        code: 200,
        message: 'success',
        data: store
      }
    }
  },

  // 更新门店信息
  {
    url: new RegExp('/mock/store/update/(.*)'),
    method: 'put',
    response: (req) => {
      const id = req.url.match(/\/mock\/store\/update\/(.*)/)[1]
      const { body } = req

      // 查找并更新正式门店
      let storeIndex = storeDetails.findIndex((item) => item.id === id)

      if (storeIndex !== -1) {
        // 更新门店信息
        storeDetails[storeIndex] = {
          ...storeDetails[storeIndex],
          ...body,
          updateTime: new Date().toLocaleString('zh-CN').replace(/\//g, '-')
        }

        return {
          code: 200,
          message: '更新门店成功',
          data: storeDetails[storeIndex]
        }
      }

      // 如果正式门店中没找到，查找草稿
      let draftIndex = storeFormDrafts.findIndex((item) => item.id === id)

      if (draftIndex !== -1) {
        // 更新草稿信息
        storeFormDrafts[draftIndex] = {
          ...storeFormDrafts[draftIndex],
          ...body,
          updateTime: new Date().toLocaleString('zh-CN').replace(/\//g, '-')
        }

        return {
          code: 200,
          message: '更新草稿成功',
          data: storeFormDrafts[draftIndex]
        }
      }

      return {
        code: 404,
        message: '未找到门店信息',
        data: null
      }
    }
  },

  // 获取门店详情 (API版本)
  {
    url: new RegExp('/api/store/detail/(.*)'),
    method: 'get',
    response: (req) => {
      const id = req.url.match(/\/api\/store\/detail\/(.*)/)[1]

      // 先查找正式门店
      let store = storeDetails.find((item) => item.id === id)

      // 如果没找到，查找草稿
      if (!store) {
        store = storeFormDrafts.find((item) => item.id === id)
      }

      // 如果还没找到，查找门店列表
      if (!store && id.startsWith('BD-')) {
        store = storeList.find((item) => item.id === id)

        // 如果找到了门店列表中的数据，进行格式转换
        if (store) {
          // 转换为门店详情格式
          store = {
            id: store.id,
            contactName: store.contactName || '联系人',
            contactPhone: store.contactPhone || '***********',
            bannerImages: store.bannerUrls || [],
            name: store.name,
            tags: Array.isArray(store.tags) ? store.tags.join(',') : '',
            openTime: store.businessHours ? store.businessHours.split(' - ')[0] : '09:00',
            closeTime: store.businessHours ? store.businessHours.split(' - ')[1] : '21:00',
            province: store.areas && store.areas[0] ? store.areas[0].split('-')[0] : '',
            city: store.areas && store.areas[0] && store.areas[0].split('-').length > 1 ? store.areas[0].split('-')[1] : '',
            district: store.areas && store.areas[0] && store.areas[0].split('-').length > 2 ? store.areas[0].split('-')[2] : '',
            address: store.address,
            phone: store.phone,
            selectedEquipment: [1, 2], // 默认选择一些设备
            teamMembers: [
              {
                name: '店员',
                description: '资深验光师',
                image: 'https://picsum.photos/200/200?random=20'
              }
            ],
            environmentImages: [],
            storeInfo: '门店详细介绍',
            status: store.isOnline
          }
        }
      }

      if (!store) {
        return {
          code: 404,
          message: '未找到门店信息',
          data: null
        }
      }

      return {
        code: 200,
        message: 'success',
        data: store
      }
    }
  },

  // 更新门店信息 (API版本)
  {
    url: new RegExp('/api/store/update/(.*)'),
    method: 'put',
    response: (req) => {
      const id = req.url.match(/\/api\/store\/update\/(.*)/)[1]
      const { body } = req

      // 查找并更新正式门店
      let storeIndex = storeDetails.findIndex((item) => item.id === id)

      if (storeIndex !== -1) {
        // 更新门店信息
        storeDetails[storeIndex] = {
          ...storeDetails[storeIndex],
          ...body,
          updateTime: new Date().toLocaleString('zh-CN').replace(/\//g, '-')
        }

        return {
          code: 200,
          message: '更新门店成功',
          data: storeDetails[storeIndex]
        }
      }

      // 如果正式门店中没找到，查找草稿
      let draftIndex = storeFormDrafts.findIndex((item) => item.id === id)

      if (draftIndex !== -1) {
        // 更新草稿信息
        storeFormDrafts[draftIndex] = {
          ...storeFormDrafts[draftIndex],
          ...body,
          updateTime: new Date().toLocaleString('zh-CN').replace(/\//g, '-')
        }

        return {
          code: 200,
          message: '更新草稿成功',
          data: storeFormDrafts[draftIndex]
        }
      }

      // 如果还没找到，查找门店列表
      if (id.startsWith('BD-')) {
        const storeListIndex = storeList.findIndex((item) => item.id === id)

        if (storeListIndex !== -1) {
          // 创建一个新的门店详情记录
          const newStore = {
            id: id,
            contactName: body.contactName || storeList[storeListIndex].contactName || '联系人',
            contactPhone: body.contactPhone || storeList[storeListIndex].contactPhone || '***********',
            bannerImages: body.bannerImages || storeList[storeListIndex].bannerUrls || [],
            name: body.name || storeList[storeListIndex].name,
            tags: body.tags || (Array.isArray(storeList[storeListIndex].tags) ? storeList[storeListIndex].tags.join(',') : ''),
            openTime: body.openTime || '09:00',
            closeTime: body.closeTime || '21:00',
            province: body.province || '',
            city: body.city || '',
            district: body.district || '',
            address: body.address || storeList[storeListIndex].address || '',
            phone: body.phone || storeList[storeListIndex].phone || '',
            selectedEquipment: body.selectedEquipment || [1, 2],
            teamMembers: body.teamMembers || [
              {
                name: '店员',
                description: '资深验光师',
                image: 'https://picsum.photos/200/200?random=20'
              }
            ],
            environmentImages: body.environmentImages || [],
            storeInfo: body.storeInfo || '门店详细介绍',
            status: body.status !== undefined ? body.status : storeList[storeListIndex].isOnline,
            updateTime: new Date().toLocaleString('zh-CN').replace(/\//g, '-')
          }

          // 添加到门店详情列表
          storeDetails.push(newStore)

          // 更新门店列表中的数据
          storeList[storeListIndex] = {
            ...storeList[storeListIndex],
            name: body.name || storeList[storeListIndex].name,
            contactName: body.contactName || storeList[storeListIndex].contactName,
            contactPhone: body.contactPhone || storeList[storeListIndex].contactPhone,
            phone: body.phone || storeList[storeListIndex].phone,
            address: body.address || storeList[storeListIndex].address,
            isOnline: body.status !== undefined ? body.status : storeList[storeListIndex].isOnline
          }

          return {
            code: 200,
            message: '更新门店成功',
            data: newStore
          }
        }
      }

      return {
        code: 404,
        message: '未找到门店信息',
        data: null
      }
    }
  },

  // 删除门店标签
  {
    url: '/mock/store/tag/delete',
    method: 'post',
    response: (req) => {
      const { tagName } = req.body

      if (!tagName) {
        return {
          code: 400,
          message: '标签名称不能为空'
        }
      }

      // 从可用标签列表中删除
      const tagIndex = availableTags.indexOf(tagName)
      if (tagIndex > -1) {
        availableTags.splice(tagIndex, 1)

        // 同时从所有门店的标签中删除
        storeTagList.forEach((store) => {
          const idx = store.tags.indexOf(tagName)
          if (idx > -1) {
            store.tags.splice(idx, 1)
          }
        })

        return {
          code: 200,
          message: '删除标签成功',
          data: availableTags
        }
      } else {
        return {
          code: 404,
          message: '标签不存在'
        }
      }
    }
  },

  // 删除门店认证
  {
    url: '/mock/store/certification/delete',
    method: 'post',
    response: (req) => {
      const { certName } = req.body

      if (!certName) {
        return {
          code: 400,
          message: '认证名称不能为空'
        }
      }

      // 从可用认证列表中删除
      const certIndex = availableCertifications.indexOf(certName)
      if (certIndex > -1) {
        availableCertifications.splice(certIndex, 1)

        // 同时从所有门店的认证中删除
        storeTagList.forEach((store) => {
          if (store.certification === certName) {
            store.certification = ''
          }
        })

        return {
          code: 200,
          message: '删除认证成功',
          data: availableCertifications
        }
      } else {
        return {
          code: 404,
          message: '认证不存在'
        }
      }
    }
  }
]
