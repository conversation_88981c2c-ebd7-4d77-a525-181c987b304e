<template>
  <div>
    <div class="page-header">
      <h2>{{ isEdit ? '编辑活动' : '新增活动' }}</h2>
      <p>{{ isEdit ? '编辑活动信息' : '在系统内添加新的活动' }}</p>
    </div>

    <el-card class="form-card" shadow="never">
      <el-form :model="activityForm" ref="activityFormRef" :rules="formRules">
        <!-- 关联门店和首页类型在一行，各占一半 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">关联门店</div>
            <el-form-item prop="store" class="no-label-form-item">
              <el-select v-model="activityForm.store" placeholder="请选择关联门店" class="form-select">
                <el-option v-for="item in storeOptions.filter((item) => item.value !== '全部门店')" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="form-item">
            <div class="filter-label">首页类型</div>
            <el-form-item prop="category" class="no-label-form-item">
              <el-select v-model="activityForm.category" placeholder="请选择首页类型" class="form-select">
                <el-option v-for="item in categoryOptions.filter((item) => item.value !== '全部')" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 活动类型独占一行 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">活动类型</div>
            <el-form-item prop="type" class="no-label-form-item">
              <el-select v-model="activityForm.type" placeholder="请选择活动类型" class="form-select">
                <el-option label="促销" value="促销"></el-option>
                <el-option label="会员" value="会员"></el-option>
                <el-option label="品牌" value="品牌"></el-option>
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 活动主标题独占一行 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">活动主标题</div>
            <el-form-item prop="name" class="no-label-form-item">
              <el-input v-model="activityForm.name" placeholder="请输入活动主标题"></el-input>
            </el-form-item>
          </div>
        </div>

        <!-- 活动副标题独占一行 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">活动副标题</div>
            <el-form-item prop="theme" class="no-label-form-item">
              <el-input v-model="activityForm.theme" placeholder="请输入活动副标题"></el-input>
            </el-form-item>
          </div>
        </div>

        <!-- 是否上架和前端排序在一行 -->
        <div class="form-row">
          <div class="form-item">
            <div class="horizontal-container">
              <div class="switch-container">
                <el-form-item prop="status" class="no-label-form-item">
                  <el-switch v-model="activityForm.status" :active-value="1" :inactive-value="0" style="--el-switch-on-color: #165dff"></el-switch>
                </el-form-item>
                <span class="switch-label">是否上架</span>
              </div>
              <div class="select-container">
                <span class="select-label">前端排序(数字越小越靠前)</span>
                <el-form-item prop="sort" class="no-label-form-item">
                  <el-select v-model="activityForm.sort" placeholder="请选择排序" class="form-select-inline" style="width: 60px">
                    <el-option v-for="i in 10" :key="i" :label="i" :value="i"></el-option>
                  </el-select>
                </el-form-item>
              </div>
            </div>
          </div>
        </div>

        <!-- 活动图片 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">活动图片</div>
            <el-form-item prop="image" class="no-label-form-item">
              <div class="upload-container">
                <div v-if="previewImage" class="image-preview-wrapper">
                  <el-image :src="previewImage" fit="cover" class="preview-image" :preview-src-list="[previewImage]" :initial-index="0" preview-teleported></el-image>
                  <div class="close-button" @click="removeImage">
                    <el-icon><Close /></el-icon>
                  </div>
                </div>
                <el-upload v-else class="upload-box" drag action="#" :auto-upload="false" :show-file-list="false" :limit="1" :on-exceed="handleExceed" accept=".jpg,.jpeg,.png,.gif" :on-change="handleImageUploadChange">
                  <div class="upload-content">
                    <el-icon class="upload-icon"><UploadFilled /></el-icon>
                    <div class="upload-text">点击上传图片</div>
                    <div class="upload-tip">支持JPG、PNG格式</div>
                  </div>
                </el-upload>
              </div>
            </el-form-item>
          </div>
        </div>

        <!-- 跳转页面类型 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">跳转页面类型</div>
            <el-form-item prop="jumpType" class="no-label-form-item">
              <el-select v-model="activityForm.jumpType" placeholder="请选择跳转类型" class="form-select">
                <el-option v-for="item in jumpTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 小程序路径，仅当选择跳转小程序路径时显示 -->
        <div class="form-row" v-if="activityForm.jumpType === '跳转小程序路径'">
          <div class="form-item">
            <div class="filter-label">小程序路径</div>
            <el-form-item prop="miniappPath" class="no-label-form-item">
              <el-input v-model="activityForm.miniappPath" placeholder="请输入小程序路径"></el-input>
            </el-form-item>
          </div>
        </div>

        <!-- 活动图文描述，仅当选择跳转图文详情时显示 -->
        <div class="form-row" v-if="activityForm.jumpType === '跳转图文详情'">
          <div class="form-item">
            <div class="filter-label">活动图文描述</div>
            <el-form-item prop="content" class="no-label-form-item">
              <div class="editor-container">
                <Toolbar style="border-bottom: 1px solid #cccccc" :editor="editorRef" :defaultConfig="editorConfig" mode="default" />
                <Editor style="height: 300px; overflow-y: hidden; background-color: #ffffff" v-model="editorContent" :defaultConfig="editorConfig" mode="default" @onCreated="handleCreated" />
              </div>
            </el-form-item>
          </div>
        </div>

        <!-- 按钮区域 -->
        <div class="form-actions form-actions-left">
          <el-button @click="handleCancel" plain class="reset-button">
            <el-icon><Close /></el-icon>取消
          </el-button>
          <el-button type="primary" @click="handleSubmit" class="search-button">
            <el-icon><Check /></el-icon>保存
          </el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onBeforeUnmount, shallowRef, defineEmits, defineProps, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import '@wangeditor/editor/dist/css/style.css'

// 定义组件通信
const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false
  },
  // 编辑时的初始数据
  editData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['cancel', 'submit'])

// 店铺选项
const storeOptions = ref([
  { value: '全部门店', label: '全部门店' },
  { value: '旗舰店', label: '旗舰店' },
  { value: '北京店', label: '北京店' },
  { value: '上海店', label: '上海店' },
  { value: '广州店', label: '广州店' }
])

// 分类选项
const categoryOptions = ref([
  { value: '全部', label: '全部' },
  { value: '首页 - 促销活动', label: '首页 - 促销活动' },
  { value: '首页 - 品牌活动', label: '首页 - 品牌活动' }
])

// 跳转类型选项
const jumpTypeOptions = ref([
  { value: '跳转图文详情', label: '跳转图文详情' },
  { value: '跳转小程序路径', label: '跳转小程序路径' }
])

// 活动表单
const activityFormRef = ref(null)
const activityForm = reactive({
  id: '',
  name: '',
  theme: '',
  description: '',
  store: '',
  category: '',
  status: 1,
  image: '',
  detailImages: [],
  content: '',
  type: '',
  sort: 1,
  jumpType: '跳转图文详情',
  miniappPath: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入活动名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  store: [{ required: true, message: '请选择关联门店', trigger: 'change' }],
  category: [{ required: true, message: '请选择首页类型', trigger: 'change' }],
  type: [{ required: true, message: '请选择活动类型', trigger: 'change' }],
  description: [{ max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }],
  jumpType: [{ required: true, message: '请选择跳转类型', trigger: 'change' }],
  miniappPath: [
    {
      required: false,
      message: '请输入小程序路径',
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (activityForm.jumpType === '跳转小程序路径' && !value) {
          callback(new Error('请输入小程序路径'))
        } else {
          callback()
        }
      }
    }
  ]
}

// wangEditor 相关配置
const editorRef = shallowRef()
const editorContent = ref('')
const editorConfig = {
  placeholder: '请输入内容...',
  MENU_CONF: {}
}
const previewImage = ref('')

// 编辑器创建完成时的回调
const handleCreated = (editor) => {
  editorRef.value = editor
}

// 组件销毁前，销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor === null) return
  editor.destroy()
})

// 图片上传处理
const handleImageUploadChange = (file) => {
  // 检查文件格式
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
  const isValidType = allowedTypes.includes(file.raw.type)

  if (!isValidType) {
    ElMessage.error('只支持JPG、PNG、JPEG、GIF格式的图片!')
    return
  }

  // 检查文件大小（2MB以内）
  const isLt2M = file.raw.size / 1024 / 1024 < 2
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return
  }

  // 生成预览URL
  const fileUrl = URL.createObjectURL(file.raw)
  previewImage.value = fileUrl
  activityForm.image = fileUrl
}

// 处理图片超出限制
const handleExceed = () => {
  ElMessage.warning('最多上传1张图片')
}

// 移除图片
const removeImage = () => {
  ElMessageBox.confirm('确定要删除这张图片吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      previewImage.value = ''
      activityForm.image = ''
      ElMessage.success('图片已删除')
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 取消按钮处理
const handleCancel = () => {
  emit('cancel')
}

// 提交表单
const handleSubmit = () => {
  activityFormRef.value.validate((valid) => {
    if (valid) {
      // 检查图片是否上传
      if (!activityForm.image && !previewImage.value) {
        ElMessage.warning('请上传活动图片')
        return false
      }

      // 检查图文详情
      if (activityForm.jumpType === '跳转图文详情' && !editorContent.value) {
        ElMessage.warning('请输入活动图文描述')
        return false
      }

      // 构建表单数据
      const formData = {
        ...activityForm,
        content: activityForm.jumpType === '跳转图文详情' ? editorContent.value : '',
        image: previewImage.value || activityForm.image
      }

      // 在编辑模式下，确保ID存在
      if (props.isEdit && !formData.id && props.editData.id) {
        formData.id = props.editData.id
      }

      console.log('提交的表单数据:', formData)

      // 提交表单
      emit('submit', formData)
    } else {
      ElMessage.warning('请填写必填项')
      return false
    }
  })
}

// 重置表单
const resetForm = () => {
  // 重置表单数据
  Object.assign(activityForm, {
    id: '',
    name: '',
    theme: '',
    description: '',
    store: '',
    category: '',
    status: 1,
    image: '',
    detailImages: [],
    content: '',
    type: '',
    sort: 1,
    jumpType: '跳转图文详情',
    miniappPath: ''
  })

  // 重置其他状态
  previewImage.value = ''
  editorContent.value = ''
}

// 初始化表单数据
const initFormData = () => {
  if (props.isEdit && props.editData && props.editData.id) {
    console.log('初始化编辑数据:', props.editData)

    const { editData } = props

    // 重置表单先
    resetForm()

    // 填充编辑数据
    Object.keys(editData).forEach((key) => {
      if (key in activityForm) {
        activityForm[key] = editData[key]
      }
    })

    // 处理图片预览
    if (editData.image) {
      previewImage.value = editData.image
      activityForm.image = editData.image
    }

    // 处理富文本内容
    if (editData.content) {
      editorContent.value = editData.content
    }

    // 确保ID正确设置
    if (editData.id) {
      activityForm.id = editData.id
    }
  } else {
    // 新增模式，重置表单
    resetForm()
  }
}

// 监听editData变化，重新初始化表单
watch(
  () => props.editData,
  (newVal) => {
    if (newVal && Object.keys(newVal).length > 0) {
      initFormData()
    }
  },
  { deep: true, immediate: true }
)

// 监听isEdit变化
watch(
  () => props.isEdit,
  (newVal) => {
    if (!newVal) {
      // 切换到新增模式时重置表单
      resetForm()
    }
  }
)

// 组件挂载时初始化
onMounted(() => {
  initFormData()
})
</script>

<style scoped lang="scss">
.page-header {
  margin-bottom: 24px;

  h2 {
    font-size: 24px;
    font-weight: 600;
    color: #1d2129;
    margin-bottom: 8px;
  }

  p {
    font-size: 14px;
    color: #86909c;
  }
}

.form-card {
  margin-bottom: 24px;
  border-radius: 8px;

  :deep(.el-card__body) {
    padding: 20px;
  }
}

.form-row {
  margin-bottom: 20px;
  display: flex;
  gap: 20px;
}

.form-item {
  flex: 1;
}

.filter-label {
  font-size: 14px;
  color: #86909c;
  margin-bottom: 4px;
}

.no-label-form-item {
  margin-bottom: 0;
}

.form-select {
  width: 100%;
}

.horizontal-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 40px;
}

.switch-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.switch-label {
  font-size: 14px;
  color: #606266;
}

.select-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.select-label {
  font-size: 14px;
  color: #606266;
}

.form-select-inline {
  width: 120px;
}

.upload-container {
  width: 100%;
}

.image-preview-wrapper {
  width: 192px;
  height: 108px; /* 16:9 比例，60%尺寸 */
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.close-button {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 20px;
  height: 20px;
  background-color: #f53f3f; /* 红色背景 */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease; /* 只保留背景色过渡 */

  &:hover {
    background-color: #d93026; /* 悬停时稍微深一点的红色 */
  }

  .el-icon {
    color: white;
    font-size: 12px;
  }
}

.upload-box {
  width: 192px;
  height: 108px; /* 16:9 比例，60%尺寸 */
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-bottom: 10px; /* 与预览图片保持一致的下边距 */
  border: 2px dashed #d0d7de; /* 虚线边框 */
  transition: border-color 0.3s ease;

  &:hover {
    border-color: #165dff; /* 悬停时蓝色边框 */
  }

  :deep(.el-upload-dragger) {
    border: none;
    background-color: transparent;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    cursor: pointer;
  }
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.upload-icon {
  font-size: 36px;
  color: #c0c4cc;
  margin-bottom: 12px;
}

.upload-text {
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
  line-height: 1.4;

  em {
    color: #165dff;
    font-style: normal;
  }
}

.upload-tip {
  color: #9ca3af;
  font-size: 12px;
  line-height: 1.4;
  margin-top: 4px;
}

.editor-container {
  border: 1px solid #ccc;
  border-radius: 6px;
  overflow: hidden;

  // 设置富文本编辑器内容的字体大小
  :deep(.w-e-text-container [data-slate-editor] p) {
    font-size: 18px;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0;
  margin-top: 30px;

  &.form-actions-left {
    justify-content: flex-start;
  }

  .el-button {
    height: 40px;
    padding: 8px 16px;
    font-size: 16px;
    display: flex;
    align-items: center;
    border: none;

    &:not(:first-child) {
      margin-left: 12px;
    }

    .el-icon {
      margin-right: 4px;
    }
  }

  .reset-button {
    background-color: #f2f3f5;
    color: #606266;

    &:hover {
      background-color: #e5e7eb;
      border-color: transparent;
      color: #606266;
    }
  }

  .search-button {
    background-color: #165dff;
    &:hover {
      background-color: $primary-hover !important;
    }
  }
}
</style>
