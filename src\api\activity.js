import request from '@/utils/request'

// 获取活动分类列表
export function getActivityCategories(params) {
  return request({
    url: '/api/activity/categories',
    method: 'get',
    params
  })
}

// 获取活动分类树结构
export function getActivityCategoryTree() {
  return request({
    url: '/mock/activity/category/tree',
    method: 'get'
  })
}

// 添加活动分类
export function addActivityCategory(data) {
  return request({
    url: '/mock/activity/category',
    method: 'post',
    data
  })
}

// 更新活动分类
export function updateActivityCategory(id, data) {
  return request({
    url: `/mock/activity/category/${id}`,
    method: 'put',
    data
  })
}

// 删除活动分类
export function deleteActivityCategory(id) {
  return request({
    url: `/mock/activity/category/${id}`,
    method: 'delete'
  })
}

// 获取活动列表
export function getActivityList(params) {
  return request({
    url: '/mock/activities',
    method: 'get',
    params
  })
}

// 获取活动详情
export function getActivityDetail(id) {
  return request({
    url: `/api/activities/${id}`,
    method: 'get'
  })
}

// 添加活动
export function addActivity(data) {
  return request({
    url: '/api/activities',
    method: 'post',
    data
  })
}

// 更新活动
export function updateActivity(data) {
  return request({
    url: `/api/activities/${data.id}`,
    method: 'put',
    data
  })
}

// 删除活动
export function deleteActivity(id) {
  return request({
    url: `/api/activities/${id}`,
    method: 'delete'
  })
}

// 切换活动状态
export function toggleActivityStatus(id, status) {
  return request({
    url: `/api/activities/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 获取门店列表（用于活动模块）
export function getStores() {
  return request({
    url: '/mock/activity/stores',
    method: 'get'
  })
}
