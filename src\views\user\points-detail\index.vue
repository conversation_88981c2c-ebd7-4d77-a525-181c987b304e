<template>
  <div class="user-points-detail-page">
    <div class="page-header">
      <h2>用户积分明细</h2>
      <p>管理系统内所有用户积分明细信息</p>
      <el-button class="back-button btn-query" type="primary" @click="handleBack">
        <el-icon><ArrowLeft /></el-icon>
        返回用户列表
      </el-button>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <div class="search-inputs">
          <!-- 时间范围 -->
          <div class="search-item">
            <div class="search-label">时间范围</div>
            <el-date-picker v-model="searchForm.dateRange" type="daterange" range-separator="-" start-placeholder="年/月/日" end-placeholder="年/月/日" format="YYYY/MM/DD" value-format="YYYY-MM-DD" clearable />
          </div>
          <!-- 积分类型 -->
          <div class="search-item">
            <div class="search-label">积分类型</div>
            <el-select v-model="searchForm.pointsType" placeholder="全部" clearable>
              <el-option label="全部" value="全部" />
              <el-option v-for="type in pointsTypeOptions" :key="type" :label="type" :value="type" />
            </el-select>
          </div>
        </div>
        <div class="buttons-row">
          <div class="search-actions">
            <el-button class="btn-reset" @click="resetSearch" plain :disabled="isResetDisabled">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
            <el-button class="btn-query" type="primary" @click="searchPointsDetail">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 积分明细表格 -->
    <el-card class="points-detail-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">积分明细列表</span>
        </div>
      </template>
      <div class="table-container">
        <!-- 表头 -->
        <div class="table-header">
          <div class="column user-column">微信昵称</div>
          <div class="column type-column">积分类型</div>
          <div class="column time-column">积分更新时间</div>
          <div class="column phone-column">手机号</div>
          <div class="column points-add-column">增减积分数量</div>
          <div class="column points-total-column">剩余积分数量</div>
        </div>
        <!-- 表格内容 -->
        <div class="table-body" v-loading="loading">
          <div v-for="(item, index) in pointsDetailList" :key="index" class="table-row">
            <div class="column user-column">{{ item.wechatName }}</div>
            <div class="column type-column">{{ item.pointsType }}</div>
            <div class="column time-column">{{ item.updateTime }}</div>
            <div class="column phone-column">{{ item.phone }}</div>
            <div class="column points-add-column" :class="{ positive: item.addPoints > 0, negative: item.addPoints < 0 }">
              {{ item.addPoints > 0 ? '+' + item.addPoints : item.addPoints }}
            </div>
            <div class="column points-total-column">{{ item.totalPoints }}</div>
          </div>
        </div>
        <!-- 分页 -->
        <div class="pagination">
          <div class="pagination-info">显示 {{ (pagination.page - 1) * pagination.pageSize + 1 }}-{{ Math.min(pagination.page * pagination.pageSize, pagination.total) }} 条，共 {{ pagination.total }} 条</div>
          <el-pagination background layout="prev, pager, next" :total="pagination.total" :page-size="pagination.pageSize" :current-page="pagination.page" @current-change="handlePageChange" />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, defineProps, defineEmits } from 'vue'
import { getUserPointsDetail } from '@/api/user'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Refresh, Search } from '@element-plus/icons-vue'

// 定义组件接收的props
const props = defineProps({
  userId: {
    type: [Number, String],
    default: null
  }
})

// 定义组件发出的事件
const emit = defineEmits(['back'])

// 返回用户列表
const handleBack = () => {
  emit('back')
}

// 搜索表单
const searchForm = reactive({
  dateRange: [], // [startDate, endDate]
  pointsType: '全部'
})

// 积分类型选项
const pointsTypeOptions = ['注册获得积分', '邀请好友获得积分', '积分商城兑换耗积分', '购买商品获得积分', '生日奖励积分', '活动奖励积分', '年度会员积分', '评价获得积分', '完善资料获得积分']

// 积分明细列表数据
const pointsDetailList = ref([])
const loading = ref(false)
const pagination = reactive({
  page: 1,
  pageSize: 5,
  total: 0
})

// 重置按钮状态
const isResetDisabled = ref(false)

// 获取积分明细列表
const fetchPointsDetail = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      userId: props.userId // 添加用户ID筛选
    }

    // 添加日期范围过滤条件
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0]
      params.endDate = searchForm.dateRange[1]
    }

    // 添加积分类型过滤条件
    if (searchForm.pointsType && searchForm.pointsType !== '全部') {
      params.pointsType = searchForm.pointsType
    }

    const res = await getUserPointsDetail(params)
    pointsDetailList.value = res.data
    pagination.total = res.total
  } catch (error) {
    console.error('获取积分明细列表失败:', error)
    ElMessage.error('获取积分明细列表失败')
  } finally {
    loading.value = false
  }
}

// 页码变化处理
const handlePageChange = (page) => {
  pagination.page = page
  fetchPointsDetail()
}

// 搜索积分明细
const searchPointsDetail = () => {
  pagination.page = 1
  fetchPointsDetail()
}

// 重置搜索条件
const resetSearch = () => {
  isResetDisabled.value = false
  searchForm.dateRange = []
  searchForm.pointsType = '全部'
  pagination.page = 1
  fetchPointsDetail()

  // 强制移除焦点
  document.activeElement.blur()
}

// 页面加载时获取数据
onMounted(() => {
  fetchPointsDetail()

  // 添加全局点击事件，确保重置按钮在任何点击后都能恢复正常
  document.addEventListener('click', () => {
    isResetDisabled.value = false
  })
})

// 组件卸载时移除事件监听
onBeforeUnmount(() => {
  document.removeEventListener('click', () => {
    isResetDisabled.value = false
  })
})
</script>

<style lang="scss" scoped>
@use 'sass:math';
@use '@/assets/styles/variables.scss' as *;

.user-points-detail-page {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;

  .page-header {
    margin-bottom: $card-spacing;
    position: relative;

    h2 {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 8px;
      color: $text-color;
    }

    p {
      font-size: 14px;
      color: $info-color;
    }

    .back-button {
      position: absolute;
      top: 0;
      right: 0;
      height: $btn-height;
      padding: $btn-padding;
      font-size: $btn-font-size;
      border: none;

      .el-icon {
        margin-right: $btn-icon-margin;
      }

      &:hover {
        border: none !important;
        outline: none !important;
        box-shadow: none !important;
        background-color: $primary-hover !important;
      }
    }
  }

  // 搜索卡片
  .search-card {
    margin-bottom: $card-spacing;
    border-radius: $border-radius;

    :deep(.el-card__body) {
      padding: $search-padding;
    }

    .search-form {
      display: flex;
      flex-direction: column;
    }

    .search-inputs {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      justify-content: space-between;

      .search-item {
        display: flex;
        flex-direction: column;
        width: calc(50% - #{math.div($search-spacing-horizontal, 2)});
        min-width: 240px;

        .search-label {
          font-size: $search-label-size;
          color: $search-label-color;
          margin-bottom: $search-label-margin;
        }

        :deep(.el-input__wrapper) {
          height: $input-height;
        }

        :deep(.el-date-editor.el-input) {
          width: 100%;
        }

        :deep(.el-date-editor--daterange) {
          width: 100%;
        }
      }
    }

    .buttons-row {
      display: flex;
      justify-content: flex-end;
      margin-top: $search-spacing-vertical;
    }

    .search-actions {
      display: flex;

      .el-button {
        height: $btn-height;
        padding: $btn-padding;
        font-size: $btn-font-size;
        border: none;

        .el-icon {
          margin-right: $btn-icon-margin;
        }

        &:not(:first-child) {
          margin-left: $btn-margin;
        }
      }

      :deep(.el-button:hover) {
        border: none !important;
        outline: none !important;
        box-shadow: none !important;
      }

      :deep(.el-button--primary:hover) {
        background-color: $primary-hover !important;
      }
    }
  }

  // 积分明细卡片
  .points-detail-card {
    margin-bottom: $card-spacing;
    border-radius: $border-radius;

    :deep(.el-card__body) {
      padding: 0;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: $text-color;
      }
    }

    .table-container {
      width: 100%;
      overflow-x: auto;
      margin-top: 0;
    }

    // 表格头部
    .table-header {
      display: flex;
      padding: 0 16px;
      border-radius: 6px;
      height: 50px;
      align-items: center;
      font-weight: 600;
      color: #89929e;
      font-size: 14px;
      margin-top: 0;
    }

    // 表格内容
    .table-body {
      .table-row {
        display: flex;
        padding: 16px;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background-color: #f9fafc;
        }
      }
    }

    .column {
      display: flex;
      align-items: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &.user-column {
        flex: 1;
        min-width: 120px;
      }

      &.type-column {
        flex: 2;
        min-width: 180px;
      }

      &.time-column {
        flex: 1.5;
        min-width: 150px;
      }

      &.phone-column {
        flex: 1;
        min-width: 120px;
      }

      &.points-add-column {
        flex: 1;
        min-width: 120px;
        font-weight: 500;

        &.positive {
          color: #00b42a;
        }

        &.negative {
          color: #f53f3f;
        }
      }

      &.points-total-column {
        flex: 1;
        min-width: 120px;
      }
    }

    // 分页
    .pagination {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      border-top: 1px solid #f0f0f0;

      .pagination-info {
        font-size: 14px;
        color: #89929e;
      }
    }
  }
}
</style>
