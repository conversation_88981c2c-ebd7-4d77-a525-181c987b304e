import request from '@/utils/request';

/**
 * 获取角色列表
 * @param {Object} params 查询参数
 */
export function getRoleList(params) {
  return request({
    url: '/subaccount/role/list',
    method: 'get',
    params,
  });
}

/**
 * 获取角色详情
 * @param {string} id 角色ID
 */
export function getRoleDetail(id) {
  return request({
    url: `/subaccount/role/detail/${id}`,
    method: 'get',
  });
}

/**
 * 创建角色
 * @param {Object} data 角色数据
 */
export function createRole(data) {
  return request({
    url: '/subaccount/role/create',
    method: 'post',
    data,
  });
}

/**
 * 更新角色
 * @param {Object} data 角色数据
 */
export function updateRole(data) {
  return request({
    url: '/subaccount/role/update',
    method: 'post',
    data,
  });
}

/**
 * 删除角色
 * @param {string} id 角色ID
 */
export function deleteRole(id) {
  return request({
    url: `/subaccount/role/delete/${id}`,
    method: 'delete',
  });
}

/**
 * 获取账号列表
 * @param {Object} params 查询参数
 */
export function getAccountList(params) {
  return request({
    url: '/subaccount/account/list',
    method: 'get',
    params,
  });
}

/**
 * 获取账号详情
 * @param {string} id 账号ID
 */
export function getAccountDetail(id) {
  return request({
    url: `/subaccount/account/detail/${id}`,
    method: 'get',
  });
}

/**
 * 创建账号
 * @param {Object} data 账号数据
 */
export function createAccount(data) {
  return request({
    url: '/subaccount/account/create',
    method: 'post',
    data,
  });
}

/**
 * 更新账号
 * @param {Object} data 账号数据
 */
export function updateAccount(data) {
  return request({
    url: '/subaccount/account/update',
    method: 'post',
    data,
  });
}

/**
 * 获取权限菜单列表
 */
export function getPermissionMenu() {
  return request({
    url: '/subaccount/permission/menu',
    method: 'get',
  });
}

/**
 * 删除账号
 * @param {string} id 账号ID
 */
export function deleteAccount(id) {
  return request({
    url: `/subaccount/account/delete/${id}`,
    method: 'delete',
  });
}
