<template>
  <div class="agreement-list-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>协议列表</h2>
      <p>管理系统内所有协议</p>
    </div>

    <!-- 协议列表 -->
    <el-table :data="agreementList" style="width: 100%" v-loading="loading">
      <el-table-column prop="name" label="协议名称" min-width="200" />
      <el-table-column
        prop="updateTime"
        label="操作"
        width="300"
        align="center"
      >
        <template #default="scope">
          <div class="action-buttons">
            <CustomBtn type="edit" @click="handleEdit(scope.row)" />
            <CustomBtn type="check" @click="handleView(scope.row)" style="width: 120px;">查看详细</CustomBtn>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 查看协议详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="currentAgreement.name"
      width="60%"
      destroy-on-close
    >
      <div class="agreement-content" v-html="currentAgreement.content"></div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import CustomBtn from '@/components/custom-btn.vue';
import { getAgreementList, getAgreementDetail } from '@/api/agreement';

const router = useRouter();
const loading = ref(false);
const agreementList = ref([]);
const dialogVisible = ref(false);
const currentAgreement = ref({
  name: '',
  content: '',
});

// 获取协议列表
const fetchAgreementList = async () => {
  loading.value = true;
  try {
    const res = await getAgreementList();
    if (res.code === 200) {
      agreementList.value = res.data;
    } else {
      ElMessage.error(res.message || '获取协议列表失败');
    }
  } catch (error) {
    console.error('获取协议列表出错:', error);
    ElMessage.error('获取协议列表失败');
  } finally {
    loading.value = false;
  }
};

// 查看协议详情
const handleView = async (row) => {
  try {
    const res = await getAgreementDetail(row.id);
    if (res.code === 200) {
      currentAgreement.value = res.data;
      dialogVisible.value = true;
    } else {
      ElMessage.error(res.message || '获取协议详情失败');
    }
  } catch (error) {
    console.error('获取协议详情出错:', error);
    ElMessage.error('获取协议详情失败');
  }
};

// 编辑协议
const handleEdit = (row) => {
  router.push(`/agreement/edit/${row.id}`);
};

// 页面加载时获取协议列表
onMounted(() => {
  fetchAgreementList();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables.scss' as *;

.agreement-list-page {
  padding: $spacing-xl;
  background-color: $bg-color;
  min-height: 100vh;
  max-width: 1280px;
  margin: 0 auto;
  .page-header {
    margin-bottom: $spacing-xl;

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: $text-color-dark;
      margin-bottom: $spacing-xs;
    }

    p {
      font-size: 14px;
      color: $text-color-light;
    }
  }

  .action-buttons {
    display: flex;
    justify-content: end;
  }
}

// 协议内容样式
.agreement-content {
  max-height: 500px;
  overflow-y: auto;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #f9f9f9;

  h2 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 20px;
    color: #303133;
  }

  p {
    margin-bottom: 12px;
    line-height: 1.6;
    color: #606266;
  }
}

// Element Plus 样式覆盖
:deep(.el-card__body) {
  padding: $spacing-xl;
}

:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

/* 调整表头样式 */
:deep(.el-table__header th) {
  background-color: #f9fafb;
  height: 50px;
  line-height: 50px;
  font-weight: 600 !important;
  color: #86909c !important;
}
</style>
