<template>
  <div class="order-detail-container">
    <!-- 页面标题和返回按钮 -->
    <div class="page-header">
      <div class="header-left">
        <div class="title-info">
          <h1 class="page-title">积分订单详情</h1>
          <p class="page-description">查看积分订单详细信息</p>
        </div>
      </div>
    </div>

    <!-- 详情内容 -->
    <el-card class="detail-card" shadow="never" v-loading="loading">
      <div class="detail-content">
        <!-- 订单基本信息 -->
        <div class="info-section">
          <h3 class="section-title">订单信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">订单ID</span>
              <span class="info-value">{{ detailData.id }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">商品ID</span>
              <span class="info-value">{{ detailData.productId }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">商品名称</span>
              <span class="info-value product-name">{{
                detailData.productName
              }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">消耗积分</span>
              <span class="info-value points-value">{{
                detailData.points
              }}</span>
            </div>
          </div>
        </div>

        <!-- 兑换信息 -->
        <div class="info-section">
          <h3 class="section-title">兑换信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">兑换人</span>
              <span class="info-value">{{ detailData.exchangeUser }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">兑换时间</span>
              <span class="info-value">{{ detailData.exchangeTime }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">订单状态</span>
              <span
                :class="['status-tag', getStatusClass(detailData.statusCode)]"
              >
                {{ detailData.orderStatus }}
              </span>
            </div>
            <div class="info-item">
              <span class="info-label">联系电话</span>
              <span class="info-value">{{ detailData.phone || '-' }}</span>
            </div>
          </div>
        </div>

        <!-- 收货信息 -->
        <div class="info-section">
          <h3 class="section-title">收货信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">收货地址</span>
              <span class="info-value">{{ detailData.address || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">备注信息</span>
              <span class="info-value">{{ detailData.remark || '无' }}</span>
            </div>
          </div>
        </div>

        <!-- 操作记录 -->
        <div class="info-section">
          <h3 class="section-title">操作记录</h3>
          <div class="operation-timeline">
            <div class="timeline-item">
              <div class="timeline-dot active"></div>
              <div class="timeline-content">
                <div class="timeline-title">订单创建</div>
                <div class="timeline-time">{{ detailData.exchangeTime }}</div>
                <div class="timeline-desc">
                  用户成功兑换商品，消耗积分{{ detailData.points }}分
                </div>
              </div>
            </div>
            <div class="timeline-item" v-if="detailData.statusCode >= 2">
              <div class="timeline-dot active"></div>
              <div class="timeline-content">
                <div class="timeline-title">订单发货</div>
                <div class="timeline-time">{{ getShipTime() }}</div>
                <div class="timeline-desc">商品已发货，请耐心等待</div>
              </div>
            </div>
            <div class="timeline-item" v-if="detailData.statusCode >= 3">
              <div class="timeline-dot active"></div>
              <div class="timeline-content">
                <div class="timeline-title">订单完成</div>
                <div class="timeline-time">{{ getCompleteTime() }}</div>
                <div class="timeline-desc">订单已完成，感谢您的兑换</div>
              </div>
            </div>
            <div class="timeline-item" v-if="detailData.statusCode === 4">
              <div class="timeline-dot cancelled"></div>
              <div class="timeline-content">
                <div class="timeline-title">订单取消</div>
                <div class="timeline-time">{{ getCancelTime() }}</div>
                <div class="timeline-desc">订单已取消，积分已返还</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <CustomBtn style="margin-top: 20px" type="back" @click="handleBack" />
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getPointsOrderDetail } from '@/api/points-mall';
import { ElMessage } from 'element-plus';
import CustomBtn from '@/components/custom-btn.vue';

// 接收参数
const props = defineProps({
  orderId: {
    type: [String, Number],
    required: true,
  },
});

// 定义事件
const emit = defineEmits(['back']);

// 数据
const loading = ref(false);
const detailData = ref({});

// 获取详情数据
const fetchDetail = async () => {
  loading.value = true;
  try {
    const response = await getPointsOrderDetail(props.orderId);
    if (response.code === 200) {
      detailData.value = response.data;
    }
  } catch (error) {
    console.error('获取订单详情失败:', error);
    ElMessage.error('获取详情失败');
  } finally {
    loading.value = false;
  }
};

// 获取状态样式类
const getStatusClass = (statusCode) => {
  switch (statusCode) {
    case 1:
      return 'pending-status'; // 待发货
    case 2:
      return 'shipped-status'; // 已发货
    case 3:
      return 'completed-status'; // 已完成
    case 4:
      return 'cancelled-status'; // 已取消
    default:
      return 'pending-status';
  }
};

// 获取发货时间（模拟）
const getShipTime = () => {
  if (detailData.value.exchangeTime) {
    const time = new Date(detailData.value.exchangeTime);
    time.setHours(time.getHours() + 2);
    return time
      .toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      })
      .replace(/\//g, '-');
  }
  return '';
};

// 获取完成时间（模拟）
const getCompleteTime = () => {
  if (detailData.value.exchangeTime) {
    const time = new Date(detailData.value.exchangeTime);
    time.setDate(time.getDate() + 3);
    return time
      .toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      })
      .replace(/\//g, '-');
  }
  return '';
};

// 获取取消时间（模拟）
const getCancelTime = () => {
  if (detailData.value.exchangeTime) {
    const time = new Date(detailData.value.exchangeTime);
    time.setMinutes(time.getMinutes() + 30);
    return time
      .toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      })
      .replace(/\//g, '-');
  }
  return '';
};

// 返回列表
const handleBack = () => {
  emit('back');
};

// 组件挂载时获取数据
onMounted(() => {
  fetchDetail();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables.scss' as *;

.order-detail-container {
  padding: $spacing-xl;
  background-color: $bg-color;
  max-width: 1280px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 16px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;

    .title-info {
      .page-title {
        font-size: 20px;
        font-weight: 600;
        color: $text-color;
        margin: 0 0 6px 0;
      }

      .page-description {
        font-size: 13px;
        color: $text-color-light;
        margin: 0;
      }
    }
  }
}

.detail-card {
  border-radius: 10px;

  .detail-content {
    .info-section {
      margin-bottom: 32px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: $text-color;
        margin: 0 0 16px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px 24px;

        .info-item {
          display: flex;
          align-items: center;

          .info-label {
            font-size: 14px;
            color: $search-label-color;
            min-width: 80px;
            margin-right: 12px;
          }

          .info-value {
            font-size: 14px;
            color: $text-color;
            flex: 1;

            &.product-name {
              font-weight: 600;
              color: #165dff;
            }

            &.points-value {
              font-weight: 600;
              color: #fa8c16;

              &::after {
                content: '分';
                margin-left: 2px;
              }
            }
          }

          .status-tag {
            display: inline-block;
            padding: 4px 12px;
            font-size: 14px;
            border-radius: 16px;
            font-weight: 500;
            text-align: center;
            min-width: 60px;

            &.pending-status {
              background-color: #fff7e6;
              color: #fa8c16;
              border: 1px solid #fff7e6;
            }

            &.shipped-status {
              background-color: #e9eeff;
              color: #165dff;
              border: 1px solid #e9eeff;
            }

            &.completed-status {
              background-color: #e8f5e8;
              color: #00b42b;
              border: 1px solid #e8f5e8;
            }

            &.cancelled-status {
              background-color: #ffeeee;
              color: #f53f3f;
              border: 1px solid #ffeeee;
            }
          }
        }
      }

      .operation-timeline {
        .timeline-item {
          display: flex;
          align-items: flex-start;
          margin-bottom: 24px;

          &:last-child {
            margin-bottom: 0;
          }

          .timeline-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 16px;
            margin-top: 4px;
            flex-shrink: 0;

            &.active {
              background-color: #165dff;
            }

            &.cancelled {
              background-color: #f53f3f;
            }
          }

          .timeline-content {
            flex: 1;

            .timeline-title {
              font-size: 16px;
              font-weight: 600;
              color: $text-color;
              margin-bottom: 4px;
            }

            .timeline-time {
              font-size: 14px;
              color: $search-label-color;
              margin-bottom: 4px;
            }

            .timeline-desc {
              font-size: 14px;
              color: $text-color-light;
              line-height: 1.4;
            }
          }
        }
      }
    }
  }
}

:deep(.el-card) {
  border-radius: 10px;
  overflow: hidden;
}
</style>
