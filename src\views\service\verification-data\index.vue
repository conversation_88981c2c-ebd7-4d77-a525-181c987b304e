<template>
  <div class="verification-data-page">
    <div class="page-header">
      <h2>真伪数据</h2>
      <p>管理系统内所有真伪数据记录</p>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <el-row :gutter="14">
          <!-- 第一行 -->
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="search-item">
              <div class="search-label">防伪码</div>
              <el-input
                v-model="searchForm.antiCounterfeitCode"
                placeholder="请输入防伪码"
                prefix-icon="Search"
                clearable
              />
            </div>
          </el-col>

          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="search-item">
              <div class="search-label">微信昵称</div>
              <el-input
                v-model="searchForm.wechatNickname"
                placeholder="请输入微信昵称"
                prefix-icon="Search"
                clearable
              />
            </div>
          </el-col>

          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="search-item">
              <div class="search-label">扫码结果</div>
              <el-select
                v-model="searchForm.scanResult"
                placeholder="全部"
                clearable
                style="width: 100%"
                suffix-icon="ArrowDown"
              >
                <el-option label="正品" value="authentic" />
                <el-option label="非正品" value="fake" />
                <el-option label="已被受权" value="authorized" />
              </el-select>
            </div>
          </el-col>

          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <div class="search-item">
              <div class="search-label">扫码时间</div>
              <el-date-picker
                v-model="searchForm.scanTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                style="width: 100%"
                clearable
                prefix-icon="Calendar"
              />
            </div>
          </el-col>
        </el-row>

        <!-- 操作按钮 -->
        <div class="search-actions">
          <CustomBtn type="reset" @click="handleReset" />
          <CustomBtn
            style="margin-left: 10px"
            type="blue"
            @click="handleQuery"
          />
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-table
      :data="tableData"
      v-loading="loading"
      style="width: 100%;border-radius: 8px;"
      :header-cell-style="{
        backgroundColor: '#f8f9fa',
        color: '#666666',
        fontWeight: '500',
      }"
    >
      <el-table-column prop="wechatNickname" label="微信昵称" width="100" />
      <el-table-column prop="antiCounterfeitCode" label="防伪码" width="100" />
      <el-table-column prop="scanCount" label="扫码次数" width="100" />
      <el-table-column
        prop="productName"
        label="商品名称"
        width="180"
        show-overflow-tooltip
      />
      <el-table-column prop="scanResult" label="扫码结果" width="120">
        <template #default="{ row }">
          <el-tag
            :type="getScanResultType(row.scanResult)"
            :class="`scan-result-${row.scanResult}`"
            size="default"
            round
          >
            {{ getScanResultText(row.scanResult) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="lastScanTime"
        label="最新扫码时间"
        min-width="160"
        align="center"
      />
      <el-table-column
        label="操作"
        min-width="100"
        header-align="right"
        align="right"
      >
        <template #default="{ row }">
          <CustomBtn type="check" @click="handleView(row)" />
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div style="display: flex; justify-content: center; margin-top: 0px">
      <SimplePagination
        v-model="pagination.currentPage"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        @change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { getVerificationDataList } from '@/api/service';
import CustomBtn from '@/components/custom-btn.vue';
import SimplePagination from '@/components/SimplePagination.vue';

const router = useRouter();

// 搜索表单
const searchForm = reactive({
  antiCounterfeitCode: '',
  wechatNickname: '',
  scanResult: '',
  scanTime: null,
});

// 表格数据
const tableData = ref([]);
const loading = ref(false);

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

// 获取扫码结果类型
const getScanResultType = (result) => {
  const resultMap = {
    authentic: 'success',
    fake: 'danger',
    authorized: 'warning',
  };
  return resultMap[result] || 'info';
};

// 获取扫码结果文本
const getScanResultText = (result) => {
  const resultMap = {
    authentic: '正品',
    fake: '非正品',
    authorized: '扫码失败',
  };
  return resultMap[result] || '未知';
};

// 搜索处理
const handleQuery = () => {
  pagination.currentPage = 1;
  fetchData();
};

// 重置处理
const handleReset = () => {
  Object.assign(searchForm, {
    antiCounterfeitCode: '',
    wechatNickname: '',
    scanResult: '',
    scanTime: null,
  });
  pagination.currentPage = 1;
  fetchData();
};

// 查看处理 - 跳转到详情页面
const handleView = (row) => {
  router.push({
    name: 'ServiceVerificationDataDetail',
    params: { id: row.id },
  });
};

// 当前页改变
const handleCurrentChange = (page) => {
  pagination.currentPage = page;
  fetchData();
};

// 获取数据
const fetchData = async () => {
  loading.value = true;
  try {
    const response = await getVerificationDataList({
      ...searchForm,
      page: pagination.currentPage,
      size: pagination.pageSize,
    });

    tableData.value = response.data.list;
    pagination.total = response.data.total;
    loading.value = false;
  } catch (error) {
    console.error('获取真伪数据失败:', error);
    ElMessage.error('获取数据失败');
    loading.value = false;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchData();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

.verification-data-page {
  width: 100%;
  padding: $spacing-xl;
  background-color: $bg-color;
  min-height: 100vh;
  max-width: 1280px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: $spacing-xl;

  h2 {
    margin: 0 0 $spacing-sm 0;
    color: $text-color-dark;
    font-size: 24px;
    font-weight: 600;
  }

  p {
    margin: 0;
    color: $text-color-light;
    font-size: 14px;
  }
}

.search-card {
  margin-bottom: $spacing-lg;
  border-radius: $border-radius;

  .search-form {
    .search-item {
      display: flex;
      flex-direction: column;
      margin-bottom: $search-spacing-vertical;

      .search-label {
        font-size: $search-label-size;
        color: $search-label-color;
        margin-bottom: $search-label-margin;
      }
    }

    .search-actions {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-top: 5px;
    }
  }
}

// 扫码结果样式
.scan-result-authentic {
  color: #009022;
}

.scan-result-fake {
  color: #f53f3f;
}

.scan-result-authorized {
  color: #ff7d00;
}

// 分页样式调整
:deep(.el-pagination) {
  .el-pager li {
    &.is-active {
      background-color: $query-btn-color;
      color: white;
    }
  }

  .btn-prev,
  .btn-next {
    &:hover {
      color: $query-btn-color;
    }
  }
}
/* 调整表头样式 */
:deep(.el-table__header th) {
  background-color: #f9fafb;
  height: 50px;
  line-height: 50px;
  font-weight: 600 !important;
  color: #86909c !important;
}
</style>
