<template>
  <div class="product-category-page">
    <div class="page-header">
      <h2>商品分类</h2>
      <p>管理系统内所有商品分类</p>
    </div>

    <!-- 商品分类树形表格 -->
    <!-- 搜索栏 -->
    <el-card class="filter-card" shadow="never">
      <div class="filter-row">
        <div class="filter-item">
          <div class="filter-label">分类名称</div>
          <el-input v-model="searchKeyword" placeholder="请输入分类名称" clearable></el-input>
        </div>

        <div class="filter-actions">
          <el-button @click="handleReset" plain class="reset-button" :disabled="isResetDisabled">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="primary" @click="handleSearch" class="search-button">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button type="success" @click="handleAdd" class="add-button">
            <el-icon><Plus /></el-icon>
            新增
          </el-button>
        </div>
      </div>
    </el-card>

    <el-card class="category-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">商品分类树</span>
          <div class="header-right">
            <span>当前选中: </span>
            <el-breadcrumb separator=">" v-if="currentPath.length > 0">
              <template v-for="(path, index) in currentPath" :key="index">
                <el-breadcrumb-item>{{ path }}</el-breadcrumb-item>
              </template>
            </el-breadcrumb>
            <span v-else class="empty-filter">无</span>
            <div class="expand-all" @click="toggleExpandAll">
              <el-icon><component :is="isAllExpanded ? 'Fold' : 'FullScreen'" /></el-icon>
              <span>{{ isAllExpanded ? '收起全部' : '展开全部' }}</span>
            </div>
          </div>
        </div>
      </template>

      <!-- 分类树形结构 -->
      <div class="category-tree-container">
        <!-- 分类树形列表 -->
        <div class="category-tree">
          <div v-for="(category, index) in displayCategories" :key="category.id || category.tempId" class="category-item" :class="{ active: isActiveCategory(category), 'new-category-item': category.isNew }" :data-level="category.level">
            <div class="category-item-content" @click="category.isNew ? null : selectCategory(category)">
              <div class="category-icon">
                <el-icon v-if="category.level < 3 && category.children && category.children.length">
                  <component :is="category.expanded ? 'Minus' : 'Plus'" />
                </el-icon>
                <span v-else-if="category.level === 3" class="category-dot" :class="{ 'active-dot': isActiveCategory(category) }"></span>
                <el-icon v-else><Plus /></el-icon>
              </div>
              <div v-if="category.isEditing" class="category-name-edit" @click.stop>
                <el-input
                  v-model="category.editingName"
                  size="small"
                  class="tree-edit-input"
                  :ref="
                    (el) => {
                      if (el) editInputRef = el
                    }
                  "
                  @blur="saveInlineEdit(category)"
                  @keyup.enter="saveInlineEdit(category)"
                  @keyup.esc="cancelInlineEdit(category)"
                >
                </el-input>
              </div>
              <div v-else-if="category.isNew" class="category-name-edit" @click.stop>
                <el-input
                  v-model="newCategoryName"
                  size="small"
                  class="tree-edit-input"
                  :ref="
                    (el) => {
                      if (el) newInputRef = el
                    }
                  "
                  placeholder="请输入分类名称"
                  @blur="handleAddBlur(category, index)"
                  @keyup.enter="saveNewCategory(category, index)"
                  @keyup.esc="cancelNewCategory(index)"
                >
                </el-input>
              </div>
              <div v-else class="category-name">{{ category.name }}</div>
            </div>

            <div class="category-actions">
              <template v-if="category.isNew">
                <el-button size="small" type="success" @click.stop="saveNewCategory(category, index)" link>
                  <el-icon><Check /></el-icon>
                </el-button>
                <el-button size="small" type="danger" @click.stop="cancelNewCategory(index)" link>
                  <el-icon><Close /></el-icon>
                </el-button>
              </template>
              <template v-else>
                <el-button size="small" type="primary" @click.stop="handleInlineEdit(category)" link>
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button v-if="category.level < 3" size="small" type="success" @click.stop="handleAddChild(category)" link>
                  <el-icon><Plus /></el-icon>
                </el-button>
                <el-button size="small" type="danger" @click.stop="handleDelete(category)" link>
                  <el-icon><Delete /></el-icon>
                </el-button>
              </template>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 新增/编辑分类对话框 -->
    <el-dialog :title="dialogType === 'add' ? '新增分类' : '编辑分类'" v-model="dialogVisible" width="500px">
      <el-form :model="categoryForm" label-width="100px" :rules="formRules" ref="categoryFormRef">
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称"></el-input>
        </el-form-item>

        <el-form-item label="上级分类" v-if="dialogType === 'add'">
          <el-cascader
            v-model="categoryForm.parentIds"
            :options="cascaderOptions"
            :props="{
              checkStrictly: true,
              label: 'name',
              value: 'id',
              children: 'children'
            }"
            clearable
            placeholder="请选择上级分类"
          ></el-cascader>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch, onBeforeUnmount, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getProductCategoryTree, addProductCategory, updateProductCategory, deleteProductCategory } from '@/api/product'
import {
  UserFilled,
  Setting,
  ArrowDown,
  Bell,
  Goods,
  ShoppingCart,
  Document,
  Avatar,
  List,
  Money,
  Shop,
  Promotion,
  DataLine,
  TrendCharts,
  Service,
  FolderOpened,
  Top,
  PriceTag,
  Picture,
  PictureRounded,
  DocumentChecked,
  DataAnalysis,
  Tickets,
  SetUp,
  Collection,
  User,
  Lock,
  HomeFilled,
  Edit,
  Delete,
  Plus,
  Minus,
  View,
  Fold,
  FullScreen,
  Check,
  Close,
  Search,
  Refresh
} from '@element-plus/icons-vue'

// 自定义聚焦指令
const vFocus = {
  mounted: (el) => {
    // 获取实际的输入框元素（Element Plus组件）
    const inputEl = el.querySelector('.el-input__inner')
    if (inputEl) {
      nextTick(() => {
        inputEl.focus()
        // 选择所有文本
        inputEl.select()
      })
    }
  }
}

// 分类列表数据
const categoryTree = ref([])

// 显示用的分类数据
const displayCategories = ref([])

// 选中的分类
const selectedCategory = ref(null)

// 是否全部展开
const isAllExpanded = ref(false)

// 是否重置按钮禁用状态
const isResetDisabled = ref(false)

// 搜索关键词
const searchKeyword = ref('')

// 原始分类树数据（用于搜索恢复）
const originalCategoryTree = ref([])

// 当前选中的路径
const currentPath = ref([])

// 编辑框引用
const editInputRef = ref(null)

// 对话框相关
const dialogVisible = ref(false)
const dialogType = ref('add') // 'add' 或 'edit'
const categoryFormRef = ref(null)
const categoryForm = reactive({
  id: null,
  name: '',
  parentId: null,
  parentIds: [],
  level: 1
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ]
}

// 级联选择器选项
const cascaderOptions = computed(() => {
  return categoryTree.value
    .map((item) => {
      if (item.level < 3) {
        return item
      }
      return null
    })
    .filter(Boolean)
})

// 新增分类相关
const isAddingCategory = ref(false)
const newCategoryParentId = ref(null)
const newCategoryLevel = ref(1)
const newCategoryName = ref('')
const newInputRef = ref(null)

// 获取分类树
const fetchCategoryTree = async () => {
  try {
    const res = await getProductCategoryTree()
    categoryTree.value = res.data
    originalCategoryTree.value = JSON.parse(JSON.stringify(res.data)) // 保存原始数据
    initDisplayCategories()
  } catch (error) {
    console.error('获取分类树失败:', error)
    ElMessage.error('获取分类树失败')
  }
}

// 初始化展示分类
const initDisplayCategories = () => {
  displayCategories.value = categoryTree.value.map((category) => ({
    ...category,
    expanded: false,
    isEditing: false,
    editingName: category.name
  }))
}

// 判断是否是当前选中的分类
const isActiveCategory = (category) => {
  return selectedCategory.value && selectedCategory.value.id === category.id
}

// 选择分类
const selectCategory = (category) => {
  // 如果有正在编辑的分类
  const editingCategory = displayCategories.value.find((item) => item.isEditing)

  // 如果点击的是当前正在编辑的分类，不做任何操作
  if (editingCategory && editingCategory.id === category.id) {
    return
  }

  // 如果有其他分类正在编辑，先保存
  if (editingCategory) {
    saveInlineEdit(editingCategory)
  }

  if (category.level < 3 && category.children && category.children.length > 0) {
    // 切换展开/收起状态
    const index = displayCategories.value.findIndex((item) => item.id === category.id)
    if (index !== -1) {
      const isExpanded = displayCategories.value[index].expanded
      displayCategories.value[index].expanded = !isExpanded

      if (!isExpanded) {
        // 展开，添加子分类到显示列表
        let insertPosition = index + 1
        if (category.children) {
          category.children.forEach((child) => {
            displayCategories.value.splice(insertPosition, 0, {
              ...child,
              expanded: false,
              isEditing: false,
              editingName: child.name
            })
            insertPosition++
          })
        }
      } else {
        // 收起，从显示列表中移除所有子分类及其子分类的子分类
        const idsToRemove = getAllChildrenIds(category)
        displayCategories.value = displayCategories.value.filter((item) => !idsToRemove.includes(item.id))
      }
    }
  }

  // 设置选中状态和路径
  selectedCategory.value = category
  updateCurrentPath(category)
}

// 获取所有子分类ID
const getAllChildrenIds = (category) => {
  const ids = []
  if (category.children) {
    category.children.forEach((child) => {
      ids.push(child.id)
      if (child.children) {
        ids.push(...getAllChildrenIds(child))
      }
    })
  }
  return ids
}

// 更新当前路径
const updateCurrentPath = (category) => {
  currentPath.value = []
  if (category.level === 1) {
    currentPath.value = [category.name]
  } else if (category.level === 2) {
    const parent = categoryTree.value.find((item) => item.id === category.parentId)
    if (parent) {
      currentPath.value = [parent.name, category.name]
    }
  } else if (category.level === 3) {
    // 找到二级父分类
    let parent2 = null
    categoryTree.value.forEach((level1) => {
      if (level1.children) {
        const foundParent2 = level1.children.find((level2) => level2.id === category.parentId)
        if (foundParent2) {
          parent2 = foundParent2
        }
      }
    })

    if (parent2) {
      // 找到一级父分类
      const parent1 = categoryTree.value.find((item) => item.id === parent2.parentId)
      if (parent1) {
        currentPath.value = [parent1.name, parent2.name, category.name]
      }
    }
  }
}

// 切换展开/收起所有分类
const toggleExpandAll = () => {
  if (isAllExpanded.value) {
    // 收起所有分类
    displayCategories.value = categoryTree.value.map((category) => ({
      ...category,
      expanded: false
    }))
    isAllExpanded.value = false
  } else {
    // 展开所有分类
    expandAllCategories()
    isAllExpanded.value = true
  }
}

// 展开所有分类
const expandAllCategories = () => {
  // 清空当前显示的分类
  displayCategories.value = []

  // 遍历处理一级分类
  categoryTree.value.forEach((level1) => {
    // 添加一级分类
    const level1Item = {
      ...level1,
      expanded: true
    }
    displayCategories.value.push(level1Item)

    // 如果有子分类
    if (level1.children && level1.children.length > 0) {
      // 遍历处理二级分类
      level1.children.forEach((level2) => {
        // 添加二级分类
        const level2Item = {
          ...level2,
          expanded: true
        }
        displayCategories.value.push(level2Item)

        // 如果二级分类有子分类
        if (level2.children && level2.children.length > 0) {
          // 添加三级分类
          level2.children.forEach((level3) => {
            displayCategories.value.push({
              ...level3
            })
          })
        }
      })
    }
  })
}

// 新增分类
const handleAdd = () => {
  dialogType.value = 'add'
  resetForm()
  dialogVisible.value = true
}

// 编辑分类
const handleEdit = (row) => {
  dialogType.value = 'edit'
  resetForm()

  Object.assign(categoryForm, {
    id: row.id,
    name: row.name,
    level: row.level,
    parentId: row.parentId
  })

  dialogVisible.value = true
}

// 新增子分类
const handleAddChild = (parentCategory) => {
  // 取消所有编辑状态
  const editingCategory = displayCategories.value.find((item) => item.isEditing)
  if (editingCategory) {
    saveInlineEdit(editingCategory)
  }

  // 取消已有的新增状态
  if (isAddingCategory.value) {
    cancelNewCategory()
  }

  // 如果父级是未展开状态且有子元素，先展开父级
  if (parentCategory.level < 3 && parentCategory.children && parentCategory.children.length > 0 && !parentCategory.expanded) {
    // 只展开，不触发选择逻辑
    const index = displayCategories.value.findIndex((item) => item.id === parentCategory.id)
    if (index !== -1) {
      displayCategories.value[index].expanded = true

      // 添加子分类到显示列表
      let insertPosition = index + 1
      if (parentCategory.children) {
        parentCategory.children.forEach((child) => {
          displayCategories.value.splice(insertPosition, 0, {
            ...child,
            expanded: false,
            isEditing: false,
            editingName: child.name
          })
          insertPosition++
        })
      }
    }
  }

  // 找到父分类在显示列表中的位置
  const parentIndex = displayCategories.value.findIndex((item) => item.id === parentCategory.id)
  if (parentIndex === -1) return

  // 设置新增状态
  isAddingCategory.value = true
  newCategoryParentId.value = parentCategory.id
  newCategoryLevel.value = parentCategory.level + 1
  newCategoryName.value = ''

  // 确定插入新分类项的位置
  let insertPosition = parentIndex + 1

  // 如果有子分类，需要找到最后一个直接子分类的位置
  if (parentCategory.children && parentCategory.children.length > 0) {
    // 遍历显示列表，找出所有直接子分类
    const directChildrenIndices = []
    for (let i = parentIndex + 1; i < displayCategories.value.length; i++) {
      const item = displayCategories.value[i]

      // 如果层级不是直接子层级，则跳过
      if (item.level !== parentCategory.level + 1) {
        // 如果层级小于等于父分类层级，说明已经不是子分类了
        if (item.level <= parentCategory.level) {
          break
        }
        continue
      }

      // 如果是直接子分类且父ID匹配
      if (item.parentId === parentCategory.id) {
        directChildrenIndices.push(i)
      }
    }

    // 如果有直接子分类，则在最后一个直接子分类后插入
    if (directChildrenIndices.length > 0) {
      insertPosition = directChildrenIndices[directChildrenIndices.length - 1] + 1

      // 还需要考虑最后一个直接子分类是否有展开的子分类
      let lastDirectChildIndex = directChildrenIndices[directChildrenIndices.length - 1]
      let lastDirectChild = displayCategories.value[lastDirectChildIndex]

      // 如果最后一个直接子分类是展开状态，需要跳过其所有子分类
      if (lastDirectChild.expanded) {
        let i = lastDirectChildIndex + 1
        while (i < displayCategories.value.length && displayCategories.value[i].level > lastDirectChild.level) {
          i++
        }
        insertPosition = i
      }
    }
  }

  // 创建新的空分类项并插入到显示列表中
  const newItem = {
    tempId: Date.now(), // 临时ID用于key
    name: '',
    level: newCategoryLevel.value,
    parentId: newCategoryParentId.value,
    isEditing: false, // 不使用编辑状态，使用新增状态
    isNew: true // 标记为新项
  }

  displayCategories.value.splice(insertPosition, 0, newItem)

  // 聚焦输入框
  nextTick(() => {
    if (newInputRef.value) {
      newInputRef.value.focus()
    }
  })
}

// 处理新增分类输入框失去焦点
const handleAddBlur = (category, index) => {
  // 如果没有输入内容，取消新增
  if (!newCategoryName.value.trim()) {
    cancelNewCategory(index)
  }
}

// 保存新分类
const saveNewCategory = async (category, index) => {
  // 验证输入
  if (!newCategoryName.value.trim()) {
    ElMessage.warning('分类名称不能为空')
    return
  }

  if (newCategoryName.value.trim().length < 2 || newCategoryName.value.trim().length > 20) {
    ElMessage.warning('分类名称长度在2到20个字符之间')
    return
  }

  try {
    // 构建新分类数据
    const newCategory = {
      name: newCategoryName.value.trim(),
      level: category.level,
      parentId: category.parentId
    }

    // 调用API添加分类
    const response = await addProductCategory(newCategory)
    ElMessage.success('添加成功')

    // 替换临时分类项为真实分类
    if (index !== undefined && response.data && response.data.id) {
      // 更新临时项为真实数据
      const realCategory = {
        id: response.data.id,
        name: newCategoryName.value.trim(),
        level: category.level,
        parentId: category.parentId,
        children: [],
        expanded: false,
        isEditing: false,
        editingName: newCategoryName.value.trim()
      }

      // 替换临时项
      displayCategories.value.splice(index, 1, realCategory)
    }

    // 清空新增状态
    newCategoryName.value = ''
    isAddingCategory.value = false
  } catch (error) {
    console.error('添加分类失败:', error)
    ElMessage.error(error.response?.data?.message || '添加失败')
  }
}

// 取消新增分类
const cancelNewCategory = (index) => {
  // 从显示列表中移除临时项
  if (index !== undefined) {
    displayCategories.value.splice(index, 1)
  } else {
    // 查找并移除所有临时项
    for (let i = displayCategories.value.length - 1; i >= 0; i--) {
      if (displayCategories.value[i].isNew) {
        displayCategories.value.splice(i, 1)
      }
    }
  }

  isAddingCategory.value = false
  newCategoryName.value = ''
}

// 处理就地编辑
const handleInlineEdit = (category) => {
  // 如果点击的分类已经是编辑状态，不做处理
  if (category.isEditing) {
    return
  }

  // 如果有其他正在编辑的分类，先保存
  const editingCategory = displayCategories.value.find((item) => item.isEditing)
  if (editingCategory && editingCategory.id !== category.id) {
    saveInlineEdit(editingCategory)
  }

  // 设置当前分类为编辑状态
  const index = displayCategories.value.findIndex((item) => item.id === category.id)
  if (index !== -1) {
    displayCategories.value[index].isEditing = true
    displayCategories.value[index].editingName = category.name

    // 等待DOM更新后聚焦输入框
    nextTick(() => {
      if (editInputRef.value) {
        editInputRef.value.focus()
        // 获取输入框元素并将光标移至末尾
        const inputEl = editInputRef.value.$el.querySelector('input')
        if (inputEl) {
          // 设置光标位置到文本末尾
          const textLength = inputEl.value.length
          inputEl.setSelectionRange(textLength, textLength)
        }
      }
    })
  }
}

// 保存就地编辑
const saveInlineEdit = async (category) => {
  if (!category.editingName || category.editingName.trim() === '') {
    ElMessage.warning('分类名称不能为空')
    category.editingName = category.name
    category.isEditing = false
    return
  }

  if (category.editingName.trim().length < 2 || category.editingName.trim().length > 20) {
    ElMessage.warning('分类名称长度在2到20个字符之间')
    category.editingName = category.name
    category.isEditing = false
    return
  }

  if (category.editingName !== category.name) {
    try {
      await updateProductCategory(category.id, { name: category.editingName.trim() })
      ElMessage.success('更新成功')

      // 更新显示的分类名称
      category.name = category.editingName.trim()

      // 更新分类树中的名称
      updateCategoryTreeName(category.id, category.name)

      // 如果当前分类是选中的分类，更新路径
      if (selectedCategory.value && selectedCategory.value.id === category.id) {
        updateCurrentPath(category)
      }
    } catch (error) {
      console.error('更新分类名称失败:', error)
      ElMessage.error(error.response?.data?.message || '更新失败')
      category.editingName = category.name
    }
  }

  // 退出编辑状态
  category.isEditing = false
}

// 取消就地编辑
const cancelInlineEdit = (category) => {
  category.isEditing = false
  category.editingName = category.name
}

// 更新分类树中的名称
const updateCategoryTreeName = (categoryId, newName) => {
  // 遍历一级分类
  for (let i = 0; i < categoryTree.value.length; i++) {
    if (categoryTree.value[i].id === categoryId) {
      categoryTree.value[i].name = newName
      return
    }

    // 遍历二级分类
    if (categoryTree.value[i].children) {
      for (let j = 0; j < categoryTree.value[i].children.length; j++) {
        if (categoryTree.value[i].children[j].id === categoryId) {
          categoryTree.value[i].children[j].name = newName
          return
        }

        // 遍历三级分类
        if (categoryTree.value[i].children[j].children) {
          for (let k = 0; k < categoryTree.value[i].children[j].children.length; k++) {
            if (categoryTree.value[i].children[j].children[k].id === categoryId) {
              categoryTree.value[i].children[j].children[k].name = newName
              return
            }
          }
        }
      }
    }
  }
}

// 删除分类
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该分类吗？删除后将无法恢复。', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        // 保存当前展开状态
        const expandedIds = displayCategories.value.filter((item) => item.expanded).map((item) => item.id)

        // 保存当前选中状态
        const currentSelectedId = selectedCategory.value ? selectedCategory.value.id : null

        // 删除分类
        const res = await deleteProductCategory(row.id)
        ElMessage.success('删除成功')

        // 从显示列表中直接移除该分类及其子分类
        const itemIndex = displayCategories.value.findIndex((item) => item.id === row.id)
        if (itemIndex !== -1) {
          // 获取所有需要移除的分类ID（包括子分类）
          const idsToRemove = [row.id]
          // 如果是展开状态，还需要移除所有显示的子分类
          if (row.expanded) {
            let i = itemIndex + 1
            while (i < displayCategories.value.length && displayCategories.value[i].level > row.level) {
              idsToRemove.push(displayCategories.value[i].id)
              i++
            }
          }

          // 从显示列表中移除这些分类
          displayCategories.value = displayCategories.value.filter((item) => !idsToRemove.includes(item.id))

          // 如果被删除的是当前选中的分类，清除选中状态
          if (currentSelectedId === row.id) {
            selectedCategory.value = null
            currentPath.value = []
          }
        }
      } catch (error) {
        console.error('删除分类失败:', error)
        if (error.response && error.response.data && error.response.data.message) {
          ElMessage.error(error.response.data.message)
        } else {
          ElMessage.error('删除失败')
        }
      }
    })
    .catch(() => {})
}

// 重置表单
const resetForm = () => {
  categoryForm.id = null
  categoryForm.name = ''
  categoryForm.parentId = null
  categoryForm.parentIds = []
  categoryForm.level = 1
}

// 提交表单
const submitForm = async () => {
  if (!categoryFormRef.value) return

  try {
    await categoryFormRef.value.validate()

    // 处理父级ID
    if (dialogType.value === 'add' && categoryForm.parentIds.length > 0) {
      categoryForm.parentId = categoryForm.parentIds[categoryForm.parentIds.length - 1]
    }

    if (dialogType.value === 'add') {
      await addProductCategory({
        name: categoryForm.name,
        level: categoryForm.level,
        parentId: categoryForm.parentId
      })
      ElMessage.success('添加成功')
    } else {
      await updateProductCategory(categoryForm.id, {
        name: categoryForm.name
      })
      ElMessage.success('更新成功')
    }

    dialogVisible.value = false
    fetchCategoryTree()
  } catch (error) {
    console.error('表单提交失败:', error)
    ElMessage.error(error.message || '操作失败')
  }
}

// 处理搜索
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    // 如果搜索关键词为空，恢复原始数据
    categoryTree.value = JSON.parse(JSON.stringify(originalCategoryTree.value))
    initDisplayCategories()
    return
  }

  // 搜索逻辑
  const keyword = searchKeyword.value.trim().toLowerCase()
  const searchResults = []

  // 记录匹配项及其父级路径的ID
  const matchPathIds = new Set()

  // 递归搜索函数
  const searchInCategories = (categories, path = []) => {
    categories.forEach((category) => {
      const currentPath = [...path, category.name]
      const match = category.name.toLowerCase().includes(keyword)

      if (match) {
        // 如果匹配，添加到结果中
        searchResults.push({
          ...category,
          path: currentPath
        })

        // 将当前分类ID加入匹配路径
        matchPathIds.add(category.id)

        // 如果是二级或三级分类，需要将父级路径也加入
        if (category.level === 2 && category.parentId) {
          matchPathIds.add(category.parentId) // 添加一级父级ID
        } else if (category.level === 3 && category.parentId) {
          // 添加二级父级ID
          matchPathIds.add(category.parentId)

          // 寻找一级父级ID
          for (const level1 of originalCategoryTree.value) {
            for (const level2 of level1.children || []) {
              if (level2.id === category.parentId) {
                matchPathIds.add(level1.id) // 添加一级父级ID
                break
              }
            }
          }
        }
      }

      // 递归搜索子分类
      if (category.children && category.children.length > 0) {
        searchInCategories(category.children, currentPath)
      }
    })
  }

  // 开始搜索
  searchInCategories(originalCategoryTree.value)

  // 更新分类树和显示
  if (searchResults.length > 0) {
    // 使用原始分类树
    categoryTree.value = JSON.parse(JSON.stringify(originalCategoryTree.value))

    // 初始化分类显示列表
    initDisplayCategories()

    // 展开包含搜索结果的路径
    const pathIdsArray = Array.from(matchPathIds)

    // 针对性地展开匹配路径上的分类
    let index = 0
    while (index < displayCategories.value.length) {
      const category = displayCategories.value[index]

      // 如果当前分类在匹配路径上且有子分类
      if (pathIdsArray.includes(category.id) && category.level < 3 && category.children && category.children.length > 0) {
        // 设置为展开状态
        displayCategories.value[index].expanded = true

        // 插入子分类到显示列表
        let insertPos = index + 1
        category.children.forEach((child) => {
          displayCategories.value.splice(insertPos, 0, {
            ...child,
            expanded: false,
            isEditing: false,
            editingName: child.name
          })
          insertPos++
        })
      }

      index++
    }

    // 如果只有一个搜索结果，自动选中它
    if (searchResults.length === 1) {
      // 等待DOM更新后选中
      nextTick(() => {
        const targetItem = displayCategories.value.find((item) => item.id === searchResults[0].id)
        if (targetItem) {
          selectCategory(targetItem)
        }
      })
    }
  } else {
    // 没有搜索结果
    categoryTree.value = []
    displayCategories.value = []
    ElMessage.info('没有找到匹配的分类')
  }
}

// 处理重置
const handleReset = () => {
  // 确保按钮不会被禁用
  isResetDisabled.value = false

  searchKeyword.value = ''
  categoryTree.value = JSON.parse(JSON.stringify(originalCategoryTree.value))
  initDisplayCategories()
  isAllExpanded.value = false
  selectedCategory.value = null
  currentPath.value = []

  // 强制移除焦点
  document.activeElement.blur()
}

// 页面加载时获取数据
onMounted(() => {
  fetchCategoryTree().then(() => {
    initDisplayCategories()
  })

  // 添加全局点击事件，确保重置按钮在任何点击后都能恢复正常
  document.addEventListener('click', () => {
    isResetDisabled.value = false
  })

  // 添加全局点击事件，处理点击页面其他位置退出编辑状态
  document.addEventListener('click', handleDocumentClick)
})

// 修改文档点击事件处理函数，兼容新增状态
const handleDocumentClick = (e) => {
  // 处理编辑状态
  const editingCategory = displayCategories.value.find((item) => item.isEditing)
  if (editingCategory) {
    // 检查点击是否在输入框内部
    const isClickInside = e.target.closest('.category-name-edit') !== null
    // 检查点击是否在编辑按钮上
    const isClickEditButton = e.target.closest('.category-actions button') !== null

    // 如果点击在输入框内部或编辑按钮上，不做处理
    if (isClickInside || isClickEditButton) {
      return
    }

    // 其他情况，保存编辑
    saveInlineEdit(editingCategory)
  }

  // 处理新增状态
  const newItemIndex = displayCategories.value.findIndex((item) => item.isNew)
  if (newItemIndex !== -1) {
    // 检查点击是否在新增输入框内部
    const isClickInsideNew = e.target.closest('.new-category-item .category-name-edit') !== null
    // 检查点击是否在新增操作按钮上
    const isClickNewButton = e.target.closest('.new-category-item .category-actions button') !== null

    // 如果点击在新增输入框内部或新增操作按钮上，不做处理
    if (isClickInsideNew || isClickNewButton) {
      return
    }

    // 如果有输入内容，则保存
    if (newCategoryName.value.trim()) {
      saveNewCategory(displayCategories.value[newItemIndex], newItemIndex)
    } else {
      // 否则取消新增
      cancelNewCategory(newItemIndex)
    }
  }
}

// 组件卸载时移除事件监听
onBeforeUnmount(() => {
  document.removeEventListener('click', () => {
    isResetDisabled.value = false
  })

  document.removeEventListener('click', handleDocumentClick)
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;
@use 'sass:color';

.product-category-page {
  max-width: 1280px;
  margin: 0 auto;
  min-height: 500px; /* 设置最小高度，避免页面切换时晃动 */
}

.filter-card,
.category-card {
  margin-bottom: $card-spacing;
  border-radius: $border-radius;

  :deep(.el-card__body) {
    padding: $search-padding;
    overflow: hidden;
  }
}

.category-card {
  :deep(.el-card__body) {
    padding: 20px;
  }
}

.filter-row {
  display: flex;
  align-items: flex-end;
  width: 100%;
  gap: $search-spacing-vertical;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 120px;

  &:last-child {
    margin-right: 0;
  }
}

.filter-label {
  font-size: $search-label-size;
  color: $search-label-color;
  margin-bottom: $search-label-margin;
}

.filter-select {
  width: 100%;
}

.filter-item {
  :deep(.el-input__inner),
  :deep(.el-select .el-input__inner) {
    color: $text-color;
  }

  :deep(.el-input__inner::placeholder),
  :deep(.el-select .el-input__inner::placeholder) {
    color: $placeholder-color;
  }
}

.page-header {
  margin-bottom: $card-spacing; // 更新为统一的24px间距

  h2 {
    font-size: 24px;
    margin: 0;
    font-weight: 600;
    color: $text-color-dark;
  }

  p {
    color: $text-color-light;
    margin: 5px 0 0;
  }
}

// 搜索栏按钮样式已移至全局样式，此处无需重复定义

.category-card {
  margin-bottom: $card-spacing; // 使用全局卡片间距变量
  border-radius: $border-radius; // 使用全局圆角变量
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: $text-color-dark;
}

.header-right {
  display: flex;
  align-items: center;

  span {
    font-size: 14px;
    color: $search-label-color;
    margin-right: 8px;
  }

  .expand-all {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: $primary-color;

    .el-icon {
      margin-right: 4px;
    }

    &:hover {
      opacity: 0.8;
    }
  }

  .header-add-btn {
    margin-left: 2px;
    height: 32px;
    padding: 0 12px;

    .el-icon {
      margin-right: 4px;
    }
  }
}

.empty-filter {
  color: #3170ff !important;
  font-size: 14px;
}

/* 移除面包屑导航的下划线 */
:deep(.el-breadcrumb__inner) {
  text-decoration: none !important;
  color: #3170ff !important;
}

:deep(.el-breadcrumb) {
  color: #3170ff;
}

:deep(.el-breadcrumb__inner.is-link) {
  text-decoration: none !important;
  font-weight: normal;
  color: #3170ff;
}

:deep(.el-breadcrumb__inner.is-link:hover) {
  color: #3170ff;
  text-decoration: none !important;
}

:deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: #3170ff;
}

:deep(.el-breadcrumb__separator) {
  margin: 0 0 0 9px;
  color: #3170ff;
}

.expand-all {
  display: flex;
  align-items: center;
  margin-left: auto;
  cursor: pointer;
  color: #3170ff;
  font-size: 14px;
}

.expand-all .el-icon {
  margin-right: 4px;
  font-size: 16px;
}

.expand-all:hover {
  opacity: 0.8;
}

.expand-all span {
  color: #3170ff;
}

.filter-text {
  font-size: 14px;
  color: $search-label-color;
}

.filter-text .el-tag {
  margin-left: 8px;
}

@media (max-width: 992px) {
  .filter-row {
    gap: 8px;
  }

  .filter-item {
    min-width: 100px;
  }

  .filter-actions {
    min-width: 200px;
    gap: 5px;
  }
}

@media (max-width: 768px) {
  .filter-item {
    flex: 0 0 100%;
  }

  .filter-actions {
    width: 100%;
    margin-top: 10px;
    justify-content: flex-end;
  }
}

.el-dialog {
  border-radius: $border-radius;
  overflow: hidden;
}

.category-tree {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.category-tree-container {
  padding: 0;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
  transition: background-color 0.2s;
}

.category-item:hover {
  background-color: #f5f7fa;
}

.new-category-item {
  background-color: #f5f7fa;
}

.category-item-content {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0; /* 允许内容区域收缩 */
  margin-right: 10px; /* 与操作按钮保持一定距离 */
}

.category-icon {
  margin-right: 8px;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: $search-label-color;
}

.category-icon .el-icon {
  font-size: 14px;
}

.category-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 2px solid #252830;
  background-color: transparent;
}

.active-dot {
  border-color: #2e6dff;
  background-color: #2e6dff;
}

.category-name {
  font-size: 14px;
  color: $text-color-dark;
}

.category-name-edit {
  flex: 1;
  width: 100%;
  margin-right: 8px;

  :deep(.el-input__wrapper) {
    padding: 0 10px;
    box-shadow: none;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }

  :deep(.el-input__inner) {
    color: $text-color-dark;
    font-size: 14px;
    height: 28px;
    line-height: 28px;
  }
}

.active .category-name {
  color: #3c76ff;
}

.category-actions {
  display: flex;
  white-space: nowrap; /* 防止操作按钮换行 */
  flex-shrink: 0; /* 防止操作区域被压缩 */
  opacity: 0;
  transition: opacity 0.2s;
}

.category-item:hover .category-actions,
.category-item.active .category-actions,
.new-category-item .category-actions {
  opacity: 1;
}

.category-actions .el-button {
  margin-left: 8px;
}

.category-actions {
  :deep(.el-button.is-link) {
    &:hover {
      background-color: transparent !important;
    }
  }

  :deep(.el-button--primary.is-link) {
    &:hover {
      color: $primary-hover !important;
      font-weight: 600;
    }
  }

  :deep(.el-button--success.is-link) {
    &:hover {
      color: $success-color !important;
      font-weight: 600;
    }
  }

  :deep(.el-button--danger.is-link) {
    &:hover {
      color: $danger-color !important;
      font-weight: 600;
    }
  }
}

.active {
  background-color: #e9eeff;
}

/* 层级缩进 */
.category-item[data-level='1'] .category-item-content {
  padding-left: 0;
}

.category-item[data-level='2'] .category-item-content {
  padding-left: 24px;
}

.category-item[data-level='3'] .category-item-content {
  padding-left: 48px;
}

/* 面包屑样式 */
:deep(.el-breadcrumb__item) {
  font-size: 14px;
}

:deep(.el-breadcrumb__inner) {
  color: #606266;
}

:deep(.el-breadcrumb__inner.is-link:hover) {
  color: #409eff;
}

/* 表单标签样式 */
:deep(.el-form-item__label) {
  color: $search-label-color;
}

/* 树编辑输入框特殊样式 */
.tree-edit-input {
  :deep(.el-input__wrapper) {
    padding: 0 10px !important;
    height: 28px !important;
    line-height: 28px;
    box-sizing: border-box;
  }

  :deep(.el-input__inner) {
    height: 28px;
    line-height: 28px;
    padding: 0;
  }
}
</style>
