---
alwaysApply: true
---
# 李白眼镜小程序项目规范

## 1. 项目初始步骤

开始新任务时，先熟悉整个项目结构并分析项目内容，确保对代码有整体了解，并以中文的形式告诉我你知道了什么。

## 2. 组件命名与存放规则

- 通用可复用组件统一存放在全局`components`文件夹中，按功能模块分类存放（如`components/Button.vue`）

## 3. 沟通规范

在修改代码后，用中文描述做了哪些修改，让团队成员容易理解变更内容。

## 4. 数据一致性

Mock数据的编写风格要与现有的home等页面保持一致，保证测试数据格式统一，Mock数据的接口地址不能和路由地址一致。

## 5. 按钮样式规范

- 重置按钮：背景色`#F2F3F5`，悬停色`#E5E7EB`
- 查询按钮：背景色`#165DFF`
- 新增按钮：背景色`#00B42B`
- 所有按钮在鼠标悬停时不显示边框，且有高光
- 按钮高度：`40px`
- 按钮内边距：`8px 16px`
- 按钮字体大小：`16px`
- 按钮图标与文字间距：`4px`
- 适用按钮：重置、查询、新增、保存、取消等

## 6. 输入框样式规范

- 输入框高度：`42px`（包含2px边框）
- 输入框聚焦边框颜色：`rgb(22,93,225)`
- 下拉框样式保持现有形式

## 7. 搜索栏样式规范

- 上下搜索栏之间间隔：`14px`
- 同一行搜索栏之间间隔：`14px`
- 搜索框内边距：`20px`
- 标签样式：
  ```css
  .search-label {
    font-size: 14px;
    color: #86909c;
    margin-bottom: 4px;
  }
  ```
- 提示文字(placeholder)颜色：`#9CA3AF`
- 布局方式：上下布局
- 按钮悬停效果：重置按钮变为`#E5E7EB`，其他按钮有高光效果
- 字体颜色在悬停时保持不变

## 8. 卡片样式及圆角规范

- 卡片组件统一圆角：`8px`
- 输入框圆角：`6px`
- 按钮圆角：`6px`
- 卡片之间间隔：`24px`
- 卡片与标题之间间隔：`24px`

## 9. CSS与图标规范

- 所有CSS采用SASS开发
- 项目中的图标已全局注册，使用时无需重复引入
- 对于SASS变量只有确认需要修改时才进行修改

## 10. 组件内部命名规范

在组件内部定义变量、方法等时，使用小驼峰命名法（如`getUserInfo`、`searchForm`）。

## 11. 开发前准备

开始编写页面前，先熟悉项目的SASS全局变量配置，确保样式一致性。

## 12. 重置按钮行为规范

- 重置按钮点击后应立即恢复到未点击状态
- 重置按钮点击后不应被禁用，保持可点击状态
- 实现方法：
  - 使用`:active`伪类设置样式恢复原状态
  - 在点击处理函数中设置`isResetDisabled = false`
  - 使用`document.activeElement.blur()`强制移除焦点
  - 添加全局点击事件监听确保按钮随时可点击

## 13. 路由与视图结构规范

- `views`文件夹中的结构应与路由结构相对应
- 采用层级嵌套结构，每个导航菜单对应一个顶层路由
- 父路由可以包含子路由（如 activity → category, list）
- 路由配置示例：
  ```javascript
  {
    path: 'activity',
    name: 'Activity',
    component: () => import('@/views/activity/index.vue'),
    redirect: '/activity/category',
    children: [
      {
        path: 'category',
        name: 'ActivityCategory',
        component: () => import('@/views/activity/category/index.vue')
      },
      {
        path: 'list',
        name: 'ActivityList',
        component: () => import('@/views/activity/list/index.vue')
      }
    ]
  }
  ```
- 父级路由组件应包含`<router-view />`用于显示子路由内容
- 所有页面均包含在主布局`layout/index.vue`中
- 登录页等特殊页面可以不包含在主布局中

## 14. 分页组件使用规范

- 项目中统一使用`SimplePagination`组件，位置：`src/components/SimplePagination.vue`
- 分页组件特性：圆形按钮设计、0.3秒平滑过渡动画、v-model双向绑定
- 标准使用方式：
  ```vue
  <SimplePagination v-model="pagination.page" :page-size="pagination.pageSize" :total="pagination.total" @change="handleCurrentChange" />
  ```
- 禁止在页面中重复定义分页样式，所有样式已封装在组件内
- 分页组件自动判断数据总数，仅在有数据时显示

## 15. 组件封装与复用规范

- 当发现3个或以上页面使用相同功能时，必须封装为可复用组件
- 组件封装原则：
  - 单一职责：每个组件只负责一个特定功能
  - 可配置性：通过props提供必要的配置选项
  - 事件通信：使用emit向父组件传递事件
  - 完整文档：组件头部必须包含使用说明和示例
- 组件命名：使用PascalCase，名称要能清晰表达功能（如`SimplePagination`、`FilterCard`）
- 组件存放：通用组件放在`src/components/`，模块专用组件放在对应模块的`components/`子目录

## 16. 代码重构与去重规范

- 定期检查项目中的重复代码，特别是CSS样式和组件逻辑
- 重复代码处理策略：
  - 样式重复：提取到全局样式文件或封装为组件
  - 逻辑重复：封装为composables或工具函数
  - 组件重复：创建通用组件库
- 重构后必须删除原有重复代码，保持代码库整洁
- 重构时保留注释说明变更原因（如："// 样式已封装到SimplePagination组件中"）

## 17. 过渡动画规范

- 统一使用0.3秒的过渡时间：`transition: all 0.3s ease`
- 常用动画类型：
  - 按钮悬停：背景色和文字颜色过渡
  - 分页按钮：背景从透明到蓝色的平滑过渡
  - 页面切换：淡入淡出效果
- 过渡效果应该提升用户体验，避免过于复杂的动画
- 禁用状态的元素不应有悬停动画效果

## 18. 全局样式管理规范

- 全局通用样式统一放在`src/assets/styles/global.scss`中
- 全局样式命名规范：
  - 使用语义化的类名（如`.filter-actions`、`.table-card`）
  - 避免过于具体的样式名称
- 页面特定样式保留在组件的`<style>`标签中
- 删除重复样式时必须确认没有其他页面依赖
- 样式修改后要测试所有相关页面，确保不影响现有功能

## 19. 项目维护与优化规范

- 新功能开发前，优先检查是否有现成的组件可以复用
- 定期审查代码库，识别可以进一步封装的重复模式
- 性能优化：合并重复的网络请求、优化大组件的渲染性能
- 保持项目文档更新，记录重要的架构决策和组件使用方式

## 20. 严格执行规范

严格按照以上所有规范进行开发，确保项目代码的一致性、可维护性和高质量。

<!-- 规范持续更新中 -->

# 李白眼镜小程序项目规范

## 1. 项目初始步骤

开始新任务时，先熟悉整个项目结构并分析项目内容，确保对代码有整体了解，并以中文的形式告诉我你知道了什么。

## 2. 组件命名与存放规则

- 通用可复用组件统一存放在全局`components`文件夹中，按功能模块分类存放（如`components/Button.vue`）

## 3. 沟通规范

在修改代码后，用中文描述做了哪些修改，让团队成员容易理解变更内容。

## 4. 数据一致性

Mock数据的编写风格要与现有的home等页面保持一致，保证测试数据格式统一，Mock数据的接口地址不能和路由地址一致。

## 5. 按钮样式规范

- 重置按钮：背景色`#F2F3F5`，悬停色`#E5E7EB`
- 查询按钮：背景色`#165DFF`
- 新增按钮：背景色`#00B42B`
- 所有按钮在鼠标悬停时不显示边框，且有高光
- 按钮高度：`40px`
- 按钮内边距：`8px 16px`
- 按钮字体大小：`16px`
- 按钮图标与文字间距：`4px`
- 适用按钮：重置、查询、新增、保存、取消等

## 6. 输入框样式规范

- 输入框高度：`42px`（包含2px边框）
- 输入框聚焦边框颜色：`rgb(22,93,225)`
- 下拉框样式保持现有形式

## 7. 搜索栏样式规范

- 上下搜索栏之间间隔：`14px`
- 同一行搜索栏之间间隔：`14px`
- 搜索框内边距：`20px`
- 标签样式：
  ```css
  .search-label {
    font-size: 14px;
    color: #86909c;
    margin-bottom: 4px;
  }
  ```
- 提示文字(placeholder)颜色：`#9CA3AF`
- 布局方式：上下布局
- 按钮悬停效果：重置按钮变为`#E5E7EB`，其他按钮有高光效果
- 字体颜色在悬停时保持不变

## 8. 卡片样式及圆角规范

- 卡片组件统一圆角：`8px`
- 输入框圆角：`6px`
- 按钮圆角：`6px`
- 卡片之间间隔：`24px`
- 卡片与标题之间间隔：`24px`

## 9. CSS与图标规范

- 所有CSS采用SASS开发
- 项目中的图标已全局注册，使用时无需重复引入
- 对于SASS变量只有确认需要修改时才进行修改

## 10. 组件内部命名规范

在组件内部定义变量、方法等时，使用小驼峰命名法（如`getUserInfo`、`searchForm`）。

## 11. 开发前准备

开始编写页面前，先熟悉项目的SASS全局变量配置，确保样式一致性。

## 12. 重置按钮行为规范

- 重置按钮点击后应立即恢复到未点击状态
- 重置按钮点击后不应被禁用，保持可点击状态
- 实现方法：
  - 使用`:active`伪类设置样式恢复原状态
  - 在点击处理函数中设置`isResetDisabled = false`
  - 使用`document.activeElement.blur()`强制移除焦点
  - 添加全局点击事件监听确保按钮随时可点击

## 13. 路由与视图结构规范

- `views`文件夹中的结构应与路由结构相对应
- 采用层级嵌套结构，每个导航菜单对应一个顶层路由
- 父路由可以包含子路由（如 activity → category, list）
- 路由配置示例：
  ```javascript
  {
    path: 'activity',
    name: 'Activity',
    component: () => import('@/views/activity/index.vue'),
    redirect: '/activity/category',
    children: [
      {
        path: 'category',
        name: 'ActivityCategory',
        component: () => import('@/views/activity/category/index.vue')
      },
      {
        path: 'list',
        name: 'ActivityList',
        component: () => import('@/views/activity/list/index.vue')
      }
    ]
  }
  ```
- 父级路由组件应包含`<router-view />`用于显示子路由内容
- 所有页面均包含在主布局`layout/index.vue`中
- 登录页等特殊页面可以不包含在主布局中

## 14. 分页组件使用规范

- 项目中统一使用`SimplePagination`组件，位置：`src/components/SimplePagination.vue`
- 分页组件特性：圆形按钮设计、0.3秒平滑过渡动画、v-model双向绑定
- 标准使用方式：
  ```vue
  <SimplePagination v-model="pagination.page" :page-size="pagination.pageSize" :total="pagination.total" @change="handleCurrentChange" />
  ```
- 禁止在页面中重复定义分页样式，所有样式已封装在组件内
- 分页组件自动判断数据总数，仅在有数据时显示

## 15. 组件封装与复用规范

- 当发现3个或以上页面使用相同功能时，必须封装为可复用组件
- 组件封装原则：
  - 单一职责：每个组件只负责一个特定功能
  - 可配置性：通过props提供必要的配置选项
  - 事件通信：使用emit向父组件传递事件
  - 完整文档：组件头部必须包含使用说明和示例
- 组件命名：使用PascalCase，名称要能清晰表达功能（如`SimplePagination`、`FilterCard`）
- 组件存放：通用组件放在`src/components/`，模块专用组件放在对应模块的`components/`子目录

## 16. 代码重构与去重规范

- 定期检查项目中的重复代码，特别是CSS样式和组件逻辑
- 重复代码处理策略：
  - 样式重复：提取到全局样式文件或封装为组件
  - 逻辑重复：封装为composables或工具函数
  - 组件重复：创建通用组件库
- 重构后必须删除原有重复代码，保持代码库整洁
- 重构时保留注释说明变更原因（如："// 样式已封装到SimplePagination组件中"）

## 17. 过渡动画规范

- 统一使用0.3秒的过渡时间：`transition: all 0.3s ease`
- 常用动画类型：
  - 按钮悬停：背景色和文字颜色过渡
  - 分页按钮：背景从透明到蓝色的平滑过渡
  - 页面切换：淡入淡出效果
- 过渡效果应该提升用户体验，避免过于复杂的动画
- 禁用状态的元素不应有悬停动画效果

## 18. 全局样式管理规范

- 全局通用样式统一放在`src/assets/styles/global.scss`中
- 全局样式命名规范：
  - 使用语义化的类名（如`.filter-actions`、`.table-card`）
  - 避免过于具体的样式名称
- 页面特定样式保留在组件的`<style>`标签中
- 删除重复样式时必须确认没有其他页面依赖
- 样式修改后要测试所有相关页面，确保不影响现有功能

## 19. 项目维护与优化规范

- 新功能开发前，优先检查是否有现成的组件可以复用
- 定期审查代码库，识别可以进一步封装的重复模式
- 性能优化：合并重复的网络请求、优化大组件的渲染性能
- 保持项目文档更新，记录重要的架构决策和组件使用方式

## 20. 严格执行规范

严格按照以上所有规范进行开发，确保项目代码的一致性、可维护性和高质量。

<!-- 规范持续更新中 -->
