// 颜色变量
$primary-color: #165dff;
$primary-light: #ecf5ff;
$primary-hover: #2e6dff;
$success-color: #17c13e;
$warning-color: #ff7d00;
$danger-color: #f53f3f;
$info-color: #86909c;
$placeholder-color: #9ca3af;

// 背景色
$bg-color: #f2f3f5;
$bg-color-light: #ffffff;
$bg-color-hover: #e5e7eb;

// 边框颜色
$border-color: #ebeef5;
$border-color-light: #eee;

// 文字颜色
$text-color: #363940;
$text-color-dark: #333333;
$text-color-light: #909399;

// 尺寸变量
$header-height: 60px;
$footer-height: 40px;
$aside-width: 256px;
$menu-item-height: 44px;

// 圆角变量 - 按照规范设置
$border-radius: 8px; // 卡片组件统一圆角 8px
$border-radius-sm: 6px; // 输入框圆角 6px
$border-radius-btn: 6px; // 按钮（重置，新增，查询等）圆角 6px

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;
$spacing-xl: 20px;
$spacing-xxl: 24px;
$card-spacing: 24px; // 卡片之间统一间隔 24px

// 按钮颜色
$reset-btn-color: #f2f3f5;
$reset-btn-hover-color: #e5e7eb;
$reset-btn-text-color: #86909c;
$query-btn-color: #165dff;
$add-btn-color: #00b42b;
$brand-color: #722ed1;

// 按钮规范 - 新增
$btn-height: 40px; // 按钮高度40px
$btn-padding: 8px 16px; // 按钮内边距
$btn-margin: 12px; // 按钮间左边距
$btn-font-size: 16px; // 按钮字体大小
$btn-icon-margin: 4px; // 按钮图标与文字的间距

// 输入框规范 - 新增
$input-height: 42px; // 输入框高度(包含2px边框)
$input-focus-color: rgb(22, 93, 255); // 输入框聚焦边框颜色

// 搜索栏配置
$search-spacing-vertical: 14px;
$search-spacing-horizontal: 14px;
$search-padding: 20px;
$search-label-color: #86909c;
$search-label-size: 14px;
$search-label-margin: 4px;

// 阴影
$box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

// 边框
$editor-border-color: #cccccc;
$editor-border-width: 2px;
