import request from '@/utils/request'

// 获取首页统计数据
export function getHomeStats() {
  return request({
    url: '/home/<USER>',
    method: 'get'
  })
}

// 获取用户趋势数据
export function getUserTrend() {
  return request({
    url: '/home/<USER>/trend',
    method: 'get'
  })
}

// 获取最新注册用户
export function getLatestUsers(params) {
  return request({
    url: '/home/<USER>/users',
    method: 'get',
    params
  })
}

// 获取最近访问用户
export function getLatestVisits() {
  return request({
    url: '/home/<USER>/visits',
    method: 'get'
  })
}

// 获取最近积分订单
export function getLatestOrders(params) {
  return request({
    url: '/home/<USER>/orders',
    method: 'get',
    params
  })
}
