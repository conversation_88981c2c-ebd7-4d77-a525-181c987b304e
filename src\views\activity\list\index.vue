<template>
  <div class="activity-list-page">
    <transition name="fade" mode="out-in">
      <div v-if="!showAddForm" key="list">
        <div class="page-header">
          <h2>活动列表</h2>
          <p>管理系统内所有活动</p>
        </div>

        <!-- 搜索筛选区域 -->
        <el-card class="filter-card" shadow="never">
          <div class="filter-row">
            <div class="filter-item">
              <div class="filter-label">活动主副标题</div>
              <el-input v-model="searchForm.name" placeholder="请输入活动主副标题" clearable></el-input>
            </div>

            <div class="filter-item">
              <div class="filter-label">关联门店</div>
              <el-select v-model="searchForm.store" placeholder="全部门店" clearable class="filter-select">
                <el-option v-for="item in storeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>

            <div class="filter-item">
              <div class="filter-label">首页类型</div>
              <el-select v-model="searchForm.category" placeholder="全部" clearable class="filter-select">
                <el-option v-for="item in categoryOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>

            <div class="filter-actions">
              <el-button @click="handleReset" plain class="reset-button" :disabled="isResetDisabled">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
              <el-button type="primary" @click="handleSearch" class="search-button">
                <el-icon><Search /></el-icon>
                查询
              </el-button>
              <el-button type="success" @click="showAddForm = true" class="add-button">
                <el-icon><Plus /></el-icon>
                新增活动
              </el-button>
            </div>
          </div>
        </el-card>

        <!-- 活动列表 -->
        <el-card class="activity-card" shadow="never">
          <el-table :data="activityList" style="width: 100%" v-loading="loading" :header-cell-style="tableHeaderStyle" :cell-style="tableCellStyle">
            <el-table-column label="活动缩略图" width="100">
              <template #default="scope">
                <div class="image-container">
                  <el-image :src="scope.row.image" fit="cover" class="activity-image" :preview-src-list="[scope.row.image]" :initial-index="0" preview-teleported></el-image>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="活动主副标题" min-width="150">
              <template #default="scope">
                <div class="activity-title">
                  <div class="main-title">{{ scope.row.name }}</div>
                  <div class="sub-title" v-if="scope.row.theme">{{ scope.row.theme }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="活动类型" width="100">
              <template #default="scope">
                <div class="activity-type-tag" :class="getTagClass(scope.row.type)">{{ scope.row.type }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="80">
              <template #default="scope">
                <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" @change="handleStatusChange(scope.row)"></el-switch>
              </template>
            </el-table-column>
            <el-table-column prop="sort" label="前端排序" width="80">
              <template #default="scope">
                <span>{{ scope.row.sort || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="store" label="关联门店" width="120"></el-table-column>
            <el-table-column prop="category" label="首页类型" width="150"></el-table-column>
            <el-table-column prop="creator" label="修改人" width="100"></el-table-column>
            <el-table-column prop="createTime" label="修改时间" width="120"></el-table-column>
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="scope">
                <el-button type="primary" link @click="handleView(scope.row)">
                  <el-icon><View /></el-icon>
                </el-button>
                <el-button type="primary" link @click="handleEdit(scope.row)">
                  <el-icon><Edit /></el-icon>
                </el-button>
                <el-button type="danger" link @click="handleDelete(scope.row)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 分页栏 - 移出卡片，采用简洁的数字分页形式 -->
        <SimplePagination v-model="pagination.page" :page-size="pagination.pageSize" :total="pagination.total" @change="handleCurrentChange" />
      </div>

      <ActivityForm v-else key="add-form" :is-edit="isEdit" :edit-data="editData" @cancel="handleCancelAdd" @submit="handleSubmitActivity" />
    </transition>

    <!-- 活动详情弹窗 -->
    <el-dialog v-model="showDetailDialog" title="活动详情" width="800px" :before-close="() => (showDetailDialog = false)">
      <div class="activity-detail" v-if="detailData.id">
        <div class="detail-row">
          <div class="detail-item">
            <span class="detail-label">活动主标题：</span>
            <span class="detail-value">{{ detailData.name }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">活动副标题：</span>
            <span class="detail-value">{{ detailData.theme || '-' }}</span>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <span class="detail-label">活动类型：</span>
            <div class="activity-type-tag" :class="getTagClass(detailData.type)">{{ detailData.type }}</div>
          </div>
          <div class="detail-item">
            <span class="detail-label">状态：</span>
            <span class="detail-value">{{ detailData.status === 1 ? '已上架' : '已下架' }}</span>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <span class="detail-label">关联门店：</span>
            <span class="detail-value">{{ detailData.store }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">首页类型：</span>
            <span class="detail-value">{{ detailData.category }}</span>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <span class="detail-label">前端排序：</span>
            <span class="detail-value">{{ detailData.sort || '-' }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">创建时间：</span>
            <span class="detail-value">{{ detailData.createTime }}</span>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item full-width">
            <span class="detail-label">活动图片：</span>
            <div class="detail-image" v-if="detailData.image">
              <el-image :src="detailData.image" fit="cover" class="preview-image" :preview-src-list="[detailData.image]" :initial-index="0" preview-teleported></el-image>
            </div>
            <span v-else class="detail-value">-</span>
          </div>
        </div>

        <div class="detail-row" v-if="detailData.description">
          <div class="detail-item full-width">
            <span class="detail-label">活动描述：</span>
            <span class="detail-value">{{ detailData.description }}</span>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
          <el-button type="primary" @click="handleEditFromDetail">编辑</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getActivityList, deleteActivity, addActivity, updateActivity, getActivityDetail, toggleActivityStatus } from '@/api/activity'
import ActivityForm from '@/views/activity/list/components/ActivityForm.vue'

import SimplePagination from '@/components/SimplePagination.vue'

// 搜索表单
const searchForm = reactive({
  name: '',
  store: '',
  category: ''
})

// 店铺选项
const storeOptions = ref([
  { value: '全部门店', label: '全部门店' },
  { value: '旗舰店', label: '旗舰店' },
  { value: '北京店', label: '北京店' },
  { value: '上海店', label: '上海店' },
  { value: '广州店', label: '广州店' }
])

// 分类选项
const categoryOptions = ref([
  { value: '全部', label: '全部' },
  { value: '首页 - 促销活动', label: '首页 - 促销活动' },
  { value: '首页 - 品牌活动', label: '首页 - 品牌活动' }
])

// 活动列表
const activityList = ref([])
const loading = ref(false)

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 5,
  total: 0
})

// 新增/编辑活动表单显示控制
const showAddForm = ref(false)
const isEdit = ref(false)
const editData = ref({})

// 活动详情弹窗
const showDetailDialog = ref(false)
const detailData = ref({})

// 重置按钮禁用状态
const isResetDisabled = ref(false)

// 表格表头样式
const tableHeaderStyle = {
  backgroundColor: '#FAFBFC',
  color: '#89929E',
  fontSize: '14px',
  fontWeight: '600',
  height: '50px'
}

// 表格单元格样式
const tableCellStyle = {
  padding: '12px 0',
  fontSize: '14px'
}

// 获取活动列表
const fetchActivityList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      name: searchForm.name,
      store: searchForm.store,
      category: searchForm.category
    }

    const res = await getActivityList(params)
    activityList.value = res.data.list
    pagination.total = res.data.total
  } catch (error) {
    console.error('获取活动列表失败:', error)
    ElMessage.error('获取活动列表失败')
  } finally {
    loading.value = false
  }
}

// 重置搜索条件
const handleReset = () => {
  // 确保按钮不会被禁用
  isResetDisabled.value = false

  searchForm.name = ''
  searchForm.store = ''
  searchForm.category = ''
  pagination.page = 1
  fetchActivityList()

  // 强制移除焦点
  document.activeElement.blur()
}

// 查询
const handleSearch = () => {
  pagination.page = 1
  fetchActivityList()
}

// 查看活动详情
const handleView = async (row) => {
  try {
    const res = await getActivityDetail(row.id)
    detailData.value = res.data
    showDetailDialog.value = true
  } catch (error) {
    console.error('获取活动详情失败:', error)
    ElMessage.error('获取活动详情失败')
  }
}

// 编辑活动
const handleEdit = async (row) => {
  try {
    console.log('开始编辑活动:', row) // 调试信息

    // 获取活动详情
    const res = await getActivityDetail(row.id)
    console.log('获取到的活动详情:', res.data) // 调试信息

    // 设置编辑状态和数据
    editData.value = { ...res.data }

    // 确保表单中有完整数据，设置默认值
    if (!editData.value.theme) editData.value.theme = ''
    if (!editData.value.content) editData.value.content = ''
    if (!editData.value.jumpType) editData.value.jumpType = '跳转图文详情'
    if (!editData.value.miniappPath) editData.value.miniappPath = ''
    if (!editData.value.sort) editData.value.sort = 1
    if (!editData.value.description) editData.value.description = ''

    // 确保ID存在
    if (!editData.value.id) {
      editData.value.id = row.id
    }

    console.log('设置的编辑数据:', editData.value) // 调试信息

    isEdit.value = true
    showAddForm.value = true
  } catch (error) {
    console.error('获取活动详情失败:', error)
    ElMessage.error('获取活动详情失败，请重试')
  }
}

// 删除活动
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认删除活动 "${row.name}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        await deleteActivity(row.id)
        ElMessage.success('删除成功')

        // 从本地列表中删除
        const index = activityList.value.findIndex((item) => item.id === row.id)
        if (index !== -1) {
          activityList.value.splice(index, 1)

          // 如果当前页没有数据了且不是第一页，则回到上一页
          if (activityList.value.length === 0 && pagination.page > 1) {
            pagination.page--
            fetchActivityList()
          } else {
            // 更新总数
            pagination.total--
          }
        }
      } catch (error) {
        ElMessage.error('删除失败')
        console.error('删除活动失败:', error)
      }
    })
    .catch(() => {})
}

// 状态切换
const handleStatusChange = async (row) => {
  try {
    await toggleActivityStatus(row.id, row.status)
    ElMessage.success(row.status === 1 ? '上架成功' : '下架成功')
  } catch (error) {
    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1
    ElMessage.error('操作失败')
    console.error('切换活动状态失败:', error)
  }
}

// 分页变化
const handleCurrentChange = (val) => {
  pagination.page = val
  fetchActivityList()
}

// 取消新增/编辑
const handleCancelAdd = () => {
  showAddForm.value = false
  // 重置编辑状态
  setTimeout(() => {
    isEdit.value = false
    editData.value = {}
  }, 300) // 等待过渡动画结束后再重置
}

// 提交活动表单
const handleSubmitActivity = async (formData) => {
  try {
    console.log('提交活动表单:', { isEdit: isEdit.value, formData }) // 调试信息

    if (isEdit.value) {
      // 编辑模式 - 确保formData包含ID
      if (!formData.id) {
        ElMessage.error('缺少活动ID，无法更新')
        return
      }

      await updateActivity(formData)
      ElMessage.success('更新成功')

      // 更新本地数据 - 使用formData.id而不是editData.value.id
      const index = activityList.value.findIndex((item) => item.id === formData.id)
      if (index !== -1) {
        // 保留原有的一些数据
        const originalData = {
          id: activityList.value[index].id,
          creator: activityList.value[index].creator,
          createTime: activityList.value[index].createTime
        }

        // 更新活动信息
        activityList.value[index] = {
          ...formData,
          ...originalData
        }
        console.log('本地数据已更新:', activityList.value[index]) // 调试信息
      } else {
        console.warn('未找到要更新的活动项，ID:', formData.id) // 调试信息
        // 如果本地没有找到，重新获取列表
        await fetchActivityList()
      }
    } else {
      // 新增模式
      const res = await addActivity(formData)
      ElMessage.success('添加成功')

      // 如果在第一页，直接添加到列表顶部
      if (pagination.page === 1) {
        const newActivity = {
          ...formData,
          id: res.data.id,
          creator: '管理员',
          createTime: new Date().toISOString().split('T')[0]
        }

        // 添加到本地列表顶部
        activityList.value.unshift(newActivity)

        // 如果列表超出了每页显示数量，移除最后一项
        if (activityList.value.length > pagination.pageSize) {
          activityList.value.pop()
        }

        // 更新总数
        pagination.total++
      } else {
        // 不在第一页时，跳转到第一页查看新添加的活动
        pagination.page = 1
        await fetchActivityList()
      }
    }

    // 关闭表单
    showAddForm.value = false

    // 重置编辑状态
    setTimeout(() => {
      isEdit.value = false
      editData.value = {}
    }, 300)
  } catch (error) {
    console.error('提交表单失败:', error)
    ElMessage.error(isEdit.value ? '更新失败，请检查数据是否正确' : '添加失败')
  }
}

// 获取活动类型对应的样式类
const getTagClass = (type) => {
  const tagClasses = {
    促销: 'promotion-tag',
    会员: 'member-tag',
    品牌: 'brand-tag'
  }
  return tagClasses[type] || 'default-tag'
}

// 从详情弹窗进入编辑模式
const handleEditFromDetail = () => {
  showDetailDialog.value = false
  handleEdit(detailData.value)
}

// 初始化
onMounted(async () => {
  fetchActivityList()

  // 添加全局点击事件，确保重置按钮在任何点击后都能恢复正常
  document.addEventListener('click', () => {
    isResetDisabled.value = false
  })
})

// 组件卸载时移除事件监听
onBeforeUnmount(() => {
  document.removeEventListener('click', () => {
    isResetDisabled.value = false
  })
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;
@use 'sass:color';

.activity-list-page {
  max-width: 1280px;
  margin: 0 auto;
  min-height: 500px;
  padding-bottom: 20px;
}

.page-header {
  margin-bottom: $card-spacing;

  h2 {
    font-size: 24px;
    margin: 0;
    font-weight: 600;
    color: $text-color-dark;
  }

  p {
    color: $text-color-light;
    margin: 5px 0 0;
  }
}

.filter-card {
  margin-bottom: $card-spacing;
  border-radius: $border-radius;

  :deep(.el-card__body) {
    padding: $search-padding;
    overflow: hidden;
  }
}

.activity-card {
  margin-bottom: 24px;
  border-radius: 8px;

  :deep(.el-card__body) {
    padding: 0;
    overflow: hidden;
  }

  :deep(.el-table) {
    border-radius: 8px;
    overflow: hidden;
  }

  :deep(.el-table th) {
    background-color: #fafbfc;
    color: #89929e;
    font-weight: 600;
    height: 50px;
  }

  :deep(.el-table td) {
    padding: 12px 0;
  }

  :deep(.el-table__row) {
    height: 62px;
  }
}

.filter-row {
  display: flex;
  align-items: flex-end;
  width: 100%;
  gap: $search-spacing-vertical;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 120px;

  &:last-child {
    margin-right: 0;
  }
}

.filter-label {
  font-size: $search-label-size;
  color: $search-label-color;
  margin-bottom: $search-label-margin;
}

.filter-select {
  width: 100%;
}

.filter-item {
  :deep(.el-input__inner),
  :deep(.el-select .el-input__inner) {
    color: $text-color;
  }

  :deep(.el-input__inner::placeholder),
  :deep(.el-select .el-input__inner::placeholder) {
    color: $placeholder-color;
  }
}

// 搜索栏按钮样式已移至全局样式，此处无需重复定义

// 分页样式已封装到SimplePagination组件中

/* 活动类型标签样式 */
.activity-type-tag {
  display: inline-block;
  padding: 0px 8px;
  border-radius: 30px;
  font-size: 12px;
  text-align: center;
}

/* 活动主副标题样式 */
.activity-title {
  display: flex;
  flex-direction: column;
}

.main-title {
  font-weight: 500;
  color: #1d2129;
}

.sub-title {
  font-size: 12px;
  color: #86909c;
}

.promotion-tag {
  color: $query-btn-color;
  background-color: color.adjust($query-btn-color, $lightness: 35%);
}

.member-tag {
  color: $add-btn-color;
  background-color: color.adjust($add-btn-color, $lightness: 50%);
}

.brand-tag {
  color: $brand-color;
  background-color: color.adjust($brand-color, $lightness: 35%);
}

.default-tag {
  color: $info-color;
  background-color: $reset-btn-color;
}

.image-container {
  width: 60px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  border-radius: 4px;
}

.activity-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

/* 确保图片预览弹窗正常显示 */
:deep(.el-image-viewer__wrapper) {
  z-index: 2050;
}

:deep(.el-image-viewer__mask) {
  z-index: 2049;
}

/* 淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

@media (max-width: 992px) {
  .filter-row {
    gap: 8px;
  }

  .filter-item {
    min-width: 100px;
  }

  .filter-actions {
    min-width: 200px;
    gap: 5px;
  }
}

@media (max-width: 768px) {
  .filter-item {
    flex: 0 0 100%;
  }

  .filter-actions {
    width: 100%;
    margin-top: 10px;
    justify-content: flex-end;
  }
}

/* 活动详情弹窗样式 */
.activity-detail {
  padding: 20px 0;

  .detail-row {
    display: flex;
    margin-bottom: 16px;
    gap: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .detail-item {
    flex: 1;
    display: flex;
    align-items: center;

    &.full-width {
      flex: 0 0 100%;
      flex-direction: column;
      align-items: flex-start;
    }
  }

  .detail-label {
    font-weight: 600;
    color: #1d2129;
    min-width: 100px;
    margin-right: 8px;
  }

  .detail-value {
    color: #86909c;
    word-break: break-all;
  }

  .detail-image {
    width: 200px;
    height: 120px;
    border-radius: 6px;
    overflow: hidden;
    margin-top: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .preview-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      cursor: pointer;
    }
  }

  .activity-type-tag {
    margin-left: 0;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
