<template>
  <div>
    <div class="page-header">
      <h2>修改轮播图</h2>
      <p>管理轮播图信息和排序</p>
    </div>

    <el-card class="banner-edit-card" shadow="never">
      <!-- 第一栏：轮播图类型（占满一行） -->
      <div class="banner-type-section">
        <div class="form-item">
          <div class="filter-label">轮播图类型</div>
          <el-select v-model="bannerConfig.type" placeholder="请选择轮播图类型" class="type-select" @change="handleTypeChange">
            <el-option v-for="item in bannerTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </div>
      </div>

      <!-- 第二栏：轮播图配置左侧，添加按钮右侧 -->
      <div class="banner-config-section">
        <span class="config-title">轮播图配置</span>
        <el-button type="primary" @click="addBannerItem" :disabled="bannerList.length >= 6 || !bannerConfig.type" class="add-button">
          <el-icon><Plus /></el-icon>
          添加
        </el-button>
      </div>

      <!-- 轮播图表格 -->
      <div class="banner-table-section" style="margin-top: 10px">
        <el-table :data="bannerList" style="width: 100%" :header-cell-style="tableHeaderStyle" :cell-style="tableCellStyle" border>
          <!-- 上传图片/视频 -->
          <el-table-column label="上传图片/视频" width="220" align="center">
            <template #default="scope">
              <div class="upload-cell">
                <div v-if="scope.row.image" class="image-preview-wrapper">
                  <el-image :src="scope.row.image" fit="cover" class="preview-image" :preview-src-list="[scope.row.image]" :initial-index="0" preview-teleported />
                  <div class="close-button" @click="removeImage(scope.$index)">
                    <el-icon><Close /></el-icon>
                  </div>
                </div>
                <el-upload v-else class="upload-box" drag action="#" :auto-upload="false" :http-request="(options) => uploadImage(options, scope.$index)" :show-file-list="false" :before-upload="beforeUpload" accept=".jpg,.jpeg,.png,.gif,.mp4,.mov">
                  <div class="upload-content">
                    <el-icon class="upload-icon"><UploadFilled /></el-icon>
                    <div class="upload-text">点击上传图片</div>
                    <div class="upload-tip">支持JPG、PNG格式</div>
                  </div>
                </el-upload>
              </div>
            </template>
          </el-table-column>

          <!-- 预览 -->
          <el-table-column label="预览" width="180" align="center">
            <template #default="scope">
              <el-button v-if="scope.row.image" type="primary" link @click="previewImage(scope.row.image)">
                <el-icon><View /></el-icon>
                预览
              </el-button>
              <span v-else class="text-placeholder">暂无预览</span>
            </template>
          </el-table-column>

          <!-- 上传背景图/视频 (仅首页顶部轮播图显示) -->
          <el-table-column v-if="bannerConfig.type === '首页顶部轮播图'" label="上传背景图/视频" width="220" align="center">
            <template #default="scope">
              <div class="upload-cell">
                <div v-if="scope.row.backgroundImage" class="image-preview-wrapper">
                  <el-image :src="scope.row.backgroundImage" fit="cover" class="preview-image" :preview-src-list="[scope.row.backgroundImage]" :initial-index="0" preview-teleported />
                  <div class="close-button" @click="removeBackgroundImage(scope.$index)">
                    <el-icon><Close /></el-icon>
                  </div>
                </div>
                <el-upload v-else class="upload-box" drag action="#" :auto-upload="false" :http-request="(options) => uploadBackgroundImage(options, scope.$index)" :show-file-list="false" :before-upload="beforeUpload" accept=".jpg,.jpeg,.png,.gif,.mp4,.mov">
                  <div class="upload-content">
                    <el-icon class="upload-icon"><UploadFilled /></el-icon>
                    <div class="upload-text">点击上传背景图</div>
                    <div class="upload-tip">支持JPG、PNG格式</div>
                  </div>
                </el-upload>
              </div>
            </template>
          </el-table-column>

          <!-- 背景图预览 (仅首页顶部轮播图显示) -->
          <el-table-column v-if="bannerConfig.type === '首页顶部轮播图'" label="预览" width="180" align="center">
            <template #default="scope">
              <el-button v-if="scope.row.backgroundImage" type="primary" link @click="previewImage(scope.row.backgroundImage)">
                <el-icon><View /></el-icon>
                预览
              </el-button>
              <span v-else class="text-placeholder">暂无预览</span>
            </template>
          </el-table-column>

          <!-- 配置链接 -->
          <el-table-column label="配置链接" min-width="400">
            <template #default="scope">
              <div class="link-config-row">
                <!-- 链接类型选择 -->
                <el-select v-model="scope.row.linkType" placeholder="请选择跳转类型" class="link-type-select" @change="handleLinkTypeChange(scope.$index)">
                  <el-option label="不跳转" value="none"></el-option>
                  <el-option label="跳转活动图文详情" value="activity"></el-option>
                  <el-option label="跳转三方H5页面" value="h5"></el-option>
                  <el-option label="跳转产品详情页" value="product"></el-option>
                </el-select>

                <!-- 活动选择 -->
                <el-select v-if="scope.row.linkType === 'activity'" v-model="scope.row.activityId" placeholder="请选择活动" class="link-detail-select">
                  <el-option v-for="activity in activityOptions" :key="activity.value" :label="activity.label" :value="activity.value"></el-option>
                </el-select>

                <!-- H5页面URL输入 -->
                <el-input v-if="scope.row.linkType === 'h5'" v-model="scope.row.h5Url" placeholder="请输入H5页面链接" class="link-detail-input" />

                <!-- 产品选择 -->
                <el-select v-if="scope.row.linkType === 'product'" v-model="scope.row.productId" placeholder="请选择产品" class="link-detail-select">
                  <el-option v-for="product in productOptions" :key="product.value" :label="product.label" :value="product.value"></el-option>
                </el-select>
              </div>
            </template>
          </el-table-column>

          <!-- 前端排序 -->
          <el-table-column label="前端排序(数字越小操作越靠前)" width="200" align="center">
            <template #default="scope">
              <el-select v-model="scope.row.sort" placeholder="请选择排序" @change="handleSortChange">
                <el-option v-for="i in 6" :key="i" :label="i" :value="i"></el-option>
              </el-select>
            </template>
          </el-table-column>

          <!-- 操作 -->
          <el-table-column label="操作" width="80" align="center">
            <template #default="scope">
              <el-icon class="delete-icon" @click="removeBannerItem(scope.$index)"><Delete /></el-icon>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 底部说明 -->
      <div class="upload-tips" style="margin-top: 10px">
        <p>[可上传图片或视频类型为1-6张，点击上传]</p>
        <p>[图片支持.jpg、.png、.gif、.jpeg格式，单张不超过2MB]</p>
        <p>视频支持.mv、.rm、.rmvb、.mp4、.3gp、.mov、.m4v、.avi、.dat、.mkv、.flv、.vob格式，不超过10MB]</p>
      </div>

      <!-- 按钮区域 -->
      <div class="form-actions">
        <el-button @click="handleCancel" plain class="cancel-button">
          <el-icon><Close /></el-icon>
          取消
        </el-button>
        <el-button @click="handleReset" plain class="reset-button">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
        <el-button type="primary" @click="handleSave" class="save-button">
          <el-icon><Check /></el-icon>
          保存
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Close, View, Delete, Check, Refresh, UploadFilled } from '@element-plus/icons-vue'
import { saveBannerConfig, getBannerConfig } from '@/api/banner'

// 定义组件事件
const emit = defineEmits(['cancel', 'save'])

// 轮播图类型选项
const bannerTypeOptions = ref([
  { value: '首页顶部轮播图', label: '首页顶部轮播图' },
  { value: '首页顶部背景图', label: '首页顶部背景图' },
  { value: '查验真伪引导图', label: '查验真伪引导图' },
  { value: '积分领取引导图', label: '积分领取引导图' },
  { value: '产品页小白鸽系列广告图', label: '产品页小白鸽系列广告图' },
  { value: '产品页皛系列广告图', label: '产品页皛系列广告图' },
  { value: '产品页侠系列广告图', label: '产品页侠系列广告图' },
  { value: '产品页度系列广告图', label: '产品页度系列广告图' },
  { value: '产品页方圆正道广告图', label: '产品页方圆正道广告图' },
  { value: '产品页猜系列广告图', label: '产品页猜系列广告图' },
  { value: '产品页小圆满广告图', label: '产品页小圆满广告图' }
])

// 轮播图配置
const bannerConfig = reactive({
  type: ''
})

// 轮播图列表
const bannerList = ref([])

// 活动选项
const activityOptions = ref([
  { value: 'activity1', label: '跳转活动图文详情' },
  { value: 'activity2', label: '图文详情1' },
  { value: 'activity3', label: '图文详情2' }
])

// 产品选项
const productOptions = ref([
  { value: 'product1', label: '跳转产品详情页' },
  { value: 'product2', label: '产品详情1' },
  { value: 'product3', label: '产品详情2' }
])

// 表格样式
const tableHeaderStyle = {
  backgroundColor: '#FAFBFC',
  color: '#89929E',
  fontSize: '14px',
  fontWeight: '600',
  height: '50px'
}

const tableCellStyle = {
  padding: '12px 0',
  fontSize: '14px'
}

// 添加轮播图项
const addBannerItem = () => {
  if (bannerList.value.length >= 6) {
    ElMessage.warning('最多只能添加6个轮播图')
    return
  }

  const newItem = {
    image: '',
    backgroundImage: '',
    linkType: 'none',
    activityId: '',
    h5Url: '',
    productId: '',
    sort: bannerList.value.length + 1
  }

  bannerList.value.push(newItem)
}

// 删除轮播图项
const removeBannerItem = (index) => {
  ElMessageBox.confirm('确定要删除这个轮播图吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      bannerList.value.splice(index, 1)
      // 重新排序
      bannerList.value.forEach((item, idx) => {
        if (item.sort > bannerList.value.length) {
          item.sort = idx + 1
        }
      })
      ElMessage.success('删除成功')
    })
    .catch(() => {})
}

// 轮播图类型变化处理
const handleTypeChange = () => {
  // 切换类型时置空表格数据
  bannerList.value = []
}

// 链接类型变化处理
const handleLinkTypeChange = (index) => {
  // 清空之前的链接配置
  bannerList.value[index].activityId = ''
  bannerList.value[index].h5Url = ''
  bannerList.value[index].productId = ''
}

// 上传前检查
const beforeUpload = (file) => {
  const isImage = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'].includes(file.type)
  const isVideo = ['video/mp4', 'video/mov', 'video/avi', 'video/mkv', 'video/flv'].includes(file.type)

  if (!isImage && !isVideo) {
    ElMessage.error('只支持图片(JPG、PNG、GIF)或视频(MP4、MOV、AVI、MKV、FLV)格式!')
    return false
  }

  if (isImage) {
    const isLt2M = file.size / 1024 / 1024 < 2
    if (!isLt2M) {
      ElMessage.error('图片大小不能超过 2MB!')
      return false
    }
  }

  if (isVideo) {
    const isLt10M = file.size / 1024 / 1024 < 10
    if (!isLt10M) {
      ElMessage.error('视频大小不能超过 10MB!')
      return false
    }
  }

  return true
}

// 上传主图片
const uploadImage = (options, index) => {
  const reader = new FileReader()
  reader.readAsDataURL(options.file)
  reader.onload = (e) => {
    bannerList.value[index].image = e.target.result
  }
}

// 删除主图片
const removeImage = (index) => {
  ElMessageBox.confirm('确定要删除这张图片吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      bannerList.value[index].image = ''
      ElMessage.success('图片已删除')
    })
    .catch(() => {})
}

// 上传背景图片
const uploadBackgroundImage = (options, index) => {
  const reader = new FileReader()
  reader.readAsDataURL(options.file)
  reader.onload = (e) => {
    bannerList.value[index].backgroundImage = e.target.result
  }
}

// 删除背景图片
const removeBackgroundImage = (index) => {
  ElMessageBox.confirm('确定要删除这张背景图片吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      bannerList.value[index].backgroundImage = ''
      ElMessage.success('背景图片已删除')
    })
    .catch(() => {})
}

// 预览图片
const previewImage = (imageSrc) => {
  // 使用Element Plus的图片预览功能，这里可以扩展为独立的预览弹窗
  console.log('预览图片:', imageSrc)
}

// 排序变化处理
const handleSortChange = () => {
  // 检查排序重复
  const sorts = bannerList.value.map((item) => item.sort)
  const uniqueSorts = [...new Set(sorts)]

  if (sorts.length !== uniqueSorts.length) {
    ElMessage.warning('排序数字不能重复，请重新选择')
    // 可以在这里实现自动调整重复排序的逻辑
  }
}

// 取消
const handleCancel = () => {
  ElMessageBox.confirm('确定要取消修改吗？未保存的更改将会丢失。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '继续编辑',
    type: 'warning'
  })
    .then(() => {
      emit('cancel')
    })
    .catch(() => {})
}

// 重置
const handleReset = () => {
  ElMessageBox.confirm('确定要重置所有内容吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      bannerConfig.type = ''
      bannerList.value = []
      ElMessage.success('重置成功')
    })
    .catch(() => {})
}

// 保存
const handleSave = async () => {
  if (!bannerConfig.type) {
    ElMessage.warning('请选择轮播图类型')
    return
  }

  if (bannerList.value.length === 0) {
    ElMessage.warning('请至少添加一个轮播图')
    return
  }

  // 验证必填项
  const hasEmptyImage = bannerList.value.some((item) => !item.image)
  if (hasEmptyImage) {
    ElMessage.warning('请为所有轮播图上传图片')
    return
  }

  // 验证链接配置
  const hasInvalidLink = bannerList.value.some((item) => {
    if (item.linkType === 'activity' && !item.activityId) {
      return true
    }
    if (item.linkType === 'h5' && !item.h5Url) {
      return true
    }
    if (item.linkType === 'product' && !item.productId) {
      return true
    }
    return false
  })
  if (hasInvalidLink) {
    ElMessage.warning('请完善所有链接配置')
    return
  }

  // 如果是首页顶部轮播图，验证背景图
  if (bannerConfig.type === '首页顶部轮播图') {
    const hasEmptyBackground = bannerList.value.some((item) => !item.backgroundImage)
    if (hasEmptyBackground) {
      ElMessage.warning('首页顶部轮播图需要上传背景图')
      return
    }
  }

  const submitData = {
    type: bannerConfig.type,
    banners: bannerList.value
  }

  try {
    const res = await saveBannerConfig(submitData)
    if (res.code === 200) {
      ElMessage.success('保存成功')
      emit('save', submitData)
    } else {
      ElMessage.error(res.message || '保存失败')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请重试')
  }
}

// 组件挂载时的初始化
onMounted(() => {
  // 可以在这里加载现有的轮播图数据
  console.log('轮播图编辑页面已加载')
})
</script>

<style lang="scss" scoped>
.page-header {
  margin-bottom: 24px;

  h2 {
    font-size: 24px;
    margin: 0 0 8px 0;
    font-weight: 600;
  }

  p {
    color: #86909c;
    margin: 0;
  }
}

.banner-edit-card {
  margin-bottom: 24px;
  border-radius: 8px;

  :deep(.el-card__body) {
    padding: 20px;
  }
}

.banner-type-section {
  margin-bottom: 24px;

  .form-item {
    .filter-label {
      font-size: 14px;
      color: #86909c;
      margin-bottom: 4px;
    }

    .type-select {
      width: 100%;
    }
  }
}

.banner-config-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;

  .config-title {
    font-size: 16px;
    font-weight: 600;
    color: #1d2129;
  }

  .add-button {
    height: 40px;
    padding: 8px 16px;
    font-size: 16px;
    display: flex;
    align-items: center;
    background-color: #165dff;

    .el-icon {
      margin-right: 4px;
    }

    &:hover {
      opacity: 0.8;
      background-color: #165dff;
    }

    &:disabled {
      opacity: 0.5;
      background-color: #165dff;
    }
  }
}

.banner-table-section {
  margin-bottom: 24px;

  .upload-cell {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    padding: 10px 0;
  }

  .image-preview-wrapper {
    width: 192px;
    height: 108px; /* 16:9 比例，60%尺寸 */
    position: relative;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
  }

  .preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
  }

  .close-button {
    position: absolute;
    top: 0;
    right: 0;
    width: 24px;
    height: 24px;
    background-color: #f53f3f; /* 红色背景 */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease; /* 只保留背景色过渡 */

    &:hover {
      background-color: #d93026; /* 悬停时稍微深一点的红色 */
    }

    .el-icon {
      color: white;
      font-size: 12px;
    }
  }

  .upload-box {
    width: 192px;
    height: 108px; /* 16:9 比例，60%尺寸 */
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: 2px dashed #d0d7de; /* 虚线边框 */
    transition: border-color 0.3s ease;

    &:hover {
      border-color: #165dff; /* 悬停时蓝色边框 */
    }

    :deep(.el-upload-dragger) {
      border: none;
      background-color: transparent;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 6px;
      cursor: pointer;
    }
  }

  .upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
  }

  .upload-icon {
    font-size: 36px;
    color: #c0c4cc;
    margin-bottom: 12px;
  }

  .upload-text {
    color: #606266;
    font-size: 14px;
    margin-bottom: 8px;
    line-height: 1.4;

    em {
      color: #165dff;
      font-style: normal;
    }
  }

  .upload-tip {
    color: #9ca3af;
    font-size: 12px;
    line-height: 1.4;
    margin-top: 4px;
  }

  .text-placeholder {
    color: #c0c4cc;
    font-size: 14px;
  }

  .delete-icon {
    color: #f53f3f;
    font-size: 18px;
    cursor: pointer;
    transition: color 0.3s ease;

    &:hover {
      color: #d93026;
    }
  }
}

.upload-tips {
  color: #86909c;
  font-size: 12px;
  margin-bottom: 20px;
  margin-left: 12px;

  p {
    margin: 0 0 4px 0;
    line-height: 1.4;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0;

  .el-button {
    height: 40px;
    padding: 8px 16px;
    font-size: 16px;
    display: flex;
    align-items: center;
    border: none;

    &:not(:first-child) {
      margin-left: 12px;
    }

    .el-icon {
      margin-right: 4px;
    }
  }

  .cancel-button,
  .reset-button {
    background-color: #f2f3f5;
    color: #606266;

    &:hover {
      background-color: #e5e7eb;
      border-color: transparent;
      color: #606266;
    }
  }

  .save-button {
    background-color: #165dff;

    &:hover {
      opacity: 0.8;
      background-color: #165dff;
    }
  }
}

// 表格内的选择框样式
:deep(.el-table .el-select) {
  width: 100%;
}

:deep(.el-table .el-input) {
  width: 100%;
}

// 链接配置行样式
.link-config-row {
  display: flex;
  align-items: center;
  gap: 14px;
  width: 100%;

  .link-type-select {
    flex: 1;
  }

  .link-detail-select {
    flex: 1;
  }

  .link-detail-input {
    flex: 1;
  }
}
</style>
