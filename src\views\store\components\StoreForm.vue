<template>
  <div class="add-store-form">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑门店' : '新增门店' }}</h2>
      <p>{{ isEdit ? '编辑门店信息' : '填写以下信息以新增门店' }}</p>
    </div>

    <el-card class="form-card" shadow="never">
      <el-form :model="storeForm" ref="storeFormRef" :rules="formRules" label-width="0">
        <!-- 门店联系人姓名 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">门店联系人姓名</div>
            <el-form-item prop="contactName" class="no-label-form-item">
              <el-input v-model="storeForm.contactName" placeholder="请输入门店联系人姓名"></el-input>
            </el-form-item>
          </div>
        </div>

        <!-- 门店联系人电话 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">门店联系人电话</div>
            <el-form-item prop="contactPhone" class="no-label-form-item">
              <el-input v-model="storeForm.contactPhone" placeholder="请输入门店联系人电话"></el-input>
            </el-form-item>
          </div>
        </div>

        <!-- 上传门店banner图 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">上传门店banner图（1-6张）</div>
            <el-form-item prop="bannerImages" class="no-label-form-item">
              <div class="upload-container">
                <div class="image-upload-grid">
                  <!-- 已上传的图片 -->
                  <div v-for="(image, index) in previewImages" :key="index" class="image-preview-wrapper">
                    <el-image :src="image" fit="cover" class="preview-image" :preview-src-list="[image]" :initial-index="0" preview-teleported></el-image>
                    <div class="close-button" @click="removeImage(index)">
                      <el-icon><Close /></el-icon>
                    </div>
                  </div>
                  <!-- 上传框 -->
                  <el-upload v-if="previewImages.length < 6" class="upload-box" drag action="#" :auto-upload="false" :show-file-list="false" :limit="6" multiple :on-exceed="handleExceed" accept=".jpg,.jpeg,.png,.gif" :on-change="handleImageUploadChange">
                    <div class="upload-content">
                      <el-icon class="upload-icon"><UploadFilled /></el-icon>
                      <div class="upload-text">点击上传图片</div>
                      <div class="upload-tip">支持JPG、PNG格式</div>
                    </div>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </div>
        </div>

        <!-- 门店名称 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">门店名称</div>
            <el-form-item prop="name" class="no-label-form-item">
              <el-input v-model="storeForm.name" placeholder="请输入门店名称"></el-input>
            </el-form-item>
          </div>
        </div>

        <!-- 特色标签 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">选择或添加特色标签</div>
            <el-form-item prop="tags" class="no-label-form-item">
              <el-input v-model="storeForm.tags" placeholder="请输入特色标签，多个标签用逗号分隔" type="textarea" :rows="2"></el-input>
            </el-form-item>
          </div>
        </div>

        <!-- 营业时间 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">设置门店营业时间</div>
            <div class="time-range">
              <el-time-picker v-model="storeForm.openTime" placeholder="开始时间" format="HH:mm" value-format="HH:mm" style="width: 49%"></el-time-picker>
              <span class="time-separator">至</span>
              <el-time-picker v-model="storeForm.closeTime" placeholder="结束时间" format="HH:mm" value-format="HH:mm" style="width: 49%"></el-time-picker>
            </div>
          </div>
        </div>

        <!-- 所属地区 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">所属地区</div>
            <div class="area-selects">
              <el-select v-model="storeForm.province" placeholder="请选择省份" clearable style="width: 32%" @change="handleProvinceChange">
                <el-option v-for="item in provinceOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
              <el-select v-model="storeForm.city" placeholder="请选择城市" clearable style="width: 32%" :disabled="!storeForm.province" @change="handleCityChange">
                <el-option v-for="item in cityOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
              <el-select v-model="storeForm.district" placeholder="请选择区/县" clearable style="width: 32%" :disabled="!storeForm.city">
                <el-option v-for="item in districtOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>
          </div>
        </div>

        <!-- 详细地址 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">详细地址</div>
            <el-form-item prop="address" class="no-label-form-item">
              <el-input v-model="storeForm.address" placeholder="请输入详细地址" type="textarea" :rows="3"></el-input>
            </el-form-item>
          </div>
        </div>

        <!-- 门店联系电话 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">门店联系电话</div>
            <el-form-item prop="phone" class="no-label-form-item">
              <el-input v-model="storeForm.phone" placeholder="请输入门店联系电话"></el-input>
            </el-form-item>
          </div>
        </div>

        <!-- 选择眼光设备 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">选择眼光设备</div>
            <div class="equipment-grid">
              <div v-for="(item, index) in equipmentOptions" :key="index" class="equipment-item" @click="toggleEquipment(item.id)">
                <div class="equipment-name">{{ item.name }}</div>
                <div v-if="selectedEquipment.includes(item.id)" class="equipment-selected-icon">
                  <el-icon><Check /></el-icon>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 团队成员 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">团队成员（1-5人）</div>
            <div class="team-container">
              <div class="team-title">团队成员信息</div>
              <div class="team-members-list">
                <div v-for="(member, index) in storeForm.teamMembers" :key="index" class="team-member-item">
                  <!-- 成员图片 -->
                  <div class="member-image-container">
                    <div class="filter-label">成员图片</div>
                    <div class="member-image-upload">
                      <div v-if="member.image" class="member-image-preview">
                        <el-image :src="member.image" fit="cover" class="member-preview-image"></el-image>
                        <div class="close-button" @click="confirmRemoveMemberImage(index)">
                          <el-icon><Close /></el-icon>
                        </div>
                      </div>
                      <el-upload v-else class="member-upload-box" drag action="#" :auto-upload="false" :show-file-list="false" accept=".jpg,.jpeg,.png,.gif" :on-change="(file) => handleMemberImageUpload(file, index)">
                        <div class="upload-content">
                          <el-icon class="upload-icon"><UploadFilled /></el-icon>
                          <div class="upload-text">点击上传图片</div>
                          <div class="upload-tip">支持JPG、PNG格式</div>
                        </div>
                      </el-upload>
                    </div>
                  </div>

                  <!-- 成员信息 -->
                  <div class="member-info">
                    <div class="member-name-input">
                      <div class="filter-label">成员姓名</div>
                      <el-input v-model="member.name" placeholder="请输入成员姓名"></el-input>
                    </div>
                    <div class="member-desc-input">
                      <div class="filter-label">成员介绍</div>
                      <el-input v-model="member.description" type="textarea" :rows="2" placeholder="请输入成员介绍"></el-input>
                    </div>
                  </div>

                  <!-- 删除成员按钮 -->
                  <div v-if="storeForm.teamMembers.length > 1" class="delete-member-btn" @click="confirmRemoveMember(index)">
                    <el-icon><Delete /></el-icon>
                  </div>
                </div>
              </div>
            </div>

            <!-- 添加成员按钮 -->
            <el-button v-if="storeForm.teamMembers.length < 5" type="primary" class="add-member-btn" @click="addTeamMember">
              <el-icon><Plus /></el-icon>
              添加成员
            </el-button>
          </div>
        </div>

        <!-- 上传门店环境图 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">上传门店环境图（1-6张）</div>
            <el-form-item prop="environmentImages" class="no-label-form-item">
              <div class="upload-container">
                <div class="image-upload-grid">
                  <!-- 已上传的图片 -->
                  <div v-for="(image, index) in environmentPreviewImages" :key="index" class="image-preview-wrapper">
                    <el-image :src="image" fit="cover" class="preview-image" :preview-src-list="[image]" :initial-index="0" preview-teleported></el-image>
                    <div class="close-button" @click="removeEnvironmentImage(index)">
                      <el-icon><Close /></el-icon>
                    </div>
                  </div>
                  <!-- 上传框 -->
                  <el-upload v-if="environmentPreviewImages.length < 6" class="upload-box" drag action="#" :auto-upload="false" :show-file-list="false" :limit="6" multiple :on-exceed="handleExceed" accept=".jpg,.jpeg,.png,.gif" :on-change="handleEnvironmentImageUpload">
                    <div class="upload-content">
                      <el-icon class="upload-icon"><UploadFilled /></el-icon>
                      <div class="upload-text">点击上传图片</div>
                      <div class="upload-tip">支持JPG、PNG格式</div>
                    </div>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </div>
        </div>

        <!-- 填写门店信息 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">填写门店信息</div>
            <el-form-item prop="storeInfo" class="no-label-form-item">
              <el-input v-model="storeForm.storeInfo" type="textarea" :rows="3" placeholder="请输入门店信息"></el-input>
            </el-form-item>
          </div>
        </div>

        <!-- 上传授权书图片 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">上传授权书图片</div>
            <el-form-item prop="authorizationImage" class="no-label-form-item">
              <div class="upload-container">
                <div class="image-upload-grid">
                  <!-- 已上传的图片 -->
                  <div v-if="authorizationPreviewImage" class="image-preview-wrapper">
                    <el-image :src="authorizationPreviewImage" fit="cover" class="preview-image" :preview-src-list="[authorizationPreviewImage]" :initial-index="0" preview-teleported></el-image>
                    <div class="close-button" @click="removeAuthorizationImage">
                      <el-icon><Close /></el-icon>
                    </div>
                  </div>
                  <!-- 上传框 -->
                  <el-upload v-if="!authorizationPreviewImage" class="upload-box" drag action="#" :auto-upload="false" :show-file-list="false" accept=".jpg,.jpeg,.png,.gif" :on-change="handleAuthorizationImageUpload">
                    <div class="upload-content">
                      <el-icon class="upload-icon"><UploadFilled /></el-icon>
                      <div class="upload-text">点击上传授权书</div>
                      <div class="upload-tip">支持JPG、PNG格式</div>
                    </div>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </div>
        </div>

        <!-- 选择关联活动 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">选择关联活动</div>
            <el-form-item prop="relatedActivity" class="no-label-form-item">
              <el-select v-model="storeForm.relatedActivity" placeholder="请选择关联活动" clearable style="width: 100%">
                <el-option v-for="item in activityOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 选择关联商品 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">选择关联商品</div>
            <el-form-item prop="relatedProduct" class="no-label-form-item">
              <el-select v-model="storeForm.relatedProduct" placeholder="请选择关联商品" clearable style="width: 100%">
                <el-option v-for="item in productOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 按钮区域 -->
        <div class="form-actions">
          <el-button @click="handleCancel" plain class="cancel-button">
            <el-icon><Close /></el-icon>
            取消
          </el-button>
          <el-button @click="handleSaveDraft" plain class="draft-button">
            <el-icon><DocumentAdd /></el-icon>
            保存草稿
          </el-button>
          <el-button type="primary" @click="handleSubmit" class="submit-button">
            <el-icon><Check /></el-icon>
            提交
          </el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, defineProps, defineEmits } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check, Delete, DocumentAdd, Close, UploadFilled, Plus } from '@element-plus/icons-vue'
import { getProvinces, getCities, getDistricts, submitStoreForm, saveStoreDraft, getStoreFormDetail, updateStoreForm } from '@/api/store'

// 定义组件属性
const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false
  },
  editId: {
    type: String,
    default: ''
  }
})

// 定义组件事件
const emit = defineEmits(['cancel', 'submit', 'save-draft'])

// 表单引用
const storeFormRef = ref(null)
const loading = ref(false)

// 门店表单数据
const storeForm = reactive({
  contactName: '',
  contactPhone: '',
  bannerImages: [],
  name: '',
  tags: '',
  openTime: '',
  closeTime: '',
  province: '',
  city: '',
  district: '',
  address: '',
  phone: '',
  // 新增字段
  selectedEquipment: [],
  teamMembers: [
    {
      name: '',
      description: '',
      image: ''
    }
  ],
  environmentImages: [],
  storeInfo: '',
  authorizationImage: null,
  relatedActivity: '',
  relatedProduct: ''
})

// 表单验证规则
const formRules = {
  contactName: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系人电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入门店名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
  phone: [{ required: true, message: '请输入门店联系电话', trigger: 'blur' }]
}

// 图片预览
const previewImages = ref([])

// 省市区选项
const provinceOptions = ref([])
const cityOptions = ref([])
const districtOptions = ref([])

// 眼光设备选项
const equipmentOptions = [
  { id: 1, name: '设备1' },
  { id: 2, name: '设备2' },
  { id: 3, name: '设备3' },
  { id: 4, name: '设备4' },
  { id: 5, name: '设备5' },
  { id: 6, name: '设备6' },
  { id: 7, name: '设备7' },
  { id: 8, name: '设备8' }
]

// 选中的设备
const selectedEquipment = ref([])

// 切换设备选择状态
const toggleEquipment = (id) => {
  const index = selectedEquipment.value.indexOf(id)
  if (index === -1) {
    selectedEquipment.value.push(id)
  } else {
    selectedEquipment.value.splice(index, 1)
  }
  storeForm.selectedEquipment = [...selectedEquipment.value]
}

// 添加团队成员
const addTeamMember = () => {
  if (storeForm.teamMembers.length >= 5) {
    ElMessage.warning('最多添加5名团队成员')
    return
  }
  storeForm.teamMembers.push({
    name: '',
    description: '',
    image: ''
  })
}

// 移除团队成员
const removeMember = (index) => {
  if (storeForm.teamMembers.length <= 1) {
    ElMessage.warning('至少需要保留1名团队成员')
    return
  }
  storeForm.teamMembers.splice(index, 1)
}

// 确认删除团队成员
const confirmRemoveMember = (index) => {
  ElMessageBox.confirm('确定要删除该团队成员吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      removeMember(index)
      ElMessage.success('团队成员已删除')
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 处理成员图片上传
const handleMemberImageUpload = (file, index) => {
  // 检查文件格式
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
  const isValidType = allowedTypes.includes(file.raw.type)

  if (!isValidType) {
    ElMessage.error('只支持JPG、PNG、JPEG、GIF格式的图片!')
    return
  }

  // 检查文件大小（10MB以内）
  const isLt2M = file.raw.size / 1024 / 1024 < 10
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 10MB!')
    return
  }

  // 生成预览
  const reader = new FileReader()
  reader.onload = (e) => {
    storeForm.teamMembers[index].image = e.target.result
  }
  reader.readAsDataURL(file.raw)
}

// 移除成员图片
const removeMemberImage = (index) => {
  storeForm.teamMembers[index].image = ''
}

// 确认删除成员图片
const confirmRemoveMemberImage = (index) => {
  ElMessageBox.confirm('确定要删除该成员图片吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      removeMemberImage(index)
      ElMessage.success('成员图片已删除')
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 门店环境图预览
const environmentPreviewImages = ref([])

// 处理环境图片上传
const handleEnvironmentImageUpload = (file) => {
  if (environmentPreviewImages.value.length >= 6) {
    ElMessage.warning('最多上传6张图片')
    return
  }

  // 检查文件格式
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
  const isValidType = allowedTypes.includes(file.raw.type)

  if (!isValidType) {
    ElMessage.error('只支持JPG、PNG、JPEG、GIF格式的图片!')
    return
  }

  // 检查文件大小（2MB以内）
  const isLt2M = file.raw.size / 1024 / 1024 < 2
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return
  }

  // 生成预览
  const reader = new FileReader()
  reader.onload = (e) => {
    environmentPreviewImages.value.push(e.target.result)
    storeForm.environmentImages.push(file.raw)
  }
  reader.readAsDataURL(file.raw)
}

// 移除环境图片
const removeEnvironmentImage = (index) => {
  ElMessageBox.confirm('确定要删除这张图片吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      environmentPreviewImages.value.splice(index, 1)
      storeForm.environmentImages.splice(index, 1)
      ElMessage.success('图片已删除')
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 授权书图片预览
const authorizationPreviewImage = ref('')

// 处理授权书图片上传
const handleAuthorizationImageUpload = (file) => {
  // 检查文件格式
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
  const isValidType = allowedTypes.includes(file.raw.type)

  if (!isValidType) {
    ElMessage.error('只支持JPG、PNG、JPEG、GIF格式的图片!')
    return
  }

  // 检查文件大小（2MB以内）
  const isLt2M = file.raw.size / 1024 / 1024 < 2
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return
  }

  // 生成预览
  const reader = new FileReader()
  reader.onload = (e) => {
    authorizationPreviewImage.value = e.target.result
    storeForm.authorizationImage = file.raw
  }
  reader.readAsDataURL(file.raw)
}

// 移除授权书图片
const removeAuthorizationImage = () => {
  ElMessageBox.confirm('确定要删除授权书图片吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      authorizationPreviewImage.value = ''
      storeForm.authorizationImage = null
      ElMessage.success('图片已删除')
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 活动选项
const activityOptions = ref([
  { value: 'activity1', label: '活动1' },
  { value: 'activity2', label: '活动2' },
  { value: 'activity3', label: '活动3' }
])

// 商品选项
const productOptions = ref([
  { value: 'product1', label: '商品1' },
  { value: 'product2', label: '商品2' },
  { value: 'product3', label: '商品3' }
])

// 保存草稿
const handleSaveDraft = async () => {
  try {
    loading.value = true

    // 构建表单数据
    const formData = {
      ...storeForm,
      status: 0, // 草稿状态
      businessHours: `${storeForm.openTime} - ${storeForm.closeTime}`,
      area: `${storeForm.province}${storeForm.city ? '-' + storeForm.city : ''}${storeForm.district ? '-' + storeForm.district : ''}`
    }

    let result
    if (props.isEdit && props.editId) {
      // 更新草稿
      result = await updateStoreForm(props.editId, formData)
    } else {
      // 保存新草稿
      result = await saveStoreDraft(formData)
    }

    ElMessage.success('草稿保存成功')
    emit('save-draft', result.data)
  } catch (error) {
    console.error('保存草稿失败:', error)
    ElMessage.error('保存草稿失败，请重试')
  } finally {
    loading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  storeFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true

        // 检查必要的字段
        if (!storeForm.openTime || !storeForm.closeTime) {
          ElMessage.warning('请设置营业时间')
          loading.value = false
          return false
        }

        if (!storeForm.province) {
          ElMessage.warning('请选择省份')
          loading.value = false
          return false
        }

        // 构建表单数据
        const formData = {
          ...storeForm,
          status: 1, // 提交状态
          businessHours: `${storeForm.openTime} - ${storeForm.closeTime}`,
          area: `${storeForm.province}${storeForm.city ? '-' + storeForm.city : ''}${storeForm.district ? '-' + storeForm.district : ''}`
        }

        let result
        if (props.isEdit && props.editId) {
          // 更新门店
          result = await updateStoreForm(props.editId, formData)
        } else {
          // 提交新门店
          result = await submitStoreForm(formData)
        }

        ElMessage.success(props.isEdit ? '门店信息更新成功' : '门店信息提交成功')
        emit('submit', result.data)
      } catch (error) {
        console.error('提交门店信息失败:', error)
        ElMessage.error('提交失败，请重试')
      } finally {
        loading.value = false
      }
    } else {
      ElMessage.warning('请填写必填项')
      return false
    }
  })
}

// 初始化
onMounted(async () => {
  // 获取省份数据
  await fetchProvinces()

  // 如果是编辑模式，获取门店数据
  if (props.isEdit && props.editId) {
    await fetchStoreDetail()
  }

  // 确保团队成员数组至少有一个成员
  if (!storeForm.teamMembers || storeForm.teamMembers.length === 0) {
    storeForm.teamMembers = [
      {
        name: '',
        description: '',
        image: ''
      }
    ]
  }
})

// 获取省份数据
const fetchProvinces = async () => {
  try {
    const res = await getProvinces()
    provinceOptions.value = res.data
  } catch (error) {
    console.error('获取省份数据失败:', error)
    // 使用默认数据
    provinceOptions.value = [
      { value: '北京市', label: '北京市' },
      { value: '上海市', label: '上海市' },
      { value: '广东省', label: '广东省' }
    ]
  }
}

// 省份变更处理
const handleProvinceChange = async (val) => {
  storeForm.city = ''
  storeForm.district = ''
  cityOptions.value = []
  districtOptions.value = []

  if (val) {
    try {
      const res = await getCities(val)
      cityOptions.value = res.data
    } catch (error) {
      console.error('获取城市数据失败:', error)
      // 使用默认数据
      if (val === '北京市') {
        cityOptions.value = [
          { value: '朝阳区', label: '朝阳区' },
          { value: '海淀区', label: '海淀区' },
          { value: '东城区', label: '东城区' },
          { value: '西城区', label: '西城区' }
        ]
      } else if (val === '上海市') {
        cityOptions.value = [
          { value: '黄浦区', label: '黄浦区' },
          { value: '徐汇区', label: '徐汇区' },
          { value: '浦东新区', label: '浦东新区' }
        ]
      } else if (val === '广东省') {
        cityOptions.value = [
          { value: '广州市', label: '广州市' },
          { value: '深圳市', label: '深圳市' }
        ]
      }
    }
  }
}

// 城市变更处理
const handleCityChange = async (val) => {
  storeForm.district = ''
  districtOptions.value = []

  if (val) {
    try {
      const res = await getDistricts(val)
      districtOptions.value = res.data
    } catch (error) {
      console.error('获取区县数据失败:', error)
      // 使用默认数据
      if (val === '广州市') {
        districtOptions.value = [
          { value: '天河区', label: '天河区' },
          { value: '越秀区', label: '越秀区' },
          { value: '海珠区', label: '海珠区' }
        ]
      } else if (val === '深圳市') {
        districtOptions.value = [
          { value: '南山区', label: '南山区' },
          { value: '福田区', label: '福田区' },
          { value: '罗湖区', label: '罗湖区' }
        ]
      }
    }
  }
}

// 图片上传变更处理
const handleImageUploadChange = (file) => {
  if (previewImages.value.length >= 6) {
    ElMessage.warning('最多上传6张图片')
    return
  }

  // 检查文件格式
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
  const isValidType = allowedTypes.includes(file.raw.type)

  if (!isValidType) {
    ElMessage.error('只支持JPG、PNG、JPEG、GIF格式的图片!')
    return
  }

  // 检查文件大小（2MB以内）
  const isLt2M = file.raw.size / 1024 / 1024 < 2
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return
  }

  // 生成预览
  const reader = new FileReader()
  reader.onload = (e) => {
    previewImages.value.push(e.target.result)
    storeForm.bannerImages.push(file.raw)
  }
  reader.readAsDataURL(file.raw)
}

// 处理图片数量超出限制
const handleExceed = () => {
  ElMessage.warning('最多上传6张图片')
}

// 移除图片
const removeImage = (index) => {
  ElMessageBox.confirm('确定要删除这张图片吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      previewImages.value.splice(index, 1)
      storeForm.bannerImages.splice(index, 1)
      ElMessage.success('图片已删除')
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 获取门店详情
const fetchStoreDetail = async () => {
  try {
    loading.value = true
    const res = await getStoreFormDetail(props.editId)

    if (res.code === 200 && res.data) {
      fillFormData(res.data)
    }
  } catch (error) {
    console.error('获取门店详情失败:', error)
    ElMessage.error('获取门店详情失败')
  } finally {
    loading.value = false
  }
}

// 编辑模式下填充表单数据
const fillFormData = (data) => {
  // 填充基本信息
  Object.keys(data).forEach((key) => {
    if (key in storeForm && key !== 'teamMembers') {
      storeForm[key] = data[key]
    }
  })

  // 处理团队成员
  if (data.teamMembers && Array.isArray(data.teamMembers)) {
    storeForm.teamMembers = [...data.teamMembers]
  }

  // 处理特殊字段
  if (data.bannerImages && Array.isArray(data.bannerImages)) {
    previewImages.value = data.bannerImages
  }

  if (data.environmentImages && Array.isArray(data.environmentImages)) {
    environmentPreviewImages.value = data.environmentImages
  }

  if (data.authorizationImage) {
    authorizationPreviewImage.value = data.authorizationImage
  }

  if (data.selectedEquipment && Array.isArray(data.selectedEquipment)) {
    selectedEquipment.value = [...data.selectedEquipment]
  }

  if (data.businessHours) {
    const [open, close] = data.businessHours.split(' - ')
    storeForm.openTime = open
    storeForm.closeTime = close
  }

  if (data.area) {
    const parts = data.area.split('-')
    storeForm.province = parts[0]
    storeForm.city = parts[1] || ''
    storeForm.district = parts[2] || ''

    // 加载城市和区县数据
    if (storeForm.province) {
      handleProvinceChange(storeForm.province).then(() => {
        if (storeForm.city) {
          handleCityChange(storeForm.city)
        }
      })
    }
  }
}

// 取消按钮处理
const handleCancel = () => {
  ElMessageBox.confirm('确定要取消操作吗？未保存的数据将会丢失', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      emit('cancel')
    })
    .catch(() => {})
}
</script>

<style scoped lang="scss">
.add-store-form {
  max-width: 1280px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;

  h2 {
    font-size: 24px;
    font-weight: 600;
    color: #1d2129;
    margin-bottom: 8px;
  }

  p {
    font-size: 14px;
    color: #86909c;
  }
}

.form-card {
  margin-bottom: 24px;
  border-radius: 8px;

  :deep(.el-card__body) {
    padding: 20px;
    overflow: hidden;
  }
}

.form-row {
  margin-bottom: 20px;
}

.form-item {
  width: 100%;
}

.filter-label {
  font-size: 14px;
  color: #86909c;
  margin-bottom: 4px;
}

.no-label-form-item {
  margin-bottom: 0;
}

.upload-container {
  width: 100%;
}

.image-upload-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.image-preview-wrapper {
  width: 192px;
  height: 108px; /* 16:9 比例，60%尺寸 */
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.close-button {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 20px;
  height: 20px;
  background-color: #f53f3f; /* 红色背景 */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease; /* 只保留背景色过渡 */

  &:hover {
    background-color: #d93026; /* 悬停时稍微深一点的红色 */
  }

  .el-icon {
    color: white;
    font-size: 12px;
  }
}

.upload-box {
  width: 192px;
  height: 108px; /* 16:9 比例，60%尺寸 */
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 2px dashed #d0d7de; /* 虚线边框 */
  transition: border-color 0.3s ease;

  &:hover {
    border-color: #165dff; /* 悬停时蓝色边框 */
  }

  :deep(.el-upload-dragger) {
    border: none;
    background-color: transparent;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    cursor: pointer;
  }
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.upload-icon {
  font-size: 36px;
  color: #c0c4cc;
  margin-bottom: 12px;
}

.upload-text {
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
  line-height: 1.4;

  em {
    color: #165dff;
    font-style: normal;
  }
}

.upload-tip {
  color: #9ca3af;
  font-size: 12px;
  line-height: 1.4;
  margin-top: 4px;
}

.time-range {
  display: flex;
  align-items: center;
  justify-content: space-between;
  line-height: 42px;
  height: 42px;
  text-align: center;
}

.time-separator {
  margin: 0 10px;
  position: relative;
  top: 5px;
}

.area-selects {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0;
  margin-top: 30px;
}

.cancel-button,
.draft-button,
.submit-button {
  height: 40px;
  padding: 8px 16px;
  font-size: 16px;
  display: flex;
  align-items: center;
  border: none;

  &:not(:first-child) {
    margin-left: 12px;
  }

  .el-icon {
    margin-right: 4px;
  }
}

.cancel-button {
  background-color: #f2f3f5;
  color: #606266;

  &:hover {
    background-color: #e5e7eb;
    border-color: transparent;
    color: #606266;
  }
}

.draft-button {
  background-color: #f2f3f5;
  color: #606266;

  &:hover {
    background-color: #e5e7eb;
    border-color: transparent;
    color: #606266;
  }
}

.submit-button {
  background-color: #165dff;

  &:hover {
    opacity: 0.8;
    background-color: #165dff;
  }
}

// 眼光设备选择样式
.equipment-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.equipment-item {
  width: 100px;
  height: 130px;
  background-color: #f5f5f5;
  border-radius: 15px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f5f5f5;
  }
}

.equipment-name {
  font-size: 16px;
  color: #333;
  text-align: center;
  padding: 10px 0;
  width: 100%;
}

.equipment-selected-icon {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 20px;
  height: 20px;
  background-color: #f53f3f;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  .el-icon {
    color: white;
    font-size: 12px;
  }
}

// 团队成员样式
.team-container {
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 8px;
}

.team-title {
  font-size: 16px;
  font-weight: 500;
  color: #000;
  margin-bottom: 16px;
}

.team-members-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.team-member-item {
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: relative;
  width: 100%;
}

.member-image-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.member-image-upload {
  width: 120px;
  height: 120px;
  flex-shrink: 0;
}

.member-image-preview {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 6px;
  overflow: hidden;
}

.member-preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.member-upload-box {
  width: 100%;
  height: 100%;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 2px dashed #d0d7de;
  transition: border-color 0.3s ease;

  &:hover {
    border-color: #165dff;
  }

  :deep(.el-upload-dragger) {
    border: none;
    background-color: transparent;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    cursor: pointer;
  }
}

.member-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.member-name-input {
  width: 100%;
}

.member-desc-input {
  width: 100%;

  :deep(.el-textarea__inner) {
    height: 76px;
  }
}

.delete-member-btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 24px;
  height: 24px;
  background-color: #f53f3f;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
  z-index: 1;

  &:hover {
    background-color: #d93026;
  }

  .el-icon {
    color: white;
    font-size: 14px;
  }
}

.add-member-btn {
  margin-top: 16px;
  background-color: #165dff;
  border: none;
  height: 40px;
  padding: 8px 16px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    opacity: 0.8;
    background-color: #165dff;
  }

  .el-icon {
    margin-right: 4px;
  }
}
</style>
