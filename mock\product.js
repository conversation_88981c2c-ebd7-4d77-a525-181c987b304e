// 产品管理相关的mock接口

// 使用全局数据存储，支持修改操作
let nextId = 10000 // 新增分类的起始ID

// 生成商品分类树结构数据
let categoryTree = [
  {
    id: 1,
    name: '光学镜架',
    level: 1,
    children: [
      {
        id: 101,
        name: '金属框',
        level: 2,
        parentId: 1,
        children: [
          {
            id: 10101,
            name: '全框',
            level: 3,
            parentId: 101
          },
          {
            id: 10102,
            name: '半框',
            level: 3,
            parentId: 101
          },
          {
            id: 10103,
            name: '无框',
            level: 3,
            parentId: 101
          }
        ]
      },
      {
        id: 102,
        name: '板材框',
        level: 2,
        parentId: 1,
        children: [
          {
            id: 10201,
            name: '全框',
            level: 3,
            parentId: 102
          },
          {
            id: 10202,
            name: '半框',
            level: 3,
            parentId: 102
          }
        ]
      },
      {
        id: 103,
        name: '混合材质',
        level: 2,
        parentId: 1,
        children: [
          {
            id: 10301,
            name: '全框',
            level: 3,
            parentId: 103
          },
          {
            id: 10302,
            name: '半框',
            level: 3,
            parentId: 103
          }
        ]
      }
    ]
  },
  {
    id: 2,
    name: '太阳镜',
    level: 1,
    children: [
      {
        id: 201,
        name: '偏光镜',
        level: 2,
        parentId: 2,
        children: [
          {
            id: 20101,
            name: '驾驶型',
            level: 3,
            parentId: 201
          },
          {
            id: 20102,
            name: '运动型',
            level: 3,
            parentId: 201
          }
        ]
      },
      {
        id: 202,
        name: '时尚墨镜',
        level: 2,
        parentId: 2,
        children: [
          {
            id: 20201,
            name: '男款',
            level: 3,
            parentId: 202
          },
          {
            id: 20202,
            name: '女款',
            level: 3,
            parentId: 202
          },
          {
            id: 20203,
            name: '中性款',
            level: 3,
            parentId: 202
          }
        ]
      }
    ]
  },
  {
    id: 3,
    name: '隐形眼镜',
    level: 1,
    children: [
      {
        id: 301,
        name: '日抛',
        level: 2,
        parentId: 3,
        children: []
      },
      {
        id: 302,
        name: '月抛',
        level: 2,
        parentId: 3,
        children: []
      },
      {
        id: 303,
        name: '年抛',
        level: 2,
        parentId: 3,
        children: []
      }
    ]
  },
  {
    id: 4,
    name: '老花镜',
    level: 1,
    children: [
      {
        id: 401,
        name: '折叠式',
        level: 2,
        parentId: 4,
        children: []
      },
      {
        id: 402,
        name: '全框式',
        level: 2,
        parentId: 4,
        children: []
      }
    ]
  },
  {
    id: 5,
    name: '防蓝光镜',
    level: 1,
    children: [
      {
        id: 501,
        name: '游戏专用',
        level: 2,
        parentId: 5,
        children: []
      },
      {
        id: 502,
        name: '办公专用',
        level: 2,
        parentId: 5,
        children: []
      }
    ]
  }
]

// 辅助函数 - 根据ID查找分类
const findCategoryById = (id, tree = categoryTree) => {
  for (const category of tree) {
    if (category.id === id) {
      return category
    }

    if (category.children && category.children.length > 0) {
      const found = findCategoryById(id, category.children)
      if (found) return found
    }
  }

  return null
}

// 辅助函数 - 删除分类
const deleteCategory = (id, tree = categoryTree) => {
  for (let i = 0; i < tree.length; i++) {
    if (tree[i].id === id) {
      tree.splice(i, 1)
      return true
    }

    if (tree[i].children && tree[i].children.length > 0) {
      const deleted = deleteCategory(id, tree[i].children)
      if (deleted) return true
    }
  }

  return false
}

// 一级分类数据
const categoryLevel1 = [
  { id: 1, name: '光学镜架' },
  { id: 2, name: '太阳镜' },
  { id: 3, name: '隐形眼镜' },
  { id: 4, name: '老花镜' },
  { id: 5, name: '防蓝光镜' }
]

// 二级分类数据
const categoryLevel2 = {
  1: [
    // 光学镜架的二级分类
    { id: 101, name: '金属框', parentId: 1 },
    { id: 102, name: '板材框', parentId: 1 },
    { id: 103, name: '混合材质', parentId: 1 }
  ],
  2: [
    // 太阳镜的二级分类
    { id: 201, name: '偏光镜', parentId: 2 },
    { id: 202, name: '时尚墨镜', parentId: 2 }
  ],
  3: [
    // 隐形眼镜的二级分类
    { id: 301, name: '日抛', parentId: 3 },
    { id: 302, name: '月抛', parentId: 3 },
    { id: 303, name: '年抛', parentId: 3 }
  ],
  4: [
    // 老花镜的二级分类
    { id: 401, name: '折叠式', parentId: 4 },
    { id: 402, name: '全框式', parentId: 4 }
  ],
  5: [
    // 防蓝光镜的二级分类
    { id: 501, name: '游戏专用', parentId: 5 },
    { id: 502, name: '办公专用', parentId: 5 }
  ]
}

// 三级分类数据
const categoryLevel3 = {
  101: [
    // 金属框的三级分类
    { id: 10101, name: '全框', parentId: 101 },
    { id: 10102, name: '半框', parentId: 101 },
    { id: 10103, name: '无框', parentId: 101 }
  ],
  102: [
    // 板材框的三级分类
    { id: 10201, name: '全框', parentId: 102 },
    { id: 10202, name: '半框', parentId: 102 }
  ],
  103: [
    // 混合材质的三级分类
    { id: 10301, name: '全框', parentId: 103 },
    { id: 10302, name: '半框', parentId: 103 }
  ],
  201: [
    // 偏光镜的三级分类
    { id: 20101, name: '驾驶型', parentId: 201 },
    { id: 20102, name: '运动型', parentId: 201 }
  ],
  202: [
    // 时尚墨镜的三级分类
    { id: 20201, name: '男款', parentId: 202 },
    { id: 20202, name: '女款', parentId: 202 },
    { id: 20203, name: '中性款', parentId: 202 }
  ]
}

// 产品列表数据
let productList = [
  {
    id: 'SP-001',
    name: '舒倍钛金光学镜架',
    image: 'https://cdn.pixabay.com/photo/2018/01/31/07/36/sunglasses-3120508_1280.jpg',
    category: {
      level1: { id: 1, name: '光学镜架' },
      level2: { id: 101, name: '金属框' },
      level3: { id: 10101, name: '全框' }
    },
    stores: ['北京西单店', '上海外滩店'],
    store: '北京西单店',
    status: 1, // 1: 上架, 0: 下架
    modifier: '张三',
    modifyTime: '2025-06-20 14:30',
    price: 699,
    stock: 120,
    sales: 56,
    description: '轻便舒适的钛金属材质镜架，适合日常佩戴。',
    specifications: [
      { name: '标准版', price: 699.0, image: 'https://cdn.pixabay.com/photo/2018/01/31/07/36/sunglasses-3120508_1280.jpg' },
      { name: '豪华版', price: 899.0, image: 'https://cdn.pixabay.com/photo/2017/08/06/09/52/sunglasses-2590997_1280.jpg' }
    ],
    mainImages: ['https://cdn.pixabay.com/photo/2018/01/31/07/36/sunglasses-3120508_1280.jpg', 'https://cdn.pixabay.com/photo/2017/08/06/09/52/sunglasses-2590997_1280.jpg'],
    detailImage: 'https://cdn.pixabay.com/photo/2017/12/20/14/51/pair-of-glasses-3030859_1280.jpg',
    detail: '<h3>产品详情</h3><p>轻便舒适的钛金属材质镜架，适合日常佩戴。</p><p>镜框尺寸: 52-18-140</p>',
    features: ['轻便', '耐用', '舒适']
  },
  {
    id: 'SP-002',
    name: '板材复古太阳镜',
    image: 'https://cdn.pixabay.com/photo/2017/08/06/09/52/sunglasses-2590997_1280.jpg',
    category: {
      level1: { id: 2, name: '太阳镜' },
      level2: { id: 202, name: '时尚墨镜' },
      level3: { id: 20201, name: '男款' }
    },
    stores: ['上海外滩店'],
    store: '上海外滩店',
    status: 0, // 1: 上架, 0: 下架
    modifier: '李四',
    modifyTime: '2025-06-21 09:15',
    price: 899,
    stock: 85,
    sales: 32,
    description: '高品质板材打造的复古风格太阳镜，彰显时尚品味。',
    specifications: [
      { name: '黑色款', price: 899.0, image: 'https://cdn.pixabay.com/photo/2017/08/06/09/52/sunglasses-2590997_1280.jpg' },
      { name: '棕色款', price: 959.0, image: 'https://cdn.pixabay.com/photo/2017/12/20/14/51/pair-of-glasses-3030859_1280.jpg' }
    ],
    mainImages: ['https://cdn.pixabay.com/photo/2017/08/06/09/52/sunglasses-2590997_1280.jpg'],
    detailImage: 'https://cdn.pixabay.com/photo/2018/01/31/07/36/sunglasses-3120508_1280.jpg',
    detail: '<h3>产品详情</h3><p>高品质板材打造的复古风格太阳镜，彰显时尚品味。</p><p>镜框尺寸: 54-20-145</p>',
    features: ['复古', '时尚', 'UV400防护']
  },
  {
    id: 'SP-003',
    name: '超轻纯钛半框镜架',
    image: 'https://cdn.pixabay.com/photo/2017/12/20/14/51/pair-of-glasses-3030859_1280.jpg',
    category: {
      level1: { id: 1, name: '光学镜架' },
      level2: { id: 101, name: '金属框' },
      level3: { id: 10102, name: '半框' }
    },
    stores: ['广州天河店', '深圳福田店'],
    store: '广州天河店',
    status: 1, // 1: 上架, 0: 下架
    modifier: '王五',
    modifyTime: '2025-06-22 16:45',
    price: 1299,
    stock: 45,
    sales: 28,
    description: '采用超轻纯钛材质，佩戴无压力，适合长时间使用。',
    specifications: [
      { name: '银色款', price: 1299.0, image: 'https://cdn.pixabay.com/photo/2017/12/20/14/51/pair-of-glasses-3030859_1280.jpg' },
      { name: '金色款', price: 1399.0, image: 'https://cdn.pixabay.com/photo/2018/01/31/07/36/sunglasses-3120508_1280.jpg' }
    ],
    mainImages: ['https://cdn.pixabay.com/photo/2017/12/20/14/51/pair-of-glasses-3030859_1280.jpg', 'https://cdn.pixabay.com/photo/2018/01/31/07/36/sunglasses-3120508_1280.jpg'],
    detailImage: 'https://cdn.pixabay.com/photo/2017/08/06/09/52/sunglasses-2590997_1280.jpg',
    detail: '<h3>产品详情</h3><p>采用超轻纯钛材质，佩戴无压力，适合长时间使用。</p><p>镜框尺寸: 53-19-142</p>',
    features: ['超轻', '纯钛', '抗过敏']
  }
]

// 用于生成新商品ID
let nextProductId = 4

// 辅助函数 - 查找产品
const findProduct = (id) => {
  return productList.find((item) => item.id === id)
}

export default [
  // 获取产品列表
  {
    url: '/mock/product/list',
    method: 'get',
    response: (request) => {
      const { level1, level2, level3, status, name, id } = request.query
      let list = [...productList]

      // 根据分类筛选
      if (level1 && level1 !== '') {
        list = list.filter((item) => item.category.level1.id === parseInt(level1))
      }

      if (level2 && level2 !== '') {
        list = list.filter((item) => item.category.level2.id === parseInt(level2))
      }

      if (level3 && level3 !== '') {
        list = list.filter((item) => item.category.level3.id === parseInt(level3))
      }

      // 根据上架状态筛选
      if (status !== undefined && status !== '') {
        list = list.filter((item) => item.status === parseInt(status))
      }

      // 根据商品名称筛选
      if (name && name !== '') {
        list = list.filter((item) => item.name.includes(name))
      }

      // 根据商品编号筛选
      if (id && id !== '') {
        list = list.filter((item) => item.id.includes(id))
      }

      return {
        code: 200,
        message: '获取成功',
        data: {
          list,
          total: list.length,
          pageSize: 10,
          page: 1
        }
      }
    }
  },

  // 获取产品详情
  {
    url: new RegExp('/product/detail/(.*)'),
    method: 'get',
    response: (request) => {
      // 从URL中手动提取ID
      const match = request.url.match(/\/product\/detail\/(.*)/)
      const id = match ? match[1] : null

      if (!id) {
        return {
          code: 400,
          message: '商品ID不能为空',
          data: null
        }
      }

      const product = findProduct(id)

      if (!product) {
        return {
          code: 404,
          message: '产品不存在',
          data: null
        }
      }

      // 转换为编辑表单所需的格式
      const editData = {
        ...product,
        level1: product.category.level1.id,
        level2: product.category.level2.id,
        level3: product.category.level3.id,
        stores: product.stores || []
      }

      return {
        code: 200,
        message: '获取成功',
        data: editData
      }
    }
  },

  // 获取一级分类
  {
    url: '/product/categories',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: '获取成功',
        data: categoryLevel1
      }
    }
  },

  // 获取二级分类
  {
    url: new RegExp('/product/categories/second/(.*)'),
    method: 'get',
    response: (request) => {
      // 从URL中手动提取parentId
      const match = request.url.match(/\/product\/categories\/second\/(.*)/)
      const parentId = match ? match[1] : null

      if (!parentId) {
        return {
          code: 400,
          message: '父级分类ID不能为空',
          data: []
        }
      }

      const categories = categoryLevel2[parentId] || []

      return {
        code: 200,
        message: '获取成功',
        data: categories
      }
    }
  },

  // 获取三级分类
  {
    url: new RegExp('/product/categories/third/(.*)'),
    method: 'get',
    response: (request) => {
      // 从URL中手动提取parentId
      const match = request.url.match(/\/product\/categories\/third\/(.*)/)
      const parentId = match ? match[1] : null

      if (!parentId) {
        return {
          code: 400,
          message: '父级分类ID不能为空',
          data: []
        }
      }

      const categories = categoryLevel3[parentId] || []

      return {
        code: 200,
        message: '获取成功',
        data: categories
      }
    }
  },

  // 切换产品上架状态
  {
    url: '/product/toggle-status',
    method: 'put',
    response: (request) => {
      const { id, status } = request.body
      const product = findProduct(id)

      if (!product) {
        return {
          code: 404,
          message: '产品不存在',
          data: null
        }
      }

      product.status = status

      return {
        code: 200,
        message: status === 1 ? '上架成功' : '下架成功',
        data: null
      }
    }
  },

  // 获取商品分类树结构
  {
    url: '/mock/product/category/tree',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: '获取成功',
        data: categoryTree
      }
    }
  },

  // 添加商品分类
  {
    url: '/mock/product/category',
    method: 'post',
    response: (req) => {
      const { name, level, parentId } = req.body

      // 参数验证
      if (!name || !level) {
        return {
          code: 400,
          message: '参数不完整'
        }
      }

      // 创建新分类
      const newCategory = {
        id: nextId++,
        name,
        level,
        parentId: parentId || null,
        children: []
      }

      // 添加到分类树中
      if (level === 1) {
        // 添加一级分类
        categoryTree.push(newCategory)
      } else {
        // 添加二级或三级分类
        const parent = findCategoryById(parentId)
        if (parent) {
          if (!parent.children) {
            parent.children = []
          }
          parent.children.push(newCategory)
        } else {
          return {
            code: 404,
            message: '父分类不存在'
          }
        }
      }

      return {
        code: 200,
        data: { id: newCategory.id },
        message: '添加成功'
      }
    }
  },

  // 更新商品分类
  {
    url: '/mock/product/category/:id',
    method: 'put',
    response: (req) => {
      // 从URL中提取ID
      const idStr = req.url.split('/').pop()
      const id = parseInt(idStr)
      const { name } = req.body

      // 参数验证
      if (!name) {
        return {
          code: 400,
          message: '参数不完整'
        }
      }

      // 查找并更新分类
      const category = findCategoryById(id)
      if (category) {
        category.name = name
        return {
          code: 200,
          message: '更新成功'
        }
      } else {
        return {
          code: 404,
          message: '分类不存在'
        }
      }
    }
  },

  // 删除商品分类
  {
    url: '/mock/product/category/:id',
    method: 'delete',
    response: (req) => {
      // 从URL中提取ID
      const idStr = req.url.split('/').pop()
      const id = parseInt(idStr)

      // 查找分类
      const category = findCategoryById(id)
      if (!category) {
        return {
          code: 404,
          message: '分类不存在'
        }
      }

      // 检查是否有子分类
      if (category.children && category.children.length > 0) {
        return {
          code: 403,
          message: '该分类下有子分类，无法删除'
        }
      }

      // 删除分类
      const deleted = deleteCategory(id)
      if (deleted) {
        return {
          code: 200,
          message: '删除成功'
        }
      } else {
        return {
          code: 500,
          message: '删除失败'
        }
      }
    }
  },

  // 新增商品
  {
    url: '/product/add',
    method: 'post',
    response: (request) => {
      const { level1, level2, level3, stores, status, id, name, specifications, mainImages, detailImage, detail, category } = request.body

      // 参数验证
      if (!name || !id || !level1 || !level2 || !level3) {
        return {
          code: 400,
          message: '参数不完整',
          data: null
        }
      }

      // 检查商品ID是否已存在
      const existingProduct = findProduct(id)
      if (existingProduct) {
        return {
          code: 409,
          message: '商品ID已存在',
          data: null
        }
      }

      // 查找分类信息
      const level1Info = categoryLevel1.find((item) => item.id === level1)
      const level2Info = categoryLevel2[level1]?.find((item) => item.id === level2)
      const level3Info = categoryLevel3[level2]?.find((item) => item.id === level3)

      // 创建新商品
      const newProduct = {
        id,
        name,
        image: mainImages && mainImages.length > 0 ? mainImages[0] : '',
        category: {
          level1: level1Info,
          level2: level2Info,
          level3: level3Info
        },
        stores: stores || [],
        store: stores && stores.length > 0 ? stores[0] : '',
        status: status || 1,
        modifier: '系统管理员',
        modifyTime: new Date().toLocaleString('zh-CN'),
        price: specifications && specifications.length > 0 ? specifications[0].price : 0,
        stock: 100,
        sales: 0,
        description: detail || '',
        specifications: specifications || [],
        mainImages: mainImages || [],
        detailImage: detailImage || '',
        detail: detail || '',
        features: []
      }

      // 添加到商品列表
      productList.push(newProduct)

      return {
        code: 200,
        message: '新增商品成功',
        data: { id: newProduct.id }
      }
    }
  },

  // 修改商品
  {
    url: '/product/update',
    method: 'put',
    response: (request) => {
      const { level1, level2, level3, stores, status, id, name, specifications, mainImages, detailImage, detail, category } = request.body

      // 参数验证
      if (!name || !id || !level1 || !level2 || !level3) {
        return {
          code: 400,
          message: '参数不完整',
          data: null
        }
      }

      // 查找商品
      const product = findProduct(id)
      if (!product) {
        return {
          code: 404,
          message: '商品不存在',
          data: null
        }
      }

      // 查找分类信息
      const level1Info = categoryLevel1.find((item) => item.id === level1)
      const level2Info = categoryLevel2[level1]?.find((item) => item.id === level2)
      const level3Info = categoryLevel3[level2]?.find((item) => item.id === level3)

      // 更新商品信息
      product.name = name
      product.image = mainImages && mainImages.length > 0 ? mainImages[0] : product.image
      product.category = {
        level1: level1Info,
        level2: level2Info,
        level3: level3Info
      }
      product.stores = stores || product.stores
      product.store = stores && stores.length > 0 ? stores[0] : product.store
      product.status = status !== undefined ? status : product.status
      product.modifier = '系统管理员'
      product.modifyTime = new Date().toLocaleString('zh-CN')
      product.price = specifications && specifications.length > 0 ? specifications[0].price : product.price
      product.description = detail || product.description
      product.specifications = specifications || product.specifications
      product.mainImages = mainImages || product.mainImages
      product.detailImage = detailImage || product.detailImage
      product.detail = detail || product.detail

      return {
        code: 200,
        message: '修改商品成功',
        data: null
      }
    }
  },

  // 删除商品
  {
    url: new RegExp('/product/delete/(.*)'),
    method: 'delete',
    response: (request) => {
      // 从URL中手动提取ID
      const match = request.url.match(/\/product\/delete\/(.*)/)
      const id = match ? match[1] : null

      if (!id) {
        return {
          code: 400,
          message: '商品ID不能为空',
          data: null
        }
      }

      // 查找商品
      const productIndex = productList.findIndex((item) => item.id === id)
      if (productIndex === -1) {
        return {
          code: 404,
          message: '商品不存在',
          data: null
        }
      }

      // 删除商品
      productList.splice(productIndex, 1)

      return {
        code: 200,
        message: '删除商品成功',
        data: null
      }
    }
  }
]
