<template>
  <div class="add-top-form">
    <div class="page-header">
      <h2>{{ props.isEditMode ? '修改门店置顶' : '新增门店置顶' }}</h2>
      <p>{{ props.isEditMode ? '修改门店置顶信息' : '选择门店进行置顶' }}</p>
    </div>

    <el-card class="form-card" shadow="never">
      <!-- 选择地区 -->
      <div class="form-section">
        <div class="filter-row">
          <div class="filter-item">
            <div class="filter-label">选择地区</div>
            <el-select v-model="formData.province" placeholder="请选择省/直辖市" clearable class="filter-select" @change="handleProvinceChange">
              <el-option v-for="item in provinceOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>

          <div class="filter-item">
            <div class="filter-label">选择城市</div>
            <el-select v-model="formData.city" placeholder="请选择市/区" clearable class="filter-select" @change="handleCityChange" :disabled="!formData.province">
              <el-option v-for="item in cityOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>

          <div class="filter-item">
            <div class="filter-label">选择区县</div>
            <el-select v-model="formData.district" placeholder="请选择区/县" clearable class="filter-select" :disabled="!formData.city">
              <el-option v-for="item in districtOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>
        </div>
      </div>

      <!-- 已选择门店 -->
      <div class="form-section">
        <div class="form-section-title">
          已选择门店 <span class="selection-count">({{ selectedStores.length }}/3)</span>
        </div>
        <el-table :data="selectedStores" class="selected-stores-table" :header-cell-style="selectedTableHeaderStyle">
          <el-table-column type="index" label="序号" width="80">
            <template #default="scope">
              <div class="store-number">{{ scope.$index + 1 }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="门店名称">
            <template #default="scope">
              <span class="store-name-text">{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button type="danger" link @click="removeSelectedStore(scope.row)" class="cancel-top-btn">
                <el-icon><Delete /></el-icon>
                取消置顶
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 选择置顶门店按钮 -->
      <div class="form-section">
        <div class="store-selection-hint">
          <el-button @click="showSelectStoreDialog" type="primary" class="select-store-btn" :disabled="selectedStores.length >= 3">
            <el-icon><Plus /></el-icon>
            {{ props.isEditMode ? '添加更多门店' : '选择置顶门店' }}
          </el-button>
          <span class="selection-hint">可选择1-3个门店进行置顶</span>
        </div>
      </div>

      <!-- 按钮区域 -->
      <div class="form-actions">
        <el-button @click="handleCancel" plain class="cancel-button">
          <el-icon><Close /></el-icon>
          取消
        </el-button>
        <el-button type="primary" @click="handleSubmit" class="save-button">
          <el-icon><Check /></el-icon>
          {{ props.isEditMode ? '保存修改' : '保存' }}
        </el-button>
      </div>
    </el-card>

    <!-- 选择门店弹窗 -->
    <el-dialog title="选择置顶门店（可多选，最多3个）" v-model="selectStoreDialogVisible" width="600px" :close-on-click-modal="false" :destroy-on-close="true">
      <div class="filter-row">
        <div class="filter-item" style="flex: 1">
          <div class="filter-label">门店名称</div>
          <el-input v-model="storeSearchKeyword" placeholder="请输入门店名称" clearable>
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <div class="filter-actions" style="align-self: flex-end; margin-left: 14px">
          <el-button type="primary" @click="searchStores" class="search-button">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
        </div>
      </div>

      <el-table :data="availableStores" style="width: 100%; margin-top: 10px" @row-click="handleRowClick" :header-cell-style="selectedTableHeaderStyle" :cell-style="tableCellStyle" class="store-select-table" border>
        <el-table-column width="55">
          <template #default="scope">
            <el-checkbox v-model="scope.row.selected" :disabled="isStoreDisabled(scope.row.id)" @change="(val) => handleStoreSelect(scope.row, val)"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column type="index" label="序号" width="80"></el-table-column>
        <el-table-column prop="id" label="门店ID" width="100"></el-table-column>
        <el-table-column prop="name" label="门店名称"></el-table-column>
      </el-table>

      <template #footer>
        <span class="form-actions" style="display: flex; justify-content: flex-end; margin-top: 10px">
          <el-button @click="selectStoreDialogVisible = false" plain class="cancel-button">
            <el-icon><Close /></el-icon>
            取消
          </el-button>
          <el-button type="primary" @click="confirmSelectStores" class="save-button">
            <el-icon><Check /></el-icon>
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Delete, Plus, Close, Check } from '@element-plus/icons-vue'
import { getProvinces, getCities, getDistricts, getAvailableStores } from '@/api/store'

// 定义组件的props
const props = defineProps({
  initialSelectedStores: {
    type: Array,
    default: () => []
  },
  isEditMode: {
    type: Boolean,
    default: false
  },
  storeToEdit: {
    type: Object,
    default: () => null
  }
})

// 定义组件的事件
const emits = defineEmits(['cancel', 'submit', 'update:selectedStores'])

// 表单数据
const formData = reactive({
  province: '',
  city: '',
  district: ''
})

// 地区选项
const provinceOptions = ref([])
const cityOptions = ref([])
const districtOptions = ref([])

// 已选择的门店
const selectedStores = ref([...props.initialSelectedStores])

// 用于跟踪已选择的门店ID
const selectedStoreIds = computed(() => {
  return new Set(selectedStores.value.map((store) => store.id))
})

// 选择门店弹窗相关
const selectStoreDialogVisible = ref(false)
const storeSearchKeyword = ref('')
const availableStores = ref([])
const storeSelection = ref([])

// 已选择表格表头样式
const selectedTableHeaderStyle = {
  backgroundColor: '#FAFBFC',
  color: '#89929E',
  fontSize: '14px',
  fontWeight: '600',
  height: '50px'
}

// 表格单元格样式
const tableCellStyle = {
  padding: '12px 0',
  fontSize: '14px'
}

// 初始化地区选项
const initAreaOptions = async () => {
  try {
    const res = await getProvinces()
    provinceOptions.value = res.data
  } catch (error) {
    console.error('获取省份数据失败:', error)
    // 使用默认数据
    provinceOptions.value = [
      { value: '北京市', label: '北京市' },
      { value: '上海市', label: '上海市' },
      { value: '广东省', label: '广东省' }
    ]
  }
}

// 省份变化处理
const handleProvinceChange = async (val) => {
  formData.city = ''
  formData.district = ''
  cityOptions.value = []
  districtOptions.value = []

  if (val) {
    try {
      const res = await getCities(val)
      cityOptions.value = res.data
    } catch (error) {
      console.error('获取城市数据失败:', error)
      // 使用默认数据
      if (val === '北京市') {
        cityOptions.value = [
          { value: '朝阳区', label: '朝阳区' },
          { value: '海淀区', label: '海淀区' },
          { value: '东城区', label: '东城区' },
          { value: '西城区', label: '西城区' }
        ]
      } else if (val === '上海市') {
        cityOptions.value = [
          { value: '黄浦区', label: '黄浦区' },
          { value: '徐汇区', label: '徐汇区' },
          { value: '浦东新区', label: '浦东新区' }
        ]
      } else if (val === '广东省') {
        cityOptions.value = [
          { value: '广州市', label: '广州市' },
          { value: '深圳市', label: '深圳市' }
        ]
      }
    }
  }
}

// 城市变化处理
const handleCityChange = async (val) => {
  formData.district = ''
  districtOptions.value = []

  if (val) {
    try {
      const res = await getDistricts(val)
      districtOptions.value = res.data
    } catch (error) {
      console.error('获取区县数据失败:', error)
      // 使用默认数据
      if (val === '广州市') {
        districtOptions.value = [
          { value: '天河区', label: '天河区' },
          { value: '越秀区', label: '越秀区' },
          { value: '海珠区', label: '海珠区' }
        ]
      } else if (val === '深圳市') {
        districtOptions.value = [
          { value: '南山区', label: '南山区' },
          { value: '福田区', label: '福田区' },
          { value: '罗湖区', label: '罗湖区' }
        ]
      }
    }
  }
}

// 显示选择门店弹窗
const showSelectStoreDialog = () => {
  selectStoreDialogVisible.value = true
  storeSearchKeyword.value = ''
  fetchAvailableStores()
}

// 获取可用门店
const fetchAvailableStores = async () => {
  try {
    const params = {
      keyword: storeSearchKeyword.value,
      province: formData.province,
      city: formData.city,
      district: formData.district
    }
    const res = await getAvailableStores(params)
    availableStores.value = res.data.map((store) => ({
      ...store,
      selected: selectedStoreIds.value.has(store.id)
    }))
  } catch (error) {
    console.error('获取可用门店失败:', error)
    // 使用默认数据
    availableStores.value = [
      { id: 1, name: '北京旗舰店', selected: false },
      { id: 2, name: '上海旗舰店', selected: false },
      { id: 3, name: '广州旗舰店', selected: false },
      { id: 4, name: '深圳旗舰店', selected: false },
      { id: 5, name: '杭州旗舰店', selected: false }
    ].map((store) => ({
      ...store,
      selected: selectedStoreIds.value.has(store.id)
    }))
  }
}

// 搜索门店
const searchStores = () => {
  fetchAvailableStores()
}

// 判断门店是否禁用选择
const isStoreDisabled = (id) => {
  return selectedStoreIds.value.has(id) && selectedStores.value.length >= 3
}

// 处理门店选择
const handleStoreSelect = (store, val) => {
  if (val) {
    if (selectedStores.value.length >= 3) {
      ElMessage.warning('最多只能选择3个门店')
      store.selected = false
      return
    }

    // 检查是否已经选择了这个门店
    if (selectedStoreIds.value.has(store.id)) {
      ElMessage.warning('已经选择了该门店')
      store.selected = true
      return
    }
  }
}

// 处理表格行点击
const handleRowClick = (row) => {
  if (isStoreDisabled(row.id)) {
    return
  }
  row.selected = !row.selected
  handleStoreSelect(row, row.selected)
}

// 移除已选择的门店
const removeSelectedStore = (store) => {
  ElMessageBox.confirm(`确定要取消置顶门店「${store.name}」吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      selectedStores.value = selectedStores.value.filter((item) => item.id !== store.id)
      // 如果弹窗打开，更新可用门店列表中的选中状态
      if (selectStoreDialogVisible.value) {
        const storeInAvailable = availableStores.value.find((item) => item.id === store.id)
        if (storeInAvailable) {
          storeInAvailable.selected = false
        }
      }

      // 通知父组件更新，但避免反应式循环
      nextTick(() => {
        emits('update:selectedStores', [...selectedStores.value])
      })

      ElMessage.success('已取消置顶')
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 确认选择门店
const confirmSelectStores = () => {
  try {
    const selectedInDialog = availableStores.value.filter((store) => store.selected)

    // 合并已选择的门店，避免重复
    const newSelectedStores = [...selectedStores.value]
    selectedInDialog.forEach((store) => {
      if (!selectedStoreIds.value.has(store.id)) {
        // 确保添加完整的门店信息，包括名称
        newSelectedStores.push({
          id: store.id,
          name: store.name,
          // 其他可能需要的信息
          areas: store.areas || []
        })
      }
    })

    // 更新选中的门店
    selectedStores.value = newSelectedStores

    // 通知父组件更新，使用独立复制避免引用问题
    emits('update:selectedStores', JSON.parse(JSON.stringify(newSelectedStores)))

    // 确保关闭对话框
    selectStoreDialogVisible.value = false

    // 额外确保对话框关闭
    setTimeout(() => {
      selectStoreDialogVisible.value = false
    }, 100)

    console.log('已选择门店:', selectedStores.value)
  } catch (error) {
    console.error('确认选择门店失败:', error)
    // 强制关闭对话框
    selectStoreDialogVisible.value = false
  }
}

// 取消按钮处理
const handleCancel = () => {
  emits('cancel')
}

// 提交表单
const handleSubmit = () => {
  if (selectedStores.value.length === 0) {
    ElMessage.warning('请至少选择一个门店')
    return
  }

  // 创建包含完整门店信息的提交数据
  const submitData = {
    province: formData.province,
    city: formData.city,
    district: formData.district,
    stores: selectedStores.value // 发送完整的门店对象数组
  }

  // 触发提交事件
  emits('submit', submitData)
}

// 监听初始选择的门店变化
watch(
  () => props.initialSelectedStores,
  (newVal) => {
    // 使用深比较避免不必要的更新
    if (JSON.stringify(newVal) !== JSON.stringify(selectedStores.value)) {
      selectedStores.value = [...newVal]
    }
  },
  { immediate: true }
)

// 初始化
initAreaOptions()
</script>

<style scoped lang="scss">
.add-top-form {
  max-width: 1280px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;

  h2 {
    font-size: 24px;
    font-weight: 600;
    color: #1d2129;
    margin-bottom: 8px;
  }

  p {
    font-size: 14px;
    color: #86909c;
  }
}

.form-card {
  margin-bottom: 24px;
  border-radius: 8px;

  :deep(.el-card__body) {
    padding: 20px;
  }
}

.form-section {
  margin-bottom: 14px;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #1d2129;
  margin-bottom: 16px;
  display: flex;
  align-items: center;

  .selection-count {
    font-size: 14px;
    color: #86909c;
    margin-left: 8px;
    font-weight: normal;
  }
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 14px;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.filter-item {
  flex: 1;
  min-width: 200px;
}

.filter-label {
  font-size: 14px;
  color: #86909c;
  margin-bottom: 4px;
}

.filter-select {
  width: 100%;
}

.selected-stores-table {
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #ebeef5;

  /* 设置表格行高，与规格配置表格保持一致 */
  :deep(.el-table__row) {
    height: 60px; /* 与规格配置表格行高一致 */
  }

  :deep(.el-table__cell) {
    padding: 8px 0; /* 与规格配置表格单元格内边距一致 */
  }
}

.store-number {
  color: #606266;
}

.store-name-text {
  font-weight: 500;
  color: #303133;
}

.cancel-top-btn {
  padding: 0;
  font-size: 14px;
}

.select-store-btn {
  height: 40px;
  padding: 8px 16px;
  font-size: 16px;
  display: flex;
  align-items: center;
  background-color: #165dff;

  .el-icon {
    margin-right: 4px;
  }

  &:hover {
    background-color: $primary-hover !important;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-start;
  gap: 0;
  margin-top: 10px;

  .el-button {
    height: 40px;
    padding: 8px 16px;
    font-size: 16px;
    display: flex;
    align-items: center;
    border: none;

    &:not(:first-child) {
      margin-left: 12px;
    }

    .el-icon {
      margin-right: 4px;
    }
  }
}

.cancel-button {
  background-color: #f2f3f5;
  color: #606266;

  &:hover {
    background-color: #e5e7eb;
    border-color: transparent;
    color: #606266;
  }
}

.save-button {
  background-color: $primary-color;

  &:hover {
    background-color: $primary-hover !important;
  }
}

.search-button {
  background-color: #165dff;
  height: 40px;
  padding: 8px 16px;
  font-size: 16px;
  display: flex;
  align-items: center;

  .el-icon {
    margin-right: 4px;
  }

  &:hover {
    opacity: 0.8;
    background-color: #165dff;
  }
}

.store-select-table {
  margin-bottom: 16px;
}

:deep(.el-dialog__body) {
  padding-top: 10px;
}

.store-selection-hint {
  display: flex;
  align-items: center;
  gap: 16px;
}

.selection-hint {
  color: #86909c;
  font-size: 14px;
}
</style>
