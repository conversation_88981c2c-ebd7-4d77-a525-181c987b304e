import request from '@/utils/request';

// 获取积分商品列表
export const getPointsProductList = (params) => {
  return request({
    url: '/points-mall/list',
    method: 'get',
    params,
  });
};

// 获取积分商品详情
export const getPointsProductDetail = (id) => {
  return request({
    url: `/points-mall/detail/${id}`,
    method: 'get',
  });
};

// 新增积分商品
export const addPointsProduct = (data) => {
  return request({
    url: '/points-mall/add',
    method: 'post',
    data,
  });
};

// 编辑积分商品
export const updatePointsProduct = (data) => {
  return request({
    url: '/points-mall/update',
    method: 'put',
    data,
  });
};

// 删除积分商品
export const deleteProduct = (id) => {
  return request({
    url: `/points-mall/delete/${id}`,
    method: 'delete',
  });
};

// 更新商品上架状态
export const updateProductShelfStatus = (data) => {
  return request({
    url: '/points-mall/shelf-status',
    method: 'put',
    data,
  });
};

// 获取积分订单列表
export const getPointsOrderList = (params) => {
  return request({
    url: '/points-mall/order/list',
    method: 'get',
    params,
  });
};

// 获取积分订单详情
export const getPointsOrderDetail = (id) => {
  return request({
    url: `/points-mall/order/detail/${id}`,
    method: 'get',
  });
};

// 获取积分规则
export const getPointsRules = () => {
  return request({
    url: '/points-mall/rules',
    method: 'get',
  });
};

// 更新积分规则
export const updatePointsRules = (data) => {
  return request({
    url: '/points-mall/rules/update',
    method: 'put',
    data,
  });
};
