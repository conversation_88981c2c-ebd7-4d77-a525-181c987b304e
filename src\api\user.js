import request from '@/utils/request'

// 登录接口
export function login(data) {
  return request({
    url: '/user/login',
    method: 'post',
    data
  })
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/user/info',
    method: 'get'
  })
}

// 获取用户列表
export function getUserList(params) {
  return request({
    url: '/user/list',
    method: 'get',
    params
  })
}

// 获取用户积分明细
export function getUserPointsDetail(params) {
  return request({
    url: '/user/points-detail',
    method: 'get',
    params
  })
}
