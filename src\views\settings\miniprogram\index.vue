<template>
  <div class="miniprogram-settings-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>设置小程序</h2>
      <p>配置小程序相关参数</p>
    </div>

    <!-- 设置卡片 -->
    <el-card class="settings-card" shadow="never">
      <div class="settings-form">
        <!-- 小程序基本信息 -->
        <div class="setting-section">
          <div class="form-field">
            <label class="field-label">小程序名称</label>
            <el-input
              v-model="settings.appName"
              :class="{ 'is-error': errors.appName }"
              readonly
            />
            <div v-if="errors.appName" class="error-message">
              {{ errors.appName }}
            </div>
          </div>

          <div class="form-field">
            <label class="field-label">小程序APPID</label>
            <el-input
              v-model="settings.appId"
              :class="{ 'is-error': errors.appId }"
              readonly
            />
            <div v-if="errors.appId" class="error-message">
              {{ errors.appId }}
            </div>
          </div>

          <div class="form-field">
            <label class="field-label">小程序秘钥 (AppSecret)</label>
            <el-input
              v-model="settings.appSecret"
              type="password"
              show-password
              :class="{ 'is-error': errors.appSecret }"
              readonly
            />
            <div v-if="errors.appSecret" class="error-message">
              {{ errors.appSecret }}
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import { getMiniprogramSettings } from '@/api/settings';

const router = useRouter();

// 设置数据
const settings = reactive({
  appName: '',
  appId: '',
  appSecret: '',
});

// 错误信息
const errors = reactive({
  appName: '',
  appId: '',
  appSecret: '',
});

// 加载设置数据
const loadSettings = async () => {
  try {
    const response = await getMiniprogramSettings();
    if (response.code === 200) {
      Object.assign(settings, response.data);
    }
  } catch (error) {
    console.error('加载设置失败:', error);
    ElMessage.error('加载设置失败');
  }
};

onMounted(() => {
  loadSettings();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables.scss' as *;

.miniprogram-settings-page {
  padding: $spacing-xl;
  background-color: $bg-color;
  min-height: 100vh;
  max-width: 1280px;
  margin: 0 auto;

  .page-header {
    margin-bottom: $spacing-xl;

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: $text-color-dark;
      margin-bottom: $spacing-xs;
    }

    p {
      font-size: 14px;
      color: $text-color-light;
    }
  }

  .settings-card {
    border-radius: 10px;

    .settings-form {
      .setting-section {
        margin-bottom: 32px;

        .form-field {
          margin-bottom: 24px;

          .field-label {
            display: block;
            font-size: 14px;
            color: #86909c;
            margin-bottom: 8px;
            font-weight: normal;
          }

          .error-message {
            font-size: 12px;
            color: #f56565;
            margin-top: 4px;
            line-height: 1.4;
          }
        }
      }
    }
  }
}

// Element Plus 样式覆盖
:deep(.el-card__body) {
  padding: $spacing-xl;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  padding: 12px 16px;
  background-color: #f2f3f5;

  &.is-error {
    border-color: #f56565;
    box-shadow: 0 0 0 1px #f56565;
  }
}

:deep(.el-input__inner) {
  font-size: 14px;
}

:deep(.el-input__inner::placeholder) {
  color: #9ca3af;
}

:deep(.el-input.is-error .el-input__wrapper) {
  border-color: #f56565;
  box-shadow: 0 0 0 1px #f56565;
}
</style>
