// 设置相关的mock数据

// 邀请链接统计数据
const getInviteStats = {
  url: '/api/settings/invite/stats',
  method: 'get',
  response: () => {
    return {
      code: 200,
      message: 'success',
      data: {
        inviteCount: 156,
        totalPoints: 24580,
      },
    };
  },
};

// 邀请链接列表数据
const getInviteList = {
  url: '/api/settings/invite/list',
  method: 'get',
  response: (config) => {
    const { query } = config;
    const {
      page = 1,
      pageSize = 10,
      inviter,
      invitee,
      startDate,
      endDate,
    } = query;

    // 模拟数据
    const mockData = [
      {
        id: 1,
        inviter: '管理员',
        invitee: '商家 A',
        inviteTime: '2024-01-01 10:00',
        status: '已接受',
        points: 100,
      },
      {
        id: 2,
        inviter: '管理员',
        invitee: '商家 B',
        inviteTime: '2024-01-02 11:30',
        status: '待确认',
        points: 0,
      },
      {
        id: 3,
        inviter: '客服',
        invitee: '商家 C',
        inviteTime: '2024-01-03 14:15',
        status: '已拒绝',
        points: 0,
      },
      {
        id: 4,
        inviter: '运营专员',
        invitee: '商家 D',
        inviteTime: '2024-01-04 09:20',
        status: '已接受',
        points: 150,
      },
      {
        id: 5,
        inviter: '客服',
        invitee: '商家 E',
        inviteTime: '2024-01-05 14:30',
        status: '待确认',
        points: 0,
      },
      {
        id: 6,
        inviter: '管理员',
        invitee: '商家 F',
        inviteTime: '2024-01-06 16:45',
        status: '已接受',
        points: 200,
      },
      {
        id: 7,
        inviter: '运营专员',
        invitee: '商家 G',
        inviteTime: '2024-01-07 10:15',
        status: '已拒绝',
        points: 0,
      },
      {
        id: 8,
        inviter: '客服',
        invitee: '商家 H',
        inviteTime: '2024-01-08 13:20',
        status: '已接受',
        points: 120,
      },
      {
        id: 9,
        inviter: '管理员',
        invitee: '商家 I',
        inviteTime: '2024-01-09 11:30',
        status: '待确认',
        points: 0,
      },
      {
        id: 10,
        inviter: '运营专员',
        invitee: '商家 J',
        inviteTime: '2024-01-10 15:40',
        status: '已接受',
        points: 180,
      },
    ];

    // 模拟筛选
    let filteredData = mockData;

    if (inviter) {
      filteredData = filteredData.filter((item) =>
        item.inviter.includes(inviter),
      );
    }

    if (invitee) {
      filteredData = filteredData.filter((item) =>
        item.invitee.includes(invitee),
      );
    }

    if (startDate) {
      filteredData = filteredData.filter(
        (item) => item.inviteTime >= startDate,
      );
    }

    if (endDate) {
      filteredData = filteredData.filter((item) => item.inviteTime <= endDate);
    }

    // 模拟分页
    const total = filteredData.length;
    const start = (page - 1) * pageSize;
    const end = start + parseInt(pageSize);
    const list = filteredData.slice(start, end);

    return {
      code: 200,
      message: 'success',
      data: {
        list,
        total,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
      },
    };
  },
};

// 获取专属邀请链接
const getExclusiveLink = {
  url: '/api/settings/invite/exclusive-link',
  method: 'get',
  response: () => {
    return {
      code: 200,
      message: 'success',
      data: {
        link: 'https://example.com/invite/12345',
      },
    };
  },
};

// 小程序设置数据
const getMiniprogramSettings = {
  url: '/api/settings/miniprogram',
  method: 'get',
  response: () => {
    return {
      code: 200,
      message: 'success',
      data: {
        appName: '李白眼镜官方小程序',
        appId: 'wx1234567890abcdef',
        appSecret: '3f8e7d9c2b1a0f5e8d7c6b9a2e1f4d3c',
      },
    };
  },
};

// 导出所有设置相关mock接口
export default [
  getInviteStats,
  getInviteList,
  getExclusiveLink,
  getMiniprogramSettings,
];
