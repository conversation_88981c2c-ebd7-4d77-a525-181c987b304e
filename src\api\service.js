import request from '@/utils/request';

// 获取激活列表
export function getActivateList(params) {
  return request({
    url: '/service/activate/list',
    method: 'get',
    params,
  });
}

// 更新激活状态
export function updateActivateStatus(data) {
  return request({
    url: '/service/activate/status',
    method: 'post',
    data,
  });
}

// 获取真伪数据列表
export function getDataList(params) {
  return request({
    url: '/service/data/list',
    method: 'get',
    params,
  });
}

// 新增真伪数据
export function addData(data) {
  return request({
    url: '/service/data',
    method: 'post',
    data,
  });
}

// 更新真伪数据
export function updateData(id, data) {
  return request({
    url: `/service/data/${id}`,
    method: 'put',
    data,
  });
}

// 删除真伪数据
export function deleteData(id) {
  return request({
    url: `/service/data/${id}`,
    method: 'delete',
  });
}

// 获取真伪验证数据列表
export function getVerificationDataList(params) {
  return request({
    url: '/service/verificationData/list',
    method: 'get',
    params,
  });
}

// 获取激活详情
export function getActivateDetail(id) {
  return request({
    url: `/service/activate/detail/${id}`,
    method: 'get',
  });
}

// 获取真伪数据详情
export function getVerificationDataDetail(id) {
  return request({
    url: `/service/verificationData/detail/${id}`,
    method: 'get',
  });
}
