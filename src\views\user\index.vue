<template>
  <div class="user-page">
    <transition name="fade" mode="out-in">
      <div v-if="!showPointsDetail" key="userList">
        <div class="page-header">
          <h2>用户管理</h2>
          <p>管理系统的所有用户信息</p>
        </div>

        <!-- 搜索区域 -->
        <el-card class="search-card" shadow="never">
          <div class="search-form">
            <div class="search-inputs">
              <div class="search-item">
                <div class="search-label">用户手机号</div>
                <el-input v-model="searchForm.phone" placeholder="请输入手机号" clearable>
                  <template #prefix>
                    <el-icon><Phone /></el-icon>
                  </template>
                </el-input>
              </div>
              <div class="search-item">
                <div class="search-label">微信昵称</div>
                <el-input v-model="searchForm.wechatId" placeholder="请输入微信昵称" clearable>
                  <template #prefix>
                    <el-icon><ChatDotRound /></el-icon>
                  </template>
                </el-input>
              </div>
            </div>
            <div class="buttons-row">
              <div class="search-actions">
                <el-button class="btn-reset" @click="resetSearch" plain :disabled="isResetDisabled">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
                <el-button class="btn-query" type="primary" @click="searchUsers">
                  <el-icon><Search /></el-icon>
                  查询
                </el-button>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 用户列表 -->
        <el-card class="user-list-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">用户列表</span>
            </div>
          </template>

          <div class="table-container">
            <!-- 表头 -->
            <div class="table-header">
              <div class="column phone-column">手机号码</div>
              <div class="column wechat-column">微信昵称</div>
              <div class="column points-column">积分数量</div>
              <div class="column time-column">最后修改时间</div>
              <div class="column action-column">操作</div>
            </div>
            <!-- 表格内容 -->
            <div class="table-body" v-loading="loading">
              <div v-for="(user, index) in userList" :key="index" class="table-row">
                <!-- 手机号码 -->
                <div class="column phone-column">{{ user.phone }}</div>
                <!-- 微信昵称 -->
                <div class="column wechat-column">{{ user.wechatName }}</div>
                <!-- 积分数量 -->
                <div class="column points-column">{{ user.points }}</div>
                <!-- 最后修改时间 -->
                <div class="column time-column">{{ user.lastModifiedTime }}</div>
                <!-- 操作 -->
                <div class="column action-column">
                  <el-button type="primary" link @click="viewUserPointsDetail(user)">
                    <el-icon><Document /></el-icon>
                    查看明细
                  </el-button>
                </div>
              </div>
            </div>
            <!-- 分页 -->
            <div class="pagination">
              <div class="pagination-info">显示 {{ (currentPage - 1) * pageSize + 1 }}-{{ Math.min(currentPage * pageSize, total) }} 条，共 {{ total }} 条</div>
              <el-pagination background layout="prev, pager, next" :total="total" :page-size="pageSize" :current-page="currentPage" @current-change="handlePageChange" />
            </div>
          </div>
        </el-card>
      </div>

      <user-points-detail v-else key="pointsDetail" :user-id="currentUserId" @back="handleBackToList" />
    </transition>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue'
import { getUserList } from '@/api/user'
import { ElMessage } from 'element-plus'
import UserPointsDetail from './points-detail/index.vue'

// 搜索表单
const searchForm = reactive({
  phone: '',
  wechatId: ''
})

// 用户列表数据
const userList = ref([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(5)

// 当前用户ID
const currentUserId = ref(null)

// 是否显示积分明细页面
const showPointsDetail = ref(false)

// 获取用户列表
const fetchUserList = async () => {
  loading.value = true
  try {
    const res = await getUserList({
      page: currentPage.value,
      pageSize: pageSize.value,
      phone: searchForm.phone,
      wechatId: searchForm.wechatId
    })
    userList.value = res.data
    total.value = res.total
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 页码变化处理
const handlePageChange = (page) => {
  currentPage.value = page
  fetchUserList()
}

// 搜索用户
const searchUsers = () => {
  currentPage.value = 1 // 搜索时重置为第一页
  fetchUserList()
}

// 是否按钮禁用状态
const isResetDisabled = ref(false)

// 重置搜索条件
const resetSearch = () => {
  // 不设置禁用状态
  isResetDisabled.value = false

  searchForm.phone = ''
  searchForm.wechatId = ''
  currentPage.value = 1
  fetchUserList()

  // 强制移除焦点
  document.activeElement.blur()
}

// 直接查看用户积分明细
const viewUserPointsDetail = (user) => {
  currentUserId.value = user.id
  showPointsDetail.value = true
}

// 从明细页返回列表
const handleBackToList = () => {
  showPointsDetail.value = false
}

// 页面加载时获取数据
onMounted(() => {
  fetchUserList()

  // 添加全局点击事件，确保重置按钮在任何点击后都能恢复正常
  document.addEventListener('click', () => {
    isResetDisabled.value = false
  })
})

// 组件卸载时移除事件监听
onBeforeUnmount(() => {
  document.removeEventListener('click', () => {
    isResetDisabled.value = false
  })
})
</script>

<style lang="scss" scoped>
@use 'sass:math';
@use '@/assets/styles/variables.scss' as *;

// 过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.user-page {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;

  .page-header {
    margin-bottom: $card-spacing;

    h2 {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 8px;
      color: $text-color;
    }

    p {
      font-size: 14px;
      color: $info-color;
    }
  }

  // 搜索卡片样式
  .search-card {
    margin-bottom: $card-spacing;
    border-radius: $border-radius;

    :deep(.el-card__body) {
      padding: $search-padding;
    }

    .search-form {
      display: flex;
      flex-direction: column;
    }

    .search-inputs {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      justify-content: space-between;

      .search-item {
        display: flex;
        flex-direction: column;
        width: calc(50% - #{math.div($search-spacing-horizontal, 2)});
        min-width: 240px;

        .search-label {
          font-size: $search-label-size;
          color: $search-label-color;
          margin-bottom: $search-label-margin;
        }

        :deep(.el-input__wrapper) {
          height: $input-height;
        }
      }
    }

    .buttons-row {
      display: flex;
      justify-content: flex-end;
      margin-top: $search-spacing-vertical;
    }

    .search-actions {
      display: flex;

      .el-button {
        height: $btn-height;
        padding: $btn-padding;
        font-size: $btn-font-size;
        border: none;

        .el-icon {
          margin-right: $btn-icon-margin;
        }

        &:not(:first-child) {
          margin-left: $btn-margin;
        }
      }

      :deep(.el-button:hover) {
        border: none !important;
        outline: none !important;
        box-shadow: none !important;
      }

      :deep(.el-button--primary:hover) {
        background-color: $primary-hover !important;
      }
    }
  }

  // 用户列表卡片
  .user-list-card {
    margin-bottom: $card-spacing;
    border-radius: $border-radius;
    margin: 0;

    :deep(.el-card__body) {
      padding: 0;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: $text-color;
      }
    }

    .table-container {
      width: 100%;
      overflow-x: auto;
      margin-top: 0;
    }

    // 表格头部
    .table-header {
      display: flex;
      padding: 0 16px;
      border-radius: 6px;
      height: 50px;
      align-items: center;
      font-weight: 600;
      color: #89929e;
      font-size: 14px;
    }

    // 表格内容
    .table-body {
      .table-row {
        display: flex;
        padding: 16px;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background-color: #f9fafc;
        }
      }
    }

    // 列样式
    .column {
      display: flex;
      align-items: center;

      &.phone-column {
        flex: 1;
        min-width: 120px;
      }

      &.wechat-column {
        flex: 1;
        min-width: 150px;
      }

      &.points-column {
        flex: 1;
        min-width: 100px;
      }

      &.time-column {
        flex: 1;
        min-width: 150px;
      }

      &.action-column {
        flex: 0.8;
        min-width: 100px;
        justify-content: center;
      }
    }

    // 分页
    .pagination {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      border-top: 1px solid #f0f0f0;

      .pagination-info {
        font-size: 14px;
        color: #89929e;
      }
    }
  }
}
</style>
