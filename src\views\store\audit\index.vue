<template>
  <div class="store-audit-page">
    <div class="page-header">
      <h2>审核列表</h2>
      <p>管理系统内所有门店的审核信息</p>
    </div>

    <!-- 筛选区域 -->
    <el-card class="filter-card" shadow="never">
      <div class="filter-row">
        <div class="filter-item">
          <div class="filter-label">选择地区</div>
          <el-select v-model="searchForm.province" placeholder="请选择省/直辖市" clearable class="filter-select">
            <el-option v-for="item in provinceOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </div>

        <div class="filter-item">
          <div class="filter-label">选择城市</div>
          <el-select v-model="searchForm.city" placeholder="请选择市/区" clearable class="filter-select">
            <el-option v-for="item in cityOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </div>

        <div class="filter-item">
          <div class="filter-label">选择区县</div>
          <el-select v-model="searchForm.district" placeholder="请选择区/县" clearable class="filter-select">
            <el-option v-for="item in districtOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </div>

        <div class="filter-item">
          <div class="filter-label">门店名称</div>
          <el-input v-model="searchForm.name" placeholder="请输入门店名称" clearable prefix-icon="Search"></el-input>
        </div>
      </div>

      <div class="filter-row">
        <div class="filter-item">
          <div class="filter-label">门店联系人名称</div>
          <el-input v-model="searchForm.contactName" placeholder="请输入联系人姓名" clearable></el-input>
        </div>

        <div class="filter-item">
          <div class="filter-label">门店联系人电话</div>
          <el-input v-model="searchForm.contactPhone" placeholder="请输入联系人电话" clearable></el-input>
        </div>

        <div class="filter-item">
          <div class="filter-label">对接服务商/业务员</div>
          <el-input v-model="searchForm.businessName" placeholder="请输入对接服务商/业务员" clearable></el-input>
        </div>

        <div class="filter-item">
          <div class="filter-label">审核状态</div>
          <el-select v-model="searchForm.auditStatus" placeholder="请选择审核状态" clearable class="filter-select">
            <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </div>
      </div>

      <div class="filter-row">
        <div class="filter-actions">
          <el-button @click="handleReset" plain class="reset-button" :disabled="isResetDisabled">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="primary" @click="handleSearch" class="search-button">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 门店审核列表 -->
    <el-card class="store-card" shadow="never">
      <el-table :data="storeList" style="width: 100%" v-loading="loading" :header-cell-style="tableHeaderStyle" :cell-style="tableCellStyle">
        <el-table-column label="所属区域" min-width="120">
          <template #default="scope">
            <span class="text-secondary">{{ scope.row.areas.join(', ') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="门店名称" min-width="180">
          <template #default="scope">
            <span class="store-name">{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="contactPhone" label="联系电话" width="150">
          <template #default="scope">
            <span class="text-secondary">{{ scope.row.contactPhone }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="address" label="门店地址" min-width="200">
          <template #default="scope">
            <span class="text-secondary">{{ scope.row.address }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="businessName" label="对接服务商/业务员" width="150">
          <template #default="scope">
            <span class="text-secondary">{{ scope.row.businessName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="auditStatus" label="审核状态" width="120">
          <template #default="scope">
            <div class="status-tag-wrapper">
              <span :class="getStatusClass(scope.row.auditStatus)">{{ scope.row.auditStatus }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="260">
          <template #default="scope">
            <div class="action-buttons">
              <el-button size="small" @click="handleView(scope.row)" class="table-action-btn view-btn">
                <el-icon><View /></el-icon>
                查看
              </el-button>
              <template v-if="scope.row.auditStatus === '待审批'">
                <el-button size="small" @click="handleApprove(scope.row)" class="table-action-btn approve-btn">
                  <el-icon><Check /></el-icon>
                  通过
                </el-button>
                <el-button size="small" @click="handleReject(scope.row)" class="table-action-btn reject-btn">
                  <el-icon><Close /></el-icon>
                  驳回
                </el-button>
              </template>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 分页栏 - 移出卡片，采用简洁的数字分页形式 -->
    <SimplePagination v-model="pagination.page" :page-size="pagination.pageSize" :total="pagination.total" @change="handleCurrentChange" />

    <!-- 查看详情对话框 -->
    <el-dialog title="门店审核详情" v-model="detailVisible" width="500px">
      <div class="detail-content">
        <div class="detail-item">
          <span class="detail-label">门店ID:</span>
          <span class="detail-value">{{ detailData.id }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">门店名称:</span>
          <span class="detail-value">{{ detailData.name }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">所属区域:</span>
          <span class="detail-value">{{ detailData.areas ? detailData.areas.join(', ') : '' }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">门店地址:</span>
          <span class="detail-value">{{ detailData.address }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">联系人:</span>
          <span class="detail-value">{{ detailData.contactName }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">联系电话:</span>
          <span class="detail-value">{{ detailData.contactPhone }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">对接服务商/业务员:</span>
          <span class="detail-value">{{ detailData.businessName }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">业务电话:</span>
          <span class="detail-value">{{ detailData.businessPhone }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">门店类型:</span>
          <span class="detail-value">{{ detailData.type }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">门店星级:</span>
          <span class="detail-value">{{ detailData.level }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">提交时间:</span>
          <span class="detail-value">{{ detailData.submitTime }}</span>
        </div>
        <div class="detail-item">
          <span class="detail-label">审核状态:</span>
          <span class="detail-value">
            <span :class="getStatusClass(detailData.auditStatus)">{{ detailData.auditStatus }}</span>
          </span>
        </div>
        <div v-if="detailData.auditStatus !== '待审批'" class="detail-item">
          <span class="detail-label">审核时间:</span>
          <span class="detail-value">{{ detailData.auditTime }}</span>
        </div>
        <div v-if="detailData.auditStatus !== '待审批'" class="detail-item">
          <span class="detail-label">审核人:</span>
          <span class="detail-value">{{ detailData.auditor }}</span>
        </div>
        <div v-if="detailData.auditComment" class="detail-item">
          <span class="detail-label">审批意见:</span>
          <span class="detail-value">{{ detailData.auditComment }}</span>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailVisible = false">关闭</el-button>
          <template v-if="detailData.auditStatus === '待审批'">
            <el-button type="success" @click="handleApprove(detailData)">审核通过</el-button>
            <el-button type="danger" @click="handleReject(detailData)">审核驳回</el-button>
          </template>
        </span>
      </template>
    </el-dialog>

    <!-- 审核弹窗 -->
    <el-dialog :title="auditForm.result === 'approve' ? '审批通过' : '审批驳回'" v-model="auditVisible" width="440px" :close-on-click-modal="false">
      <div class="audit-content">
        <div class="audit-form-item">
          <div class="audit-label">审批结果</div>
          <el-radio-group v-model="auditForm.result" class="audit-radio-group">
            <el-radio value="approve">通过</el-radio>
            <el-radio value="reject">驳回</el-radio>
          </el-radio-group>
        </div>

        <div class="audit-form-item">
          <div class="audit-label">审批意见</div>
          <el-input v-model="auditForm.comment" type="textarea" :rows="4" placeholder="请输入..." class="audit-textarea"></el-input>
        </div>
      </div>

      <template #footer>
        <span class="audit-dialog-footer">
          <el-button @click="handleCancelAudit" class="audit-cancel-btn">取消</el-button>
          <el-button type="primary" @click="handleConfirmAudit" class="audit-confirm-btn">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, View, Edit, Delete, Plus, Refresh, Check, Close } from '@element-plus/icons-vue'
import { getStoreAuditList, getStoreAuditDetail, approveStoreAudit, rejectStoreAudit, getProvinces, getCities, getDistricts } from '@/api/store'
import SimplePagination from '@/components/SimplePagination.vue'

// 搜索表单
const searchForm = reactive({
  province: '',
  city: '',
  district: '',
  name: '',
  contactName: '',
  contactPhone: '',
  businessName: '',
  auditStatus: ''
})

// 省份选项
const provinceOptions = ref([])

// 城市选项
const cityOptions = ref([])

// 区县选项
const districtOptions = ref([])

// 审批状态选项
const statusOptions = ref([
  { value: '', label: '全部' },
  { value: '待审批', label: '待审批' },
  { value: '审批通过', label: '审批通过' },
  { value: '审批驳回', label: '审批驳回' }
])

// 门店列表
const storeList = ref([])

// 加载状态
const loading = ref(false)

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 5,
  total: 0
})

// 表格表头样式
const tableHeaderStyle = {
  backgroundColor: '#FAFBFC',
  color: '#89929E',
  fontSize: '14px',
  fontWeight: '600',
  height: '50px'
}

// 表格单元格样式
const tableCellStyle = {
  padding: '12px 0',
  fontSize: '14px'
}

// 是否重置按钮禁用状态
const isResetDisabled = ref(false)

// 详情对话框相关
const detailVisible = ref(false)
const detailData = reactive({
  id: '',
  name: '',
  areas: [],
  address: '',
  contactName: '',
  contactPhone: '',
  businessName: '',
  businessPhone: '',
  type: '',
  level: '',
  submitTime: '',
  auditStatus: '',
  auditTime: '',
  auditor: '',
  auditComment: ''
})

// 审核弹窗相关
const auditVisible = ref(false)
const auditForm = reactive({
  result: 'approve',
  comment: '',
  currentRow: null
})

// 获取省份数据
const fetchProvinces = async () => {
  try {
    const res = await getProvinces()
    provinceOptions.value = res.data
  } catch (error) {
    console.error('获取省份数据失败:', error)
  }
}

// 获取城市数据
const fetchCities = async (provinceCode) => {
  try {
    const res = await getCities(provinceCode)
    cityOptions.value = res.data
  } catch (error) {
    console.error('获取城市数据失败:', error)
  }
}

// 获取区县数据
const fetchDistricts = async (cityCode) => {
  try {
    const res = await getDistricts(cityCode)
    districtOptions.value = res.data
  } catch (error) {
    console.error('获取区县数据失败:', error)
  }
}

// 监听省份变化
watch(
  () => searchForm.province,
  async (val) => {
    searchForm.city = ''
    searchForm.district = ''
    cityOptions.value = []
    districtOptions.value = []

    if (val) {
      await fetchCities(val)
    }
  }
)

// 监听城市变化
watch(
  () => searchForm.city,
  async (val) => {
    searchForm.district = ''
    districtOptions.value = []

    if (val && searchForm.province) {
      await fetchDistricts(val)
    }
  }
)

// 获取门店审核列表
const fetchStoreList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      name: searchForm.name,
      province: searchForm.province,
      city: searchForm.city,
      district: searchForm.district,
      contactName: searchForm.contactName,
      contactPhone: searchForm.contactPhone,
      businessName: searchForm.businessName,
      auditStatus: searchForm.auditStatus
    }

    const res = await getStoreAuditList(params)

    if (res && res.code === 200) {
      if (res.data && Array.isArray(res.data.list)) {
        storeList.value = res.data.list
        pagination.total = res.data.total || 0
      } else if (Array.isArray(res.data)) {
        storeList.value = res.data
        pagination.total = res.data.length
      } else {
        console.error('无法识别的数据格式:', res.data)
        ElMessage.warning('数据格式不正确，显示默认数据')
        setDefaultData()
      }
    } else {
      console.error('API返回错误状态:', res)
      ElMessage.warning('获取数据失败，显示默认数据')
      setDefaultData()
    }
  } catch (error) {
    console.error('获取门店审核列表失败, 错误详情:', error)
    ElMessage.error('获取门店审核列表失败，显示默认数据')
    setDefaultData()
  } finally {
    loading.value = false
  }
}

// 设置默认数据的辅助函数
const setDefaultData = () => {
  storeList.value = [
    {
      id: 'DEFAULT-001',
      name: '李白眼镜默认门店',
      areas: ['示例地区'],
      address: '示例地址',
      contactName: '张三',
      contactPhone: '***********',
      businessName: '李四',
      businessPhone: '***********',
      type: '旗舰店',
      level: '金牌门店',
      submitTime: new Date()
        .toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
        .replace(/\//g, '-'),
      auditStatus: '待审批',
      auditTime: '',
      auditor: ''
    }
  ]
  pagination.total = 1
}

// 重置搜索条件
const handleReset = () => {
  // 确保按钮不会被禁用
  isResetDisabled.value = false

  searchForm.province = ''
  searchForm.city = ''
  searchForm.district = ''
  searchForm.name = ''
  searchForm.contactName = ''
  searchForm.contactPhone = ''
  searchForm.businessName = ''
  searchForm.auditStatus = ''
  pagination.page = 1

  cityOptions.value = []
  districtOptions.value = []

  fetchStoreList()

  // 强制移除焦点
  document.activeElement.blur()
}

// 查询
const handleSearch = () => {
  pagination.page = 1
  fetchStoreList()
}

// 查看详情
const handleView = async (row) => {
  try {
    const res = await getStoreAuditDetail(row.id)

    if (res && res.code === 200) {
      Object.assign(detailData, res.data)
      detailVisible.value = true
    } else {
      ElMessage.error('获取门店详情失败')
    }
  } catch (error) {
    console.error('获取门店详情失败:', error)
    ElMessage.error('获取门店详情失败')
  }
}

// 审核通过
const handleApprove = (row) => {
  auditForm.result = 'approve'
  auditForm.comment = ''
  auditForm.currentRow = row
  detailVisible.value = false
  auditVisible.value = true
}

// 审核驳回
const handleReject = (row) => {
  auditForm.result = 'reject'
  auditForm.comment = ''
  auditForm.currentRow = row
  detailVisible.value = false
  auditVisible.value = true
}

// 获取审核状态对应的样式类
const getStatusClass = (status) => {
  switch (status) {
    case '待审批':
      return 'status-tag waiting'
    case '审批通过':
      return 'status-tag approved'
    case '审批驳回':
      return 'status-tag rejected'
    default:
      return 'status-tag'
  }
}

// 分页大小变化
const handleSizeChange = (val) => {
  pagination.pageSize = val
  fetchStoreList()
}

// 页码变化
const handleCurrentChange = (val) => {
  pagination.page = val
  fetchStoreList()
}

// 审核弹窗相关
const handleConfirmAudit = async () => {
  try {
    const row = auditForm.currentRow
    if (!row) return

    if (auditForm.result === 'approve') {
      // 审核通过
      const res = await approveStoreAudit(row.id, auditForm.comment)
      if (res && res.code === 200) {
        ElMessage.success('审批通过成功')

        // 更新本地数据
        const index = storeList.value.findIndex((item) => item.id === row.id)
        if (index !== -1) {
          storeList.value[index].auditStatus = '审批通过'
          storeList.value[index].auditTime = new Date().toLocaleString('zh-CN').replace(/\//g, '-')
          storeList.value[index].auditor = '管理员'
          storeList.value[index].auditComment = auditForm.comment
        }
      } else {
        ElMessage.error('审批通过失败')
      }
    } else {
      // 审核驳回
      const res = await rejectStoreAudit(row.id, auditForm.comment)
      if (res && res.code === 200) {
        ElMessage.success('审批驳回成功')

        // 更新本地数据
        const index = storeList.value.findIndex((item) => item.id === row.id)
        if (index !== -1) {
          storeList.value[index].auditStatus = '审批驳回'
          storeList.value[index].auditTime = new Date().toLocaleString('zh-CN').replace(/\//g, '-')
          storeList.value[index].auditor = '管理员'
          storeList.value[index].auditComment = auditForm.comment
        }
      } else {
        ElMessage.error('审批驳回失败')
      }
    }

    // 关闭审核弹窗
    auditVisible.value = false

    // 重新获取列表数据
    fetchStoreList()
  } catch (error) {
    console.error('审核失败:', error)
    ElMessage.error('审核失败')
  }
}

const handleCancelAudit = () => {
  // 重置表单
  auditForm.result = 'approve'
  auditForm.comment = ''
  auditForm.currentRow = null
  // 取消审核
  auditVisible.value = false
}

// 初始化
onMounted(async () => {
  // 设置默认审核状态为空（全部）
  searchForm.auditStatus = ''

  // 获取省份数据
  await fetchProvinces()

  // 获取门店列表
  fetchStoreList()

  // 添加全局点击事件，确保重置按钮在任何点击后都能恢复正常
  document.addEventListener('click', () => {
    isResetDisabled.value = false
  })
})

// 组件卸载时移除事件监听
onBeforeUnmount(() => {
  document.removeEventListener('click', () => {
    isResetDisabled.value = false
  })
})
</script>

<style lang="scss" scoped>
.store-audit-page {
  max-width: 1280px;
  margin: 0 auto;
  min-height: 500px;
  padding-bottom: 20px;

  .page-header {
    margin-bottom: 24px;

    h2 {
      font-size: 24px;
      margin: 0;
      font-weight: 600;
      color: #1f2937;
    }

    p {
      color: #6b7280;
      margin: 5px 0 0;
    }
  }

  .filter-card {
    margin-bottom: 24px;
    border-radius: 8px;

    :deep(.el-card__body) {
      padding: 20px;
      overflow: hidden;
    }
  }

  .store-card {
    margin-bottom: 24px;
    border-radius: 8px;

    :deep(.el-card__body) {
      padding: 0;
      overflow: hidden;
    }

    :deep(.el-table) {
      border-radius: 8px;
      overflow: hidden;
    }

    :deep(.el-table th) {
      background-color: #fafbfc;
      color: #89929e;
      font-weight: 600;
      height: 50px;
    }

    :deep(.el-table td) {
      padding: 12px 0;
    }

    :deep(.el-table__row) {
      height: 62px;
    }
  }

  .filter-row {
    display: flex;
    align-items: flex-end;
    width: 100%;
    gap: 14px;
    flex-wrap: wrap;
    margin-bottom: 14px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  // 搜索栏按钮样式已移至全局样式，此处无需重复定义

  .filter-item {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 200px;
  }

  .filter-label {
    font-size: 14px;
    color: #86909c;
    margin-bottom: 4px;
  }

  .filter-select,
  .el-input {
    width: 100%;

    :deep(.el-input__inner) {
      height: 42px;
      border-radius: 6px;
    }
  }

  .table-area-tags {
    padding: 5px 0;
  }

  .area-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    max-width: 100%;

    .area-tag {
      margin-right: 0;
      background-color: transparent !important;
      border-color: transparent !important;
      color: #86909c !important;
      font-size: 12px;
      padding: 0;
      height: 24px;
      line-height: 24px;
      white-space: normal;
      max-width: 100%;
      margin-bottom: 4px;
      display: inline-block;
    }
  }

  .status-tag-wrapper {
    display: flex;
  }

  .status-tag {
    font-size: 12px;
    padding: 0 8px;
    height: 24px;
    line-height: 24px;
    border-radius: 15px;
    display: inline-block;

    &.waiting {
      background-color: #e9eeff;
      color: #165dff;
    }

    &.approved {
      background-color: #e8ffea;
      color: #00b42a;
    }

    &.rejected {
      background-color: #ffece8;
      color: #f53f3f;
    }
  }

  .action-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    white-space: nowrap;
  }

  .table-action-btn {
    height: 32px;
    padding: 0 12px;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    margin: 0 4px;
    border-radius: 4px;
    border: none;

    &.view-btn {
      background-color: #e9eeff;
      color: #165dff;

      &:hover {
        background-color: #d0dbff !important;
        color: #165dff !important;
      }
    }

    &.approve-btn {
      background-color: #e5f7e9;
      color: #00b42a;

      &:hover {
        background-color: #c7ecd1 !important;
        color: #00b42a !important;
      }
    }

    &.reject-btn {
      background-color: #ffece8;
      color: #f53f3f;

      &:hover {
        background-color: #ffd8d8 !important;
        color: #f53f3f !important;
      }
    }

    .el-icon {
      margin-right: 4px;
    }
  }

  .pagination-container {
    padding: 20px;
    display: flex;
    justify-content: flex-end;
  }

  .detail-content {
    padding: 10px 0;
  }

  .detail-item {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
  }

  .detail-label {
    width: 100px;
    font-weight: 500;
    color: #606266;
  }

  .detail-value {
    flex: 1;
    color: #303133;
  }

  .text-secondary {
    color: #86909c;
  }

  .store-name {
    font-weight: 500 !important;
    color: #000000 !important;
    font-size: 12px !important;
  }

  // 审核弹窗样式
  .audit-content {
    padding: 20px 0;
  }

  .audit-form-item {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .audit-label {
    font-size: 14px;
    color: #606266;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .audit-radio-group {
    .el-radio {
      margin-right: 20px;

      :deep(.el-radio__label) {
        font-size: 14px;
        color: #303133;
      }
    }
  }

  .audit-textarea {
    :deep(.el-textarea__inner) {
      border-radius: 6px;
      border: 1px solid #dcdfe6;
      font-size: 14px;

      &:focus {
        border-color: #165dff;
      }
    }
  }

  .audit-dialog-footer {
    display: flex;
    justify-content: flex-end;

    .el-button {
      height: 36px;
      padding: 0 20px;
      font-size: 14px;
      border-radius: 6px;
      margin-left: 12px;

      &:first-child {
        margin-left: 0;
      }
    }

    .audit-cancel-btn {
      background-color: #f2f3f5;
      color: #86909c;
      border: none;

      &:hover {
        background-color: #e5e7eb;
        color: #86909c;
      }
    }

    .audit-confirm-btn {
      background-color: #165dff;
      border: none;

      &:hover {
        background-color: #4080ff;
      }
    }
  }
}

// 分页样式已封装到SimplePagination组件中
</style>
