<template>
  <div class="banner-list-page">
    <transition name="fade" mode="out-in">
      <div v-if="!showEditForm" key="list">
        <div class="page-header">
          <h2>轮播图管理</h2>
          <p>管理系统内所有轮播图</p>
        </div>

        <!-- 轮播图列表 -->
        <el-card class="banner-card" shadow="never">
          <el-table :data="bannerList" style="width: 100%" v-loading="loading" :header-cell-style="tableHeaderStyle" :cell-style="tableCellStyle">
            <el-table-column prop="type" label="轮播图类型" min-width="220">
              <template #default="scope">
                <div class="banner-type-wrapper">
                  <span class="banner-type-tag" :style="{ backgroundColor: getBannerTypeColor(scope.row.type) }">
                    {{ getBannerTypeName(scope.row.type) }}
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="count" label="轮播图数量" width="300" align="left">
              <template #default="scope">
                <span class="text-secondary">{{ scope.row.count }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" align="left" fixed="right">
              <template #default="scope">
                <div class="action-buttons">
                  <el-button size="small" @click="handleView(scope.row)" class="table-action-btn view-btn">
                    <el-icon><View /></el-icon>
                    查看
                  </el-button>
                  <el-button size="small" @click="handleEdit(scope.row)" class="table-action-btn edit-btn">
                    <el-icon><Edit /></el-icon>
                    修改
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 分页栏 - 移出卡片，采用简洁的数字分页形式 -->
        <SimplePagination v-model="pagination.page" :page-size="pagination.pageSize" :total="pagination.total" @change="handleCurrentChange" />
      </div>

      <BannerEditForm v-else key="edit-form" @cancel="handleCancelEdit" @save="handleSaveBanner" />
    </transition>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import { getBannerList } from '@/api/banner'
import BannerEditForm from '@/views/banner/components/BannerEditForm.vue'
import SimplePagination from '@/components/SimplePagination.vue'

// 轮播图类型选项
const typeOptions = ref([
  { id: '', name: '全部类型' },
  { id: 1, name: '首页顶部轮播图' },
  { id: 2, name: '首页顶部背景图' },
  { id: 3, name: '查验真伪引导图' },
  { id: 4, name: '积分领取引导图' },
  { id: 5, name: '产品页小白鸽系列广告图' },
  { id: 6, name: '产品页皛系列广告图' },
  { id: 7, name: '产品页侠系列广告图' },
  { id: 8, name: '产品页度系列广告图' },
  { id: 9, name: '产品页方圆正道广告图' },
  { id: 10, name: '产品页猜系列广告图' },
  { id: 11, name: '产品页小圆满广告图' }
])

// 轮播图类型颜色映射
const typeColorMap = {
  1: '#BAD7FF', // 首页顶部轮播图
  2: '#E8F4FD', // 首页顶部背景图
  3: '#C2F0E0', // 查验真伪引导图
  4: '#FFD8A3', // 积分领取引导图
  5: '#C2F4C5', // 产品页小白鸽系列广告图
  6: '#F0E6FF', // 产品页皛系列广告图
  7: '#FFE6E6', // 产品页侠系列广告图
  8: '#E6F7FF', // 产品页度系列广告图
  9: '#F6FFED', // 产品页方圆正道广告图
  10: '#FFF7E6', // 产品页猜系列广告图
  11: '#F0F5FF' // 产品页小圆满广告图
}

// 获取轮播图类型名称
const getBannerTypeName = (type) => {
  const found = typeOptions.value.find((item) => item.id === type)
  return found ? found.name : '-'
}

// 获取轮播图类型颜色
const getBannerTypeColor = (type) => {
  return typeColorMap[type] || '#f0f0f0'
}

// 控制编辑表单显示
const showEditForm = ref(false)

// 轮播图列表
const bannerList = ref([])
const loading = ref(false)

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 5,
  total: 0
})

// 表格表头样式
const tableHeaderStyle = {
  backgroundColor: '#FAFBFC',
  color: '#89929E',
  fontSize: '14px',
  fontWeight: '600',
  height: '50px'
}

// 表格单元格样式
const tableCellStyle = {
  padding: '12px 0',
  fontSize: '14px'
}

// 获取轮播图列表
const fetchBannerList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize
    }

    const res = await getBannerList(params)
    bannerList.value = res.data.list
    pagination.total = res.data.total
  } catch (error) {
    console.error('获取轮播图列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 查看轮播图
const handleView = (row) => {
  ElMessage.info(`查看ID为 ${row.id} 的轮播图，共${row.count}张图片`)
}

// 编辑轮播图
const handleEdit = (row) => {
  showEditForm.value = true
}

// 取消编辑
const handleCancelEdit = () => {
  showEditForm.value = false
}

// 保存轮播图
const handleSaveBanner = (data) => {
  console.log('保存轮播图数据:', data)
  showEditForm.value = false
  // 刷新列表
  fetchBannerList()
}

// 页码改变
const handleCurrentChange = (page) => {
  pagination.page = page
  fetchBannerList()
}

// 组件挂载后获取数据
onMounted(() => {
  fetchBannerList()
})
</script>

<style lang="scss" scoped>
.banner-list-page {
  max-width: 1280px;
  margin: 0 auto;
  box-sizing: border-box;
  min-height: 500px;
  padding-bottom: 20px;
}

.page-header {
  margin-bottom: 24px;

  h2 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    padding: 0;
    line-height: 32px;
    color: #1d2129;
  }

  p {
    font-size: 14px;
    color: #86909c;
    margin: 8px 0 0;
    padding: 0;
    line-height: 22px;
  }
}

.banner-card {
  margin-bottom: 24px;
  border-radius: 8px;

  :deep(.el-card__body) {
    padding: 0;
    overflow: hidden;
  }

  :deep(.el-table) {
    border-radius: 8px;
    overflow: hidden;
  }

  :deep(.el-table th) {
    background-color: #fafbfc;
    color: #89929e;
    font-weight: 600;
    height: 50px;
  }

  :deep(.el-table td) {
    padding: 12px 0;
  }

  :deep(.el-table__row) {
    height: 62px;
  }
}

.text-secondary {
  color: #86909c;
}

.banner-type-wrapper {
  display: flex;
}

.banner-type-tag {
  padding: 0 12px;
  height: 24px;
  line-height: 24px;
  border-radius: 15px;
  font-size: 12px;
  color: #333;
  text-align: center;
  display: inline-block;
}

.action-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.table-action-btn {
  height: 32px;
  padding: 0 12px;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  margin: 0 4px;
  border-radius: 4px;
  border: none;
  flex-shrink: 0;
  width: 80px;

  &.view-btn {
    background-color: #e9eeff;
    color: #165dff;
    &:hover {
      background-color: #d0dbff !important;
      color: #165dff !important;
    }
  }

  &.edit-btn {
    background-color: #fff2e9;
    color: #ff7d00;
    &:hover {
      background-color: #ffe9d9 !important;
      color: #ff7d00 !important;
    }
  }

  .el-icon {
    margin-right: 4px;
  }
}

/* 渐入渐出过渡效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 分页样式已封装到SimplePagination组件中
</style>
