<template>
  <el-button
    size="small"
    link
    bg
    :class="buttonConfig.class"
    @click="handleClick"
  >
    <el-icon><component :is="buttonConfig.icon" /></el-icon>
    <div style="padding-left: 5px">
      <slot>{{ buttonConfig.defaultText }}</slot>
    </div>
  </el-button>
</template>

<script setup>
import { computed } from 'vue';
import {
  Search,
  CloseBold,
  View,
  EditPen,
  Delete,
  Check,
  Refresh,
  Plus,
  FolderChecked,
  ArrowLeft,
} from '@element-plus/icons-vue';

const props = defineProps({
  type: {
    type: String,
    default: 'blue',
    validator: (value) =>
      [
        'blue',
        'grey',
        'check',
        'edit',
        'red',
        'green',
        'add',
        'save',
        'delete',
        'reset',
        'back',
      ].includes(value),
  },
});

const buttonConfigurations = {
  blue: { icon: Search, defaultText: '查询', class: 'custom-blue-btn' },
  grey: { icon: CloseBold, defaultText: '取消', class: 'custom-grey-btn' },
  reset: { icon: Refresh, defaultText: '重置', class: 'custom-reset-btn' },
  check: { icon: View, defaultText: '查看', class: 'custom-check-btn' },
  edit: { icon: EditPen, defaultText: '编辑', class: 'custom-edit-btn' },
  red: { icon: Delete, defaultText: '驳回', class: 'custom-red-btn' },
  green: { icon: Check, defaultText: '通过', class: 'custom-green-btn' },
  add: { icon: Plus, defaultText: '新增', class: 'custom-blue-btn' },
  save: { icon: FolderChecked, defaultText: '保存', class: 'custom-blue-btn' },
  delete: { icon: Delete, defaultText: '删除', class: 'custom-delete-btn' },
  back: { icon: ArrowLeft, defaultText: '返回', class: 'custom-back-btn' },
};

const buttonConfig = computed(() => {
  return buttonConfigurations[props.type] || buttonConfigurations.blue;
});

const emit = defineEmits(['click']);

const handleClick = (event) => {
  emit('click', event);
};
</script>

<style lang="scss" scoped>
/* Blue Button */
.custom-blue-btn {
  font-size: 16px;
  width: 80px;
  height: 40px;
  border-radius: 8px;
  background-color: #165dff !important;
  color: #fff !important;
}

/* Grey Button */
.custom-grey-btn {
  font-size: 16px;
  width: 80px;
  height: 40px;
  border-radius: 8px;
  background-color: #f2f3f5 !important;
  color: #1d2129 !important;
  border-color: #f2f3f5 !important;
}

.custom-reset-btn {
  font-size: 16px;
  width: 80px;
  height: 40px;
  border-radius: 8px;
  color: #86909c !important;
  background-color: #f2f3f5 !important;
}

/* Check Button */
.custom-check-btn {
  font-size: 14px;
  width: 80px;
  height: 32px;
  border-radius: 8px;
  background-color: #e9eeff !important;
  color: #165dff !important;
  margin-right: 10px;
}

/* Edit Button */
.custom-edit-btn {
  font-size: 14px;
  width: 80px;
  height: 32px;
  border-radius: 8px;
  background-color: #fff7e6 !important;
  color: #fa8c16 !important;
  border-color: #fff7e6 !important;
  margin-left: 8px;
}

/* Red Button */
.custom-red-btn {
  font-size: 14px;
  width: 80px;
  height: 32px;
  border-radius: 8px;
  background-color: #ffeeee !important;
  color: #f53f3f !important;
  margin-right: 10px;
}

/* Green Button */
.custom-green-btn {
  font-size: 14px;
  width: 80px;
  height: 32px;
  border-radius: 8px;
  background-color: #f0f9eb !important;
  color: #67c23a !important;
  margin-right: 10px;
}
/* 删除按钮 */
.custom-delete-btn {
  font-size: 14px;
  width: 80px;
  height: 32px;
  border-radius: 8px;
  background-color: #3662ec !important;
  color: #ffffff !important;
}

/* 返回按钮 */
.custom-back-btn {
  font-size: 14px;
  width: 80px;
  height: 32px;
  border-radius: 8px;
  background-color: #f2f3f5 !important;
  color: #86909c !important;
  border-color: #f2f3f5 !important;
}
</style>
