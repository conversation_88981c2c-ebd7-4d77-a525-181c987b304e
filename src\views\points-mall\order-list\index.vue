<template>
  <div class="order-list-page">
    <!-- 列表页面 -->
    <div v-if="!showDetail" class="list-view">
      <!-- 页面标题 -->
      <div class="page-header">
        <h2>积分订单列表</h2>
        <p>管理系统内所有积分订单</p>
      </div>

      <!-- 搜索区域 -->
      <el-card class="search-card" shadow="never">
        <el-form :model="searchForm" ref="searchFormRef">
          <el-row :gutter="14">
            <el-col :span="8">
              <div style="display: flex; flex-direction: column">
                <p>商品名称</p>
                <el-form-item>
                  <el-input
                    v-model="searchForm.productName"
                    placeholder="请输入商品名称"
                    clearable
                  />
                </el-form-item>
              </div>
            </el-col>
            <el-col :span="8">
              <p>商品ID</p>
              <el-form-item>
                <el-input
                  v-model="searchForm.productId"
                  placeholder="请输入商品ID"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-row :gutter="14">
                <el-col :span="12">
                  <p>兑换开始时间</p>
                  <el-form-item>
                    <el-date-picker
                      v-model="searchForm.startTime"
                      type="datetime"
                      placeholder="年/月/日 --:--"
                      format="YYYY/MM/DD HH:mm"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <p>兑换结束时间</p>
                  <el-form-item>
                    <el-date-picker
                      v-model="searchForm.endTime"
                      type="datetime"
                      placeholder="年/月/日 --:--"
                      format="YYYY/MM/DD HH:mm"
                      value-format="YYYY-MM-DD HH:mm:ss"
                      clearable
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <el-row :gutter="14">
            <el-col :span="8">
              <p>兑换人名称</p>
              <el-form-item>
                <el-input
                  v-model="searchForm.exchangeUserName"
                  placeholder="请输入兑换人昵称"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <p>订单状态</p>
              <el-form-item>
                <el-select
                  v-model="searchForm.orderStatus"
                  placeholder="全部"
                  clearable
                  style="width: 100%"
                >
                  <el-option label="待发货" :value="1" />
                  <el-option label="已发货" :value="2" />
                  <el-option label="已完成" :value="3" />
                  <el-option label="已取消" :value="4" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <p>消耗积分区间</p>
              <el-row :gutter="16">
                <el-form-item>
                  <el-col :span="12">
                    <el-input
                      v-model="searchForm.startPoints"
                      placeholder="起始积分"
                      clearable
                    />
                  </el-col>
                  <el-col :span="12">
                    <el-input
                      v-model="searchForm.endPoints"
                      placeholder="结束积分"
                      clearable
                    />
                  </el-col>
                </el-form-item>
              </el-row>
            </el-col>
          </el-row>
          <CustomBtn type="blue" @click="handleSearch" />
          <CustomBtn type="reset" @click="handleReset" />
        </el-form>
      </el-card>

      <!-- 数据表格 -->
      <el-table :data="tableData" style="border-radius: 8px;" v-loading="loading">
        <el-table-column prop="productId" label="商品ID" min-width="100" />
        <el-table-column prop="productName" label="商品名称" min-width="180" />
        <el-table-column prop="points" label="消耗积分" min-width="100" />
        <el-table-column prop="exchangeTime" label="兑换时间" min-width="180" />
        <el-table-column prop="exchangeUser" label="兑换人" min-width="120" />
        <el-table-column prop="orderStatus" label="订单状态" min-width="120">
          <template #default="{ row }">
            <el-tag
              :class="getStatusClass(row.statusCode)"
              effect="light"
              size="small"
            >
              {{ row.orderStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <CustomBtn
              style="margin: 0 3px"
              type="check"
              @click="viewDetail(row.id)"
            />
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <SimplePagination
          v-model="pagination.page"
          :page-size="pagination.size"
          :total="pagination.total"
          @change="handleCurrentChange"
        />
      </div>
    </div>
    <LopTransition>
      <!-- 详情页面 -->
      <OrderDetail
        v-if="showDetail"
        :order-id="selectedOrderId"
        @back="handleBackToList"
      />
    </LopTransition>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { getPointsOrderList } from '@/api/points-mall';
import CustomBtn from '@/components/custom-btn.vue';
import SimplePagination from '@/components/SimplePagination.vue';
import OrderDetail from './detail.vue';
import LopTransition from '@/components/LopTransition.vue';

// 搜索表单
const searchForm = reactive({
  productName: '',
  productId: '',
  exchangeUserName: '',
  startTime: '',
  endTime: '',
  startPoints: '',
  endPoints: '',
  orderStatus: '',
});

const searchFormRef = ref(null);

// 表格数据
const tableData = ref([]);
const loading = ref(false);

// 组件切换状态
const showDetail = ref(false);
const selectedOrderId = ref(null);

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0,
});

// 获取订单列表数据
const fetchOrderList = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchForm,
      page: pagination.page,
      size: pagination.size,
    };

    const res = await getPointsOrderList(params);
    if (res.code === 200) {
      tableData.value = res.data.list;
      pagination.total = res.data.total;
    } else {
      ElMessage.error(res.message || '获取订单列表失败');
    }
  } catch (error) {
    console.error('获取订单列表出错:', error);
    ElMessage.error('获取订单列表失败');
  } finally {
    loading.value = false;
  }
};

// 查询
const handleSearch = () => {
  pagination.page = 1;
  fetchOrderList();
};

// 重置
const handleReset = () => {
  searchFormRef.value.resetFields();
  pagination.page = 1;
  fetchOrderList();
};

// 页码变化
const handleCurrentChange = (page) => {
  pagination.page = page;
  fetchOrderList();
};

// 查看详情
const viewDetail = (id) => {
  selectedOrderId.value = id;
  showDetail.value = true;
};

// 返回列表
const handleBackToList = () => {
  showDetail.value = false;
  selectedOrderId.value = null;
};

// 获取状态标签类型 , 旧版本不存在primary
const getStatusType = (statusCode) => {
  switch (statusCode) {
    case 1:
      return 'warning'; // 待发货
    case 2:
      return 'info'; // 已发货
    case 3:
      return 'success'; // 已完成
    case 4:
      return 'danger'; // 已取消
    default:
      return 'info';
  }
};

// 获取状态对应的自定义类名
const getStatusClass = (statusCode) => {
  switch (statusCode) {
    case 1:
      return 'status-pending'; // 待发货
    case 2:
      return 'status-shipped'; // 已发货
    case 3:
      return 'status-completed'; // 已完成
    case 4:
      return 'status-cancelled'; // 已取消
    default:
      return 'status-default';
  }
};

// 页面加载时获取数据
onMounted(() => {
  fetchOrderList();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables.scss' as *;

.order-list-page {
  padding: $spacing-xl;
  background-color: $bg-color;
  max-width: 1280px;
  margin: 0 auto;
  .page-header {
    margin-bottom: $spacing-xl;

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: $text-color-dark;
      margin-bottom: $spacing-xs;
    }

    p {
      font-size: 14px;
      color: $text-color-light;
    }
  }

  .search-card {
    margin-bottom: $spacing-lg;
    border-radius: 10px;
  }

  .table-card {
    border-radius: $border-radius;
  }
}
.pagination-container {
  margin-top: 0px;
  display: flex;
  justify-content: center;
}
// Element Plus 样式覆盖
:deep(.el-card__body) {
  padding: $spacing-xl;
}

:deep(.el-form-item__label) {
  font-size: 14px;
  color: #86909c;
  margin-bottom: 4px;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-date-editor) {
  width: 100%;
}

// 自定义订单状态标签样式
:deep(.status-pending) {
  --el-tag-bg-color: #fef9c3;
  --el-tag-border-color: #f8d486;
  --el-tag-text-color: #854d0f;
  --el-tag-border-radius: 12px;
  font-weight: bold;
}

:deep(.status-shipped) {
  --el-tag-bg-color: #edf2fc;
  --el-tag-border-color: #a8c1f0;
  --el-tag-text-color: #409eff;
  --el-tag-border-radius: 12px;
  font-weight: bold;
}

:deep(.status-completed) {
  --el-tag-bg-color: #f0f9eb;
  --el-tag-border-color: #b3e19d;
  --el-tag-text-color: #67c23a;
  --el-tag-border-radius: 12px;
  font-weight: bold;
}

:deep(.status-cancelled) {
  --el-tag-bg-color: #fef0f0;
  --el-tag-border-color: #fbc4c4;
  --el-tag-text-color: #f56c6c;
  --el-tag-border-radius: 12px;
  font-weight: bold;
}

:deep(.status-default) {
  --el-tag-bg-color: #f4f4f5;
  --el-tag-border-color: #e9e9eb;
  --el-tag-text-color: #909399;
  --el-tag-border-radius: 12px;
  font-weight: bold;
}
/* 调整表头样式 */
:deep(.el-table__header th) {
  background-color: #f9fafb;
  height: 50px;
  line-height: 50px;
}
</style>
