// 轮播图管理相关的mock接口

// 轮播图类型及背景色配置
const bannerTypes = [
  { id: 1, name: '首页顶部轮播图', color: '#BAD7FF' },
  { id: 2, name: '首页顶部背景图', color: '#E8F4FD' },
  { id: 3, name: '查验真伪引导图', color: '#C2F0E0' },
  { id: 4, name: '积分领取引导图', color: '#FFD8A3' },
  { id: 5, name: '产品页小白鸽系列广告图', color: '#C2F4C5' },
  { id: 6, name: '产品页皛系列广告图', color: '#F0E6FF' },
  { id: 7, name: '产品页侠系列广告图', color: '#FFE6E6' },
  { id: 8, name: '产品页度系列广告图', color: '#E6F7FF' },
  { id: 9, name: '产品页方圆正道广告图', color: '#F6FFED' },
  { id: 10, name: '产品页猜系列广告图', color: '#FFF7E6' },
  { id: 11, name: '产品页小圆满广告图', color: '#F0F5FF' }
]

// 模拟数据
const bannerList = [
  {
    id: 'CB-001',
    type: 1,
    typeName: '首页顶部轮播图',
    count: 5,
    images: [
      'https://cdn.pixabay.com/photo/2018/01/11/09/52/woman-3075704_1280.jpg',
      'https://cdn.pixabay.com/photo/2016/11/29/09/41/bag-1868758_1280.jpg',
      'https://cdn.pixabay.com/photo/2016/11/23/18/14/men-1854191_1280.jpg',
      'https://cdn.pixabay.com/photo/2018/01/29/17/01/woman-3116587_1280.jpg',
      'https://cdn.pixabay.com/photo/2016/11/23/14/37/blur-1853262_1280.jpg'
    ]
  },
  {
    id: 'CB-002',
    type: 2,
    typeName: '首页顶部背景图',
    count: 3,
    images: ['https://cdn.pixabay.com/photo/2018/01/11/09/52/woman-3075704_1280.jpg', 'https://cdn.pixabay.com/photo/2016/11/29/09/41/bag-1868758_1280.jpg', 'https://cdn.pixabay.com/photo/2016/11/23/18/14/men-1854191_1280.jpg']
  },
  {
    id: 'CB-003',
    type: 3,
    typeName: '查验真伪引导图',
    count: 4,
    images: ['https://cdn.pixabay.com/photo/2018/01/11/09/52/woman-3075704_1280.jpg', 'https://cdn.pixabay.com/photo/2016/11/29/09/41/bag-1868758_1280.jpg', 'https://cdn.pixabay.com/photo/2016/11/23/18/14/men-1854191_1280.jpg', 'https://cdn.pixabay.com/photo/2018/01/29/17/01/woman-3116587_1280.jpg']
  },
  {
    id: 'CB-004',
    type: 4,
    typeName: '积分领取引导图',
    count: 2,
    images: ['https://cdn.pixabay.com/photo/2018/01/11/09/52/woman-3075704_1280.jpg', 'https://cdn.pixabay.com/photo/2016/11/29/09/41/bag-1868758_1280.jpg']
  },
  {
    id: 'CB-005',
    type: 5,
    typeName: '产品页小白鸽系列广告图',
    count: 6,
    images: [
      'https://cdn.pixabay.com/photo/2018/01/11/09/52/woman-3075704_1280.jpg',
      'https://cdn.pixabay.com/photo/2016/11/29/09/41/bag-1868758_1280.jpg',
      'https://cdn.pixabay.com/photo/2016/11/23/18/14/men-1854191_1280.jpg',
      'https://cdn.pixabay.com/photo/2018/01/29/17/01/woman-3116587_1280.jpg',
      'https://cdn.pixabay.com/photo/2016/11/23/14/37/blur-1853262_1280.jpg',
      'https://cdn.pixabay.com/photo/2016/11/29/12/13/fence-1869401_1280.jpg'
    ]
  },
  {
    id: 'CB-006',
    type: 6,
    typeName: '产品页皛系列广告图',
    count: 3,
    images: ['https://cdn.pixabay.com/photo/2018/01/11/09/52/woman-3075704_1280.jpg', 'https://cdn.pixabay.com/photo/2016/11/29/09/41/bag-1868758_1280.jpg', 'https://cdn.pixabay.com/photo/2016/11/23/18/14/men-1854191_1280.jpg']
  },
  {
    id: 'CB-007',
    type: 7,
    typeName: '产品页侠系列广告图',
    count: 2,
    images: ['https://cdn.pixabay.com/photo/2018/01/11/09/52/woman-3075704_1280.jpg', 'https://cdn.pixabay.com/photo/2016/11/29/09/41/bag-1868758_1280.jpg']
  }
]

export default [
  // 获取轮播图类型
  {
    url: '/banner/types',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: '获取成功',
        data: bannerTypes
      }
    }
  },

  // 获取轮播图列表
  {
    url: '/mock/banner/list',
    method: 'get',
    response: (request) => {
      const { page = 1, pageSize = 5 } = request.query
      const list = [...bannerList]

      // 计算分页
      const startIndex = (page - 1) * pageSize
      const endIndex = startIndex + parseInt(pageSize)
      const paginatedList = list.slice(startIndex, endIndex)

      return {
        code: 200,
        message: '获取成功',
        data: {
          list: paginatedList,
          total: list.length,
          pageSize: parseInt(pageSize),
          page: parseInt(page)
        }
      }
    }
  },

  // 获取轮播图详情
  {
    url: '/banner/detail/:id',
    method: 'get',
    response: (request) => {
      const { id } = request.params
      const banner = bannerList.find((item) => item.id === id)

      if (!banner) {
        return {
          code: 404,
          message: '轮播图不存在',
          data: null
        }
      }

      return {
        code: 200,
        message: '获取成功',
        data: banner
      }
    }
  },

  // 保存/更新轮播图
  {
    url: '/banner/save',
    method: 'post',
    response: (request) => {
      const { type, banners } = request.body

      // 模拟保存逻辑
      console.log('保存轮播图:', { type, banners })

      return {
        code: 200,
        message: '保存成功',
        data: {
          id: `CB-${Date.now()}`,
          type,
          banners
        }
      }
    }
  },

  // 获取轮播图详细配置（用于编辑页面）
  {
    url: '/banner/config/:id',
    method: 'get',
    response: (request) => {
      const { id } = request.params
      const banner = bannerList.find((item) => item.id === id)

      if (!banner) {
        return {
          code: 404,
          message: '轮播图不存在',
          data: null
        }
      }

      // 模拟详细配置数据
      const config = {
        type: banner.typeName,
        banners: banner.images.map((image, index) => ({
          image,
          backgroundImage: banner.type === 1 ? `${image}?bg=true` : '', // 仅首页顶部轮播图有背景图
          link: `https://example.com/page${index + 1}`,
          sort: index + 1
        }))
      }

      return {
        code: 200,
        message: '获取成功',
        data: config
      }
    }
  }
]
