@use './variables' as *;
@use 'sass:color';

/* Element Plus 组件样式覆盖 */

/* 输入框和文本域样式 */
.el-input__wrapper .el-input__inner,
.el-textarea__wrapper .el-textarea__inner,
.el-input .el-input__inner,
.el-textarea .el-textarea__inner {
  color: $text-color !important;
}

/* 下拉框样式 */
.el-select .el-input__wrapper .el-input__inner {
  color: $text-color !important;
}

/* 下拉菜单项样式 */
.el-select-dropdown__item {
  color: $text-color !important;
}

.el-select-dropdown__item.selected,
.el-select-dropdown__item.hover {
  color: $query-btn-color !important;
}

/* 表单项状态样式 */
.el-form-item.is-success .el-input__wrapper .el-input__inner,
.el-form-item.is-success .el-textarea__wrapper .el-textarea__inner {
  color: $text-color !important;
}

/* 对话框按钮样式 */
.dialog-footer .el-button {
  border: none;
}

.dialog-footer .el-button.el-button--default {
  background-color: $reset-btn-color;
  color: $reset-btn-text-color;
}

.dialog-footer .el-button.el-button--default:hover,
.dialog-footer .el-button.el-button--default:focus {
  background-color: $reset-btn-hover-color;
  color: $reset-btn-text-color;
  border: none;
  outline: none;
  box-shadow: none;
}

/* 重置按钮样式 */
.reset-button.el-button.is-plain,
.btn-reset.el-button.is-plain,
.btn-reset.el-button {
  background-color: $reset-btn-color !important;
  color: $reset-btn-text-color !important;
  border: none !important;
}

.reset-button.el-button.is-plain:hover,
.reset-button.el-button.is-plain:focus,
.btn-reset.el-button.is-plain:hover,
.btn-reset.el-button.is-plain:focus {
  background-color: $reset-btn-hover-color !important;
  color: $reset-btn-text-color !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* 全局按钮高光效果 - 参考用户管理查询按钮 */
.el-button {
  border: none !important;

  &:hover,
  &:focus {
    border: none !important;
    outline: none !important;
  }
}
// 弹出框的样式
