<template>
  <div class="product-add-component">
    <!-- 表单卡片 -->
    <el-card class="form-card" shadow="never">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="product-form"
      >
        <!-- 基本信息 -->
        <el-row>
          <el-col :span="8">
            <div style="padding: 15px 15px 0px 15px">
              <p style="font-size: 14px; font-weight: 500; color: #606266">
                商品名称
              </p>
              <el-form-item prop="productName">
                <el-input
                  v-model="formData.productName"
                  placeholder="请输入商品名称"
                  maxlength="50"
                  show-word-limit
                />
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="padding: 15px 15px 0px 15px">
              <p style="font-size: 14px; font-weight: 500; color: #606266">
                商品ID
              </p>
              <el-form-item prop="productId">
                <el-input
                  v-model="formData.productId"
                  placeholder="请输入商品ID"
                  maxlength="20"
                  @input="validatePositiveInteger($event, 'productId')"
                />
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="padding: 15px 15px 0px 15px">
              <p style="font-size: 14px; font-weight: 500; color: #606266">
                积分商品分类
              </p>
              <el-form-item prop="category">
                <el-select
                  v-model="formData.category"
                  placeholder="请选择分类"
                  style="width: 100%"
                >
                  <el-option label="数码产品" value="digital" />
                  <el-option label="生活用品" value="daily" />
                  <el-option label="服装配饰" value="clothing" />
                  <el-option label="美妆护肤" value="beauty" />
                  <el-option label="其他" value="other" />
                </el-select>
              </el-form-item>
            </div>
          </el-col>
        </el-row>

        <!-- 规格配置 -->
        <div class="section-title">
          <span style="font-size: 20px; font-weight: 600; margin-left: 10px"
            >规格配置</span
          >
          <CustomBtn
            type="add"
            @click="addSpecification"
            style="width: 100px; margin: 10px 10px 10px 0px"
            >新增规格</CustomBtn
          >
        </div>

        <div class="spec-container">
          <!-- 规格表头 -->
          <div class="spec-header">
            <div class="spec-header-item">规格名称</div>
            <div class="spec-header-item">兑换所需积分</div>
            <div class="spec-header-item">库存数量</div>
            <div class="spec-header-item">规格图片</div>
            <div class="spec-header-item">操作</div>
          </div>

          <!-- 规格列表 -->
          <div class="spec-list">
            <div
              v-for="(spec, index) in formData.specifications"
              :key="index"
              class="spec-item"
            >
              <div class="spec-field">
                <el-input
                  v-model="spec.specName"
                  placeholder="请输入规格名称"
                />
              </div>
              <div class="spec-field">
                <el-input
                  v-model="spec.requiredPoints"
                  placeholder="请输入所需积分"
                  type="number"
                  min="1"
                  @input="
                    validatePositiveInteger($event, 'requiredPoints', index)
                  "
                />
              </div>
              <div class="spec-field">
                <el-input
                  v-model="spec.stockCount"
                  placeholder="请输入库存数量"
                  type="number"
                  min="0"
                  @input="validatePositiveInteger($event, 'stockCount', index)"
                />
              </div>
              <div class="spec-field">
                <div class="spec-image-container">
                  <div class="spec-upload">
                    <el-upload
                      :show-file-list="false"
                      :on-success="
                        (response) => handleSpecImageSuccess(response, index)
                      "
                      :on-change="(file) => handleSpecImageChange(file, index)"
                      action="#"
                      :auto-upload="false"
                    >
                      <div class="upload-btn">
                        <el-icon class="upload-icon"><Upload /></el-icon>
                        <div class="upload-text-spec">上传图片</div>
                      </div>
                    </el-upload>
                  </div>
                  <!-- 规格图片预览区域 -->
                  <div v-if="spec.image || spec.imagePreview" class="spec-preview-item">
                    <img
                      :src="spec.image || spec.imagePreview"
                      class="spec-preview-image"
                      alt="规格图片预览"
                    />
                    <div class="spec-preview-actions">
                      <el-button
                        circle
                        size="small"
                        style="color: white; background-color: red;"
                        @click="removeSpecImage(index)"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="spec-field">
                <CustomBtn type="delete" @click="removeSpecification(index)" style="background-color: red!important;"/>
              </div>
            </div>
          </div>
        </div>

        <!-- 商品外观 -->
        <div class="section-title">商品列表图</div>

        <el-form-item label="" prop="productImage" class="full-width-upload">
          <el-upload
            v-if="!formData.productImage && !mainImagePreview"
            class="main-image-upload"
            :show-file-list="false"
            :on-success="handleMainImageSuccess"
            :on-change="handleMainImageChange"
            action="#"
            :auto-upload="false"
            drag
          >
            <div class="upload-area">
              <el-icon class="upload-icon">
                <Upload />
              </el-icon>
              <div class="upload-text">
                <p style="font-weight: bold; color: black">
                  点击上传或者拖拽图片到此处
                </p>
                <br />
                支持 JPG、PNG 格式，建议尺寸 300x300px
              </div>
            </div>
          </el-upload>
        </el-form-item>

        <!-- 商品列表图预览区域 -->
        <div
          v-if="formData.productImage || mainImagePreview"
          class="main-image-preview"
        >
          <div class="preview-image-container">
            <img
              :src="formData.productImage || mainImagePreview"
              class="preview-main-image"
              alt="商品列表图预览"
            />
            <div class="preview-image-actions">
              <el-button                
                circle
                size="small"
                @click="removeMainImage"
                style="color: white; background-color: red;"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </div>

        <div class="gallery-section">
          <div class="gallery-label">商品主图 (可上传1-6张)</div>
          <div class="gallery-container">
            <!-- 左侧上传区域 -->
            <div class="gallery-left">
              <div class="upload-placeholder">
                <el-upload
                  class="gallery-upload-btn"
                  :file-list="formData.productGallery"
                  :on-success="handleGallerySuccess"
                  :on-remove="handleGalleryRemove"
                  :on-change="handleGalleryChange"
                  action="#"
                  :auto-upload="false"
                  :show-file-list="false"
                  :limit="6"
                  multiple
                >
                  <div class="upload-btn-content">
                    <el-icon class="upload-icon"><Plus /></el-icon>
                    <div class="upload-text-small">点击上传图片</div>
                    <div class="upload-tip-small">支持 JPG、PNG 格式</div>
                  </div>
                </el-upload>
              </div>
            </div>

            <!-- 右侧预览区域 -->
            <div class="gallery-right">
              <div
                v-for="(file, index) in formData.productGallery"
                :key="index"
                class="uploaded-image"
              >
                <img
                  :src="file.url || file.response?.url || file.preview"
                  alt="商品图片"
                />
                <div class="image-actions">
                  <el-button
                    circle
                    size="small"
                    style="color: white; background-color: red;"
                    @click="removeGalleryImage(index)"
                  >
                    <el-icon><Close /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 商品详情 -->
        <div class="section-title">商品详情</div>

        <el-form-item label="" prop="productDetail" class="full-width-upload">
          <div class="editor-container">
            <Toolbar
              style="border-bottom: 1px solid #ccc"
              :editor="editorRef"
              :defaultConfig="toolbarConfig"
              :mode="mode"
            />
            <Editor
              style="height: 300px; overflow-y: hidden"
              v-model="formData.productDetail"
              :defaultConfig="editorConfig"
              :mode="mode"
              @onCreated="handleCreated"
              @onChange="handleChange"
            />
          </div>
        </el-form-item>
        <div class="detail-tip">
          提示：可在此编辑商品详情，支持文本格式化，插入图片等操作
        </div>
        <!-- 操作按钮 -->
        <div class="form-actions">
          <CustomBtn type="save" @click="handleSave" :loading="saving" />
          <CustomBtn type="grey" @click="handleCancel" />
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import {
  ref,
  reactive,
  shallowRef,
  onBeforeUnmount,
  watch,
  computed,
} from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Upload, Close, ZoomIn, Delete } from '@element-plus/icons-vue';
import '@wangeditor/editor/dist/css/style.css';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import CustomBtn from '@/components/custom-btn.vue';

// Props
const props = defineProps({
  initialData: {
    type: Object,
    default: () => ({}),
  },
});

// Emits
const emit = defineEmits(['save', 'cancel']);

const formRef = ref();
const saving = ref(false);
const editorRef = shallowRef();
const editorWordCount = ref(0);
const mode = 'default';

// 图片预览相关
const previewVisible = ref(false);
const previewImageUrl = ref('');
const mainImagePreview = ref('');

// 编辑器配置
const toolbarConfig = {
  excludeKeys: ['uploadVideo', 'insertTable', 'codeBlock', 'todo'],
};

const editorConfig = {
  placeholder: '请输入商品详情',
  maxLength: 5000,
  MENU_CONF: {},
};

// 编辑器创建完成时的回调
const handleCreated = (editor) => {
  editorRef.value = editor;
};

// 编辑器内容变化时的回调
const handleChange = (editor) => {
  editorWordCount.value = editor.getText().length;
};

// 表单数据
const formData = reactive({
  productName: '',
  productId: '',
  category: '',
  productImage: '',
  productGallery: [],
  productDetail: '',
  specifications: [
    {
      specName: '',
      requiredPoints: '',
      stockCount: '',
      image: '',
      imagePreview: '',
    },
  ],
  ...props.initialData,
});

// 监听props变化
watch(
  () => props.initialData,
  (newData) => {
    Object.assign(formData, newData);
  },
  { deep: true },
);

// 验证规则
const rules = {
  productName: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
  productId: [{ required: true, message: '请输入商品ID', trigger: 'blur' }],
  category: [{ required: true, message: '请选择商品分类', trigger: 'change' }],
  productImage: [
    { required: true, message: '请上传商品主图', trigger: 'change' },
  ],
};

// 新增规格
const addSpecification = () => {
  formData.specifications.push({
    specName: '',
    requiredPoints: '',
    stockCount: '',
    image: '',
    imagePreview: '',
  });
};

// 删除规格
const removeSpecification = async (index) => {
  if (formData.specifications.length > 1) {
    try {
      await ElMessageBox.confirm('确定要删除这个规格吗？', '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });
      formData.specifications.splice(index, 1);
      ElMessage.success('规格删除成功');
    } catch (error) {
      // 用户取消删除操作
    }
  } else {
    ElMessage.warning('至少保留一个规格');
  }
};

// 处理规格图片文件变化（显示预览）
const handleSpecImageChange = (file, index) => {
  if (file.raw) {
    const reader = new FileReader();
    reader.onload = (e) => {
      formData.specifications[index].imagePreview = e.target.result;
    };
    reader.readAsDataURL(file.raw);
  }
};

// 处理规格图片上传成功
const handleSpecImageSuccess = (response, index) => {
  formData.specifications[index].image = response.url;
  formData.specifications[index].imagePreview = '';
};

// 处理主图文件变化（显示预览）
const handleMainImageChange = (file) => {
  if (file.raw) {
    const reader = new FileReader();
    reader.onload = (e) => {
      mainImagePreview.value = e.target.result;
    };
    reader.readAsDataURL(file.raw);
  }
};

// 处理主图上传成功
const handleMainImageSuccess = (response) => {
  formData.productImage = response.url;
  mainImagePreview.value = '';
};

// 处理商品图集文件变化（显示预览）
const handleGalleryChange = (file, fileList) => {
  // 为每个文件添加预览URL
  fileList.forEach((item) => {
    if (item.raw && !item.preview) {
      const reader = new FileReader();
      reader.onload = (e) => {
        item.preview = e.target.result;
      };
      reader.readAsDataURL(item.raw);
    }
  });
  formData.productGallery = fileList;
};

// 处理商品图集上传成功
const handleGallerySuccess = (response, file, fileList) => {
  formData.productGallery = fileList;
};

// 删除商品图集
const handleGalleryRemove = (file, fileList) => {
  formData.productGallery = fileList;
};

// 删除指定索引的图片
const removeGalleryImage = async (index) => {
  try {
    await ElMessageBox.confirm('确定要删除这张图片吗？', '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    formData.productGallery.splice(index, 1);
    ElMessage.success('图片删除成功');
  } catch (error) {}
};

// 删除主图
const removeMainImage = async () => {
  try {
    await ElMessageBox.confirm('确定要删除商品列表图吗？', '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    formData.productImage = '';
    mainImagePreview.value = '';
    ElMessage.success('商品列表图删除成功');
  } catch (error) {
    // 用户取消删除操作
  }
};

// 删除规格图片
const removeSpecImage = async (index) => {
  try {
    await ElMessageBox.confirm('确定要删除这张规格图片吗？', '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });
    formData.specifications[index].image = '';
    formData.specifications[index].imagePreview = '';
    ElMessage.success('规格图片删除成功');
  } catch (error) {}
};

// 计算是否有规格图片
const hasSpecImages = computed(() => {
  return formData.specifications.some(
    (spec) => spec.image || spec.imagePreview,
  );
});

// 保存商品
const handleSave = async () => {
  try {
    // 检查编辑器内容是否超过字数限制
    if (editorWordCount.value > 5000) {
      ElMessage.error('商品详情不能超过5000字');
      return;
    }
    await formRef.value.validate();
    saving.value = true;
    const data = {
      ...formData,
      productGallery: formData.productGallery.map(
        (file) => file.url || file.response?.url || file,
      ),
    };

    // 直接返回数据，由父组件决定是新增还是更新
    ElMessage.success(props.initialData.id ? '修改商品成功' : '新增商品成功');
    emit('save', data);
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('保存失败，请检查输入信息');
  } finally {
    saving.value = false;
  }
};

// 取消
const handleCancel = () => {
  emit('cancel');
};

// 组件卸载前，销毁编辑器实例
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor === null) return;
  editor.destroy();
});

// 验证正整数输入
const validatePositiveInteger = (value, field, index = null) => {
  // 如果是空值，直接返回
  if (value === '') {
    return;
  }

  const num = parseFloat(value);
  const isInteger = Number.isInteger(num);

  // 验证是否为正整数
  if (
    isNaN(num) ||
    !isInteger ||
    num < 0 ||
    (field !== 'stockCount' && num === 0)
  ) {
    ElMessage.warning(
      `${field === 'productId' ? '商品ID' : field === 'requiredPoints' ? '兑换所需积分' : '库存数量'}必须是${field === 'stockCount' ? '大于等于0' : '大于0'}的整数`,
    );

    // 根据字段和索引更新值
    if (field === 'productId') {
      // 移除非数字字符
      formData.productId = String(formData.productId).replace(/[^\d]/g, '');
      // 如果是0开头，则移除0
      if (formData.productId.startsWith('0') && formData.productId.length > 1) {
        formData.productId = formData.productId.replace(/^0+/, '');
      }
    } else if (index !== null) {
      // 规格相关字段
      if (field === 'requiredPoints') {
        formData.specifications[index].requiredPoints = Math.max(
          1,
          parseInt(formData.specifications[index].requiredPoints) || 1,
        );
      } else if (field === 'stockCount') {
        formData.specifications[index].stockCount = Math.max(
          0,
          parseInt(formData.specifications[index].stockCount) || 0,
        );
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables.scss' as *;

.product-add-component {
  min-height: 100vh;
  max-width: 1280px;
  margin: 0 auto;
  .form-card {
    border-radius: 10px;
  }

  .product-form {
    // 章节标题样式
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: $text-color-dark;
      margin: 10px 0 16px 15px;
      padding-bottom: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &:first-child {
        margin-top: 0;
      }
    }

    // 规格配置样式
    .spec-container {
      margin: 0px 15px 15px 15px;
      overflow: hidden;
    }

    .spec-header {
      display: flex;
      background-color: #f5f7fa;
      padding: 12px 16px;
      margin-bottom: 0;

      .spec-header-item {
        flex: 1;
        font-size: 14px;
        font-weight: 500;
        color: #606266;
        // text-align: center;

        &:last-child {
          flex: 0.6;
        }
      }
    }

    .spec-list {
      .spec-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        border-top: 1px solid #ebeef5;
        background-color: #fff;

        &:last-child {
          margin-bottom: 0;
        }

        .spec-field {
          flex: 1;
          padding: 0 12px;

          &:last-child {
            flex: 0.6;
            text-align: center;
          }
        }

        .spec-image-container {
          display: flex;
          align-items: center;
          gap: 10px;
          min-width: 130px; /* 确保有足够的空间放置两个元素 */

          .spec-upload {
            flex-shrink: 0;
            width: 60px;
            height: 60px;
            
            .upload-btn {
              width: 60px;
              height: 60px;
              border: 1px dashed #d9d9d9;
              border-radius: 6px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              transition: border-color 0.3s;

              &:hover {
                border-color: #409eff;
              }

              .upload-icon {
                font-size: 20px;
                color: #8c939d;
                margin-bottom: 4px;
              }

              .upload-text-spec {
                font-size: 10px;
                color: #8c939d;
                text-align: center;
              }
            }
          }

          .spec-preview-item {
            flex-shrink: 0;
            width: 60px;
            height: 60px;
            border-radius: 6px;
            overflow: hidden;
            position: relative;
            border: 1px solid #d9d9d9;

            .spec-preview-image {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }

            .spec-preview-actions {
              position: absolute;
              top: 4px;
              right: 4px;
              opacity: 0;
              transition: opacity 0.3s;
              display: flex;
              gap: 4px;

              .el-button {
                width: 24px;
                height: 24px;
                min-height: 24px;
                padding: 0;

                .el-icon {
                  font-size: 14px;
                }
              }
            }

            &:hover .spec-preview-actions {
              opacity: 1;
            }
          }
        }
      }
    }

    // 全宽上传表单项
    .full-width-upload {
      :deep(.el-form-item__content) {
        width: 100% !important;
      }
    }

    // 主图上传样式
    .main-image-upload {
      margin-bottom: 24px;
      width: 100%;
      padding: 0 15px;

      :deep(.el-upload-dragger) {
        width: 100%;
        height: 180px;
        border: 2px dashed #d9d9d9;
        border-radius: 6px;
        position: relative;
        overflow: hidden;

        &:hover {
          border-color: #409eff;
        }
      }

      .upload-area {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;

        .upload-icon {
          font-size: 48px;
          color: #8c939d;
          margin-bottom: 16px;
        }

        .upload-text {
          color: #8c939d;
          font-size: 14px;
          text-align: center;
          line-height: 1.4;
        }

        .main-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          position: absolute;
          top: 0;
          left: 0;
        }

        .image-overlay {
          position: absolute;
          top: 10px;
          right: 10px;
          display: flex;
          gap: 8px;
          opacity: 0;
          transition: opacity 0.3s;

          .el-button {
            width: 32px;
            height: 32px;
            min-height: 32px;
            padding: 0;

            .el-icon {
              font-size: 16px;
            }
          }
        }

        &:hover .image-overlay {
          opacity: 1;
        }
      }
    }

    // 主图预览样式
    .main-image-preview {
      margin: 16px 15px 24px 15px;
      width: 100%;

      .preview-image-container {
        width: 100%;
        max-width: 300px;
        height: 180px;
        position: relative;
        border-radius: 6px;
        overflow: hidden;
        border: 1px solid #d9d9d9;

        .preview-main-image {
          width: 100%;
          height: 100%;
          object-fit: contain;
          background-color: #f5f7fa;
        }

        .preview-image-actions {
          position: absolute;
          top: 8px;
          right: 8px;
          opacity: 0;
          transition: opacity 0.3s;
          display: flex;
          gap: 6px;

          .el-button {
            width: 32px;
            height: 32px;
            min-height: 32px;
            padding: 0;
            background-color: rgba(255, 255, 255, 0.8);
            border: none;

            .el-icon {
              font-size: 16px;
            }
          }
        }

        &:hover .preview-image-actions {
          opacity: 1;
        }
      }
    }

    // 图集上传样式
    .gallery-section {
      margin-bottom: 24px;

      .gallery-label {
        font-size: 14px;
        font-weight: bold;
        color: #86909c;
        margin: 10px 0px 10px 15px;
        font-weight: 500;
      }

      .gallery-container {
        display: flex;
        gap: 20px;
        align-items: flex-start;
        margin: 0px 15px 0px 15px;
      }

      .gallery-left {
        flex-shrink: 0;
      }

      .gallery-right {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        flex: 1;
      }

      .upload-placeholder {
        width: 192px;
        height: 108px;
        border: 2px dashed #d9d9d9;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: border-color 0.3s;

        &:hover {
          border-color: #409eff;
        }

        .gallery-upload-btn {
          width: 100%;
          height: 100%;

          :deep(.el-upload) {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }

        .upload-btn-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          text-align: center;

          .upload-icon {
            font-size: 24px;
            color: #8c939d;
            margin-bottom: 8px;
          }

          .upload-text-small {
            font-size: 12px;
            color: #606266;
            margin-bottom: 4px;
          }

          .upload-tip-small {
            font-size: 10px;
            color: #8c939d;
            line-height: 1.2;
          }
        }
      }

      .uploaded-image {
        width: 192px;
        height: 108px;
        position: relative;
        border-radius: 6px;
        overflow: hidden;
        border: 1px solid #d9d9d9;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .image-actions {
          position: absolute;
          top: 4px;
          right: 4px;
          opacity: 0;
          transition: opacity 0.3s;
          display: flex;
          gap: 4px;

          .el-button {
            width: 24px;
            height: 24px;
            min-height: 24px;
            padding: 0;

            .el-icon {
              font-size: 14px;
            }
          }
        }

        &:hover .image-actions {
          opacity: 1;
        }
      }
    }

    // 富文本编辑器样式
    .editor-container {
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      overflow: hidden;
      margin: 0px 15px;

      .editor-word-count {
        text-align: right;
        color: #909399;
        font-size: 12px;
        padding: 5px 10px;
        background-color: #f5f7fa;
        border-top: 1px solid #e4e7ed;
      }
    }

    .detail-tip {
      font-size: 12px;
      color: #909399;
      margin-top: 8px;
      padding-left: 50px;
    }

    // 操作按钮
    .form-actions {
      margin: 40px 0px 20px 20px;

      .el-button {
        min-width: 100px;
        margin: 0 $spacing-md;
      }
    }
  }
}

// Element Plus 样式覆盖
:deep(.el-card__body) {
  padding: $spacing-xl;
}

:deep(.el-form-item__content) {
  margin-left: 0px !important;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-button) {
  border-radius: 6px;
}

:deep(.w-e-toolbar) {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  border-bottom: none !important;
}

:deep(.w-e-text-container) {
  border: none !important;
}

:deep(.w-e-text-placeholder) {
  color: #c0c4cc;
  font-style: normal;
}

// 图片预览样式
.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;

  .preview-image {
    max-width: 100%;
    max-height: 60vh;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}
</style>
