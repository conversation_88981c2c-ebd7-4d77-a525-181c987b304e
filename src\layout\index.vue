<template>
  <div class="layout-container">
    <el-container>
      <el-header class="header">
        <div class="header-left">
          <img src="/logo.png" alt="李白眼镜" class="logo-image" />
        </div>
        <div class="header-right">
          <div class="notification-container">
            <el-badge is-dot class="notification-badge">
              <el-icon class="notification-icon"><Bell /></el-icon>
            </el-badge>
          </div>
          <el-dropdown @command="handleCommand" class="user-dropdown">
            <span class="el-dropdown-link">
              <div class="user-avatar">
                <el-icon class="user-icon"><User /></el-icon>
              </div>
              管理员 <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>
      <el-container class="main-container">
        <el-aside width="256px" class="aside">
          <div class="menu-title">主菜单</div>
          <el-scrollbar style="flex: 1; height: 0; width: 100%" :always="false">
            <el-menu :default-active="activeMenu" class="el-menu-vertical" :collapse="false" background-color="#ffffff" text-color="#606266" active-text-color="#409EFF">
              <!-- 首页 -->
              <el-menu-item index="1" @click="navigateTo('/home')">
                <el-icon><HomeFilled /></el-icon>
                <span>首页</span>
              </el-menu-item>

              <!-- 用户管理 -->
              <el-menu-item index="2" @click="navigateTo('/user')">
                <el-icon><UserFilled /></el-icon>
                <span>用户管理</span>
              </el-menu-item>

              <!-- 活动管理 -->
              <el-sub-menu index="3">
                <template #title>
                  <el-icon><Promotion /></el-icon>
                  <span>活动管理</span>
                </template>
                <el-menu-item index="3-1" @click="navigateTo('/activity/category')" :class="{ 'active-menu-item': activeMenu === '3-1' }">
                  <el-icon><Collection /></el-icon>
                  <span>活动分类</span>
                </el-menu-item>
                <el-menu-item index="3-2" @click="navigateTo('/activity/list')" :class="{ 'active-menu-item': activeMenu === '3-2' }">
                  <el-icon><Tickets /></el-icon>
                  <span>活动列表</span>
                </el-menu-item>
              </el-sub-menu>

              <!-- 门店管理 -->
              <el-sub-menu index="4">
                <template #title>
                  <el-icon><Shop /></el-icon>
                  <span>门店管理</span>
                </template>
                <el-menu-item index="4-1" @click="navigateTo('/store/top')" :class="{ 'active-menu-item': activeMenu === '4-1' }">
                  <el-icon><Top /></el-icon>
                  <span>门店置顶</span>
                </el-menu-item>
                <el-menu-item index="4-2" @click="navigateTo('/store/list')" :class="{ 'active-menu-item': activeMenu === '4-2' }">
                  <el-icon><Tickets /></el-icon>
                  <span>门店列表</span>
                </el-menu-item>
                <el-menu-item index="4-3" @click="navigateTo('/store/audit')" :class="{ 'active-menu-item': activeMenu === '4-3' }">
                  <el-icon><DocumentChecked /></el-icon>
                  <span>审核列表</span>
                </el-menu-item>
                <el-menu-item index="4-4" @click="navigateTo('/store/tags')" :class="{ 'active-menu-item': activeMenu === '4-4' }">
                  <el-icon><PriceTag /></el-icon>
                  <span>门店类型标签</span>
                </el-menu-item>
              </el-sub-menu>

              <!-- 轮播图管理 -->
              <el-sub-menu index="5">
                <template #title>
                  <el-icon><Picture /></el-icon>
                  <span>轮播图管理</span>
                </template>
                <el-menu-item index="5-1" @click="navigateTo('/banner')" :class="{ 'active-menu-item': activeMenu === '5-1' }">
                  <el-icon><PictureRounded /></el-icon>
                  <span>轮播图列表</span>
                </el-menu-item>
              </el-sub-menu>

              <!-- 产品管理 -->
              <el-sub-menu index="6">
                <template #title>
                  <el-icon><Goods /></el-icon>
                  <span>产品管理</span>
                </template>
                <el-menu-item index="6-1" @click="navigateTo('/product/list')" :class="{ 'active-menu-item': activeMenu === '6-1' }">
                  <el-icon><List /></el-icon>
                  <span>商品列表</span>
                </el-menu-item>
                <el-menu-item index="6-2" @click="navigateTo('/product/category')" :class="{ 'active-menu-item': activeMenu === '6-2' }">
                  <el-icon><FolderOpened /></el-icon>
                  <span>分类管理</span>
                </el-menu-item>
              </el-sub-menu>

              <!-- 售后激活 -->
              <el-sub-menu index="7">
                <template #title>
                  <el-icon><Service /></el-icon>
                  <span>售后激活</span>
                </template>
                <el-menu-item index="7-1" @click="navigateTo('/service/activate')" :class="{ 'active-menu-item': activeMenu === '7-1' }">
                  <el-icon><List /></el-icon>
                  <span>激活列表</span>
                </el-menu-item>
                <el-menu-item index="7-2" @click="navigateTo('/service/verificationData')" :class="{ 'active-menu-item': activeMenu === '7-2' }">
                  <el-icon><DataAnalysis /></el-icon>
                  <span>真伪数据</span>
                </el-menu-item>
              </el-sub-menu>

              <!-- 红包领取列表 -->
              <el-menu-item index="8" @click="navigateTo('/redpacket')">
                <el-icon><Money /></el-icon>
                <span>红包领取列表</span>
              </el-menu-item>

              <!-- 积分商城 -->
              <el-sub-menu index="9">
                <template #title>
                  <el-icon><ShoppingCart /></el-icon>
                  <span>积分商城</span>
                </template>
                <el-menu-item index="9-1" @click="navigateTo('/points/goods')" :class="{ 'active-menu-item': activeMenu === '9-1' }">
                  <el-icon><Goods /></el-icon>
                  <span>积分商品</span>
                </el-menu-item>
                <el-menu-item index="9-2" @click="navigateTo('/points/orders')" :class="{ 'active-menu-item': activeMenu === '9-2' }">
                  <el-icon><Tickets /></el-icon>
                  <span>积分订单</span>
                </el-menu-item>
                <el-menu-item index="9-3" @click="navigateTo('/points/rules')" :class="{ 'active-menu-item': activeMenu === '9-3' }">
                  <el-icon><SetUp /></el-icon>
                  <span>积分规则</span>
                </el-menu-item>
              </el-sub-menu>

              <!-- 协议管理 -->
              <el-sub-menu index="10">
                <template #title>
                  <el-icon><Document /></el-icon>
                  <span>协议管理</span>
                </template>
                <el-menu-item index="10-1" @click="navigateTo('/agreement/list')" :class="{ 'active-menu-item': activeMenu === '10-1' }">
                  <el-icon><List /></el-icon>
                  <span>协议列表</span>
                </el-menu-item>
              </el-sub-menu>

              <!-- 子账号 -->
              <el-sub-menu index="11">
                <template #title>
                  <el-icon><Avatar /></el-icon>
                  <span>子账号</span>
                </template>
                <el-menu-item index="11-1" @click="navigateTo('/account/roles')" :class="{ 'active-menu-item': activeMenu === '11-1' }">
                  <el-icon><Lock /></el-icon>
                  <span>角色权限</span>
                </el-menu-item>
                <el-menu-item index="11-2" @click="navigateTo('/account/list')" :class="{ 'active-menu-item': activeMenu === '11-2' }">
                  <el-icon><List /></el-icon>
                  <span>账号列表</span>
                </el-menu-item>
              </el-sub-menu>

              <!-- 其他配置 -->
              <el-sub-menu index="12">
                <template #title>
                  <el-icon><Setting /></el-icon>
                  <span>其他配置</span>
                </template>
                <el-menu-item index="12-1" @click="navigateTo('/settings/miniprogram')" :class="{ 'active-menu-item': activeMenu === '12-1' }">
                  <el-icon><Monitor /></el-icon>
                  <span>设置小程序</span>
                </el-menu-item>
                <el-menu-item index="12-2" @click="navigateTo('/settings/invite-link')" :class="{ 'active-menu-item': activeMenu === '12-2' }">
                  <el-icon><Link /></el-icon>
                  <span>生成邀请商家链接</span>
                </el-menu-item>
              </el-sub-menu>
            </el-menu>
          </el-scrollbar>
        </el-aside>
        <el-main class="main">
          <!-- 路由出口 -->
          <router-view />
        </el-main>
      </el-container>
      <!-- 添加底部版权栏 -->
      <el-footer height="40px" class="footer">
        <div class="footer-content">
          <span>© 2023 李白眼镜 保留所有权利</span>
          <div class="footer-links">
            <a href="#">使用条款</a>
            <a href="#">隐私政策</a>
            <a href="#">联系我们</a>
          </div>
        </div>
      </el-footer>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/store/user'
import { ElMessageBox } from 'element-plus'
import {
  UserFilled,
  Setting,
  ArrowDown,
  Bell,
  Goods,
  ShoppingCart,
  Document,
  Avatar,
  List,
  Money,
  Shop,
  Promotion,
  DataLine,
  TrendCharts,
  Service,
  FolderOpened,
  Top,
  PriceTag,
  Picture,
  PictureRounded,
  DocumentChecked,
  DataAnalysis,
  Tickets,
  SetUp,
  Collection,
  User,
  Lock,
  HomeFilled
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 当前激活的菜单项
const activeMenu = computed(() => {
  const path = route.path
  if (path.includes('/home')) {
    return '1'
  } else if (path.includes('/user')) {
    return '2'
  } else if (path.includes('/activity/category')) {
    return '3-1'
  } else if (path.includes('/activity')) {
    return '3-2'
  } else if (path.includes('/store/top')) {
    return '4-1'
  } else if (path.includes('/store/list')) {
    return '4-2'
  } else if (path.includes('/store/audit')) {
    return '4-3'
  } else if (path.includes('/store/tags')) {
    return '4-4'
  } else if (path.includes('/banner')) {
    return '5-1'
  } else if (path.includes('/product/list')) {
    return '6-1'
  } else if (path.includes('/product/category')) {
    return '6-2'
  } else if (path.includes('/service/activate')) {
    return '7-1'
  } else if (path.includes('/service/data')) {
    return '7-2'
  } else if (path.includes('/redpacket')) {
    return '8'
  } else if (path.includes('/points/goods')) {
    return '9-1'
  } else if (path.includes('/points/orders')) {
    return '9-2'
  } else if (path.includes('/points/rules')) {
    return '9-3'
  } else if (path.includes('/agreement')) {
    return '10'
  } else if (path.includes('/account/roles')) {
    return '11-1'
  } else if (path.includes('/account/list')) {
    return '11-2'
  } else if (path.includes('/settings')) {
    return '12'
  }
  return '1' // 默认选中"首页"
})

const navigateTo = (path) => {
  router.push(path)
}

const handleCommand = (command) => {
  if (command === 'logout') {
    ElMessageBox.confirm('确认退出登录吗?', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        userStore.logout()
        router.push('/login')
      })
      .catch(() => {})
  }
}
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
}

.el-container {
  height: 100%;
  width: 100%;
}

// 原来的 main-container 样式已在底部重新定义

.header {
  background-color: $bg-color-light;
  color: $text-color-dark;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  position: relative;
  height: $header-height;
  padding: 6px;
  border-bottom: 1px solid $border-color-light;

  &-left {
    display: flex;
    align-items: center;
    margin-left: 10px;
  }

  &-right {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-right: 20px;
  }
}

.logo-image {
  height: 40px;
  object-fit: contain;
}

.notification {
  &-container {
    display: flex;
    align-items: center;
    height: 100%;
  }

  &-badge {
    cursor: pointer;
    display: flex;
    align-items: center;
    height: 100%;
  }

  &-icon {
    font-size: 20px;
    color: $info-color;
  }
}

.user {
  &-dropdown {
    display: flex;
    align-items: center;
    height: 100%;
  }

  &-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #d3deff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
  }

  &-icon {
    font-size: 16px;
    color: $primary-color;
  }
}

.el-dropdown-link {
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: $text-color-dark;
  height: 100%;
}

// 侧边栏样式
.aside {
  background-color: $bg-color-light;
  border-right: 1px solid $border-color;
  overflow: hidden;
  padding: 0;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;

  :deep(.el-menu--popup) {
    max-width: 100%;
  }

  :deep(.el-menu--inline) {
    background-color: #f9fbfd !important;
    padding-bottom: 0;
  }

  // Element Plus 滚动条样式
  :deep(.el-scrollbar__bar) {
    z-index: 3;
  }

  :deep(.el-scrollbar__wrap) {
    overflow-x: hidden;
    margin-right: 0 !important;
    padding-right: 0 !important;
  }

  :deep(.el-scrollbar__view) {
    padding-right: 0px; // 移除右侧留白
    padding-bottom: 30px; // 确保滚动到底部
    width: 100%; // 确保宽度铺满
  }
}

.menu-title {
  padding: 16px;
  font-size: 14px;
  color: $text-color-light;
  font-weight: 500;
}

.el-menu-vertical {
  border-right: none;
  width: 100% !important;

  :deep(.el-menu-item) {
    width: 100%;
    margin-right: 0;
  }
}

.el-menu-vertical {
  .el-menu-item {
    height: $menu-item-height;
    line-height: $menu-item-height;
  }

  :deep(.el-menu-item.is-active) {
    background-color: $primary-light !important;
    color: $primary-hover !important;
    position: relative;
    margin-right: 0;
    padding-right: 20px !important; // 确保选中状态右侧无间隙
    width: 100%;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 3px;
      height: 100%;
      background-color: $primary-color;
    }
  }

  // 子菜单项样式
  :deep(.el-menu--inline .el-menu-item) {
    height: $menu-item-height;
    line-height: $menu-item-height;
    margin-left: 24px;
    width: calc(100% - 24px);
    position: relative; // 添加相对定位
    padding-left: 20px !important; // 确保有足够的左侧空间显示指示条

    &.is-active {
      background-color: $primary-light !important;
      color: $primary-hover !important;

      &::before {
        content: '';
        position: absolute;
        left: 0; // 调整为前面而不是左侧
        top: 0;
        width: 4px;
        height: 100%;
        background-color: $primary-color;
        z-index: 3;
      }
    }
  }
}

.el-menu-vertical {
  // active-menu-item 类在子菜单中的特殊样式
  :deep(.el-menu--inline .el-menu-item.active-menu-item) {
    background-color: $primary-light !important;
    color: $primary-hover !important;

    &::before {
      content: '';
      position: absolute;
      left: 0; // 调整为前面而不是左侧
      top: 0;
      width: 4px;
      height: 100%;
      background-color: $primary-color;
      z-index: 3;
    }
  }

  // 覆盖鼠标悬停样式
  :deep(.el-menu-item:hover) {
    background-color: #f5f7fa !important;
    padding-right: 20px !important; // 确保悬停状态右侧无间隙
    margin-right: 0;
    width: 100%;
  }

  :deep(.el-menu-item.is-active:hover) {
    background-color: $primary-light !important;
  }

  :deep(.el-sub-menu__title) {
    height: $menu-item-height;
    line-height: $menu-item-height;
  }
}

// 自定义选中项样式
.active-menu-item {
  background-color: $primary-light !important;
  color: $primary-hover !important;
  position: relative;
  margin-right: 0;
  padding-right: 20px !important; // 确保选中状态右侧无间隙
  width: 100%;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 3px;
    height: 100%;
    background-color: $primary-color;
  }
}

// 图标样式
.el-menu-item [class^='el-icon-'] + span,
.el-submenu [class^='el-icon-'] + span {
  margin-left: 5px;
}

.el-menu-item .el-icon,
.el-sub-menu .el-icon {
  margin-right: 10px;
  width: 24px;
  text-align: center;
  font-size: 18px;
  vertical-align: middle;
}

.main {
  padding: 20px;
  overflow-y: auto;
  height: calc(100vh - #{$header-height + $footer-height}); // 减去头部和底部的高度
  position: relative;
  background-color: $bg-color;
}

// 底部样式
.footer {
  background-color: $bg-color-light;
  border-top: 1px solid $border-color-light;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: $text-color-light;

  &-content {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 75px;
  }

  &-links {
    display: flex;
    gap: 15px;

    a {
      color: $text-color-light;
      text-decoration: none;

      &:hover {
        color: $primary-color;
      }
    }
  }
}

// 调整主容器高度以适应底部
.main-container {
  height: calc(100vh - #{$header-height + $footer-height}); // 减去头部和底部的高度
  overflow: hidden;
  background-color: $bg-color;
}
</style>
