---
description: 
globs: 
alwaysApply: true
---
# Vue 3 项目结构与规范

## 项目概述
这是一个基于 Vue 3 + Vite 构建的后台管理系统，使用 Element Plus 作为 UI 组件库，集成了 ECharts 图表库。项目遵循模块化设计，包含用户管理、数据统计等功能。

## 目录结构
- [src/](mdc:src) - 源代码目录
  - [src/api/](mdc:src/api) - API 接口定义，按模块划分
  - [src/assets/](mdc:src/assets) - 静态资源文件
  - [src/components/](mdc:src/components) - 可复用组件
  - [src/layout/](mdc:src/layout) - 布局组件
  - [src/router/](mdc:src/router) - 路由配置
  - [src/store/](mdc:src/store) - Vuex 状态管理
  - [src/utils/](mdc:src/utils) - 工具函数
  - [src/views/](mdc:src/views) - 页面视图组件
- [mock/](mdc:mock) - 模拟数据，用于开发阶段
- [public/](mdc:public) - 公共静态资源

## 关键文件
- [src/main.js](mdc:src/main.js) - 应用入口文件
- [src/App.vue](mdc:src/App.vue) - 根组件
- [src/router/index.js](mdc:src/router/index.js) - 路由配置
- [src/store/index.js](mdc:src/store/index.js) - 状态管理入口
- [src/utils/request.js](mdc:src/utils/request.js) - Axios 请求封装
- [vite.config.js](mdc:vite.config.js) - Vite 配置文件

## 开发规范

### 组件命名
- 页面组件使用 PascalCase 命名
- 文件夹使用 kebab-case 命名

### API 接口
- API 接口按模块划分，存放在 src/api 目录下
- 接口函数命名应具有描述性，如 `getUserList`、`updateUserInfo`

### 样式规范
- 组件样式使用 scoped 属性限制作用域
- 公共样式放在 src/assets/styles 目录下
- 使用 Element Plus 变量实现主题定制

### 首页开发规范
- 图表区域：无内边距，确保图表占满容器
- 表格样式：表头无下划线，行之间有分隔线
- 整体内容区域限制在 1280px 宽度内

### 所有页面主体内容
- 整体内容区域限制在 1280px 宽度内
- 对于分页形式来说布局参考home分页的布局形式即可

