import Mock from 'mockjs';
const Random = Mock.Random;

// 角色列表数据
const roleList = [
  {
    roleId: 'ROLE-001',
    roleName: '普通用户角色',
    description: '拥有基本查看权限',
    modifier: '张三',
    updateTime: '2025-06-20 14:30',
  },
  {
    roleId: 'ROLE-002',
    roleName: '管理员角色',
    description: '拥有所有管理权限',
    modifier: '李四',
    updateTime: '2025-06-21 09:15',
  },
  {
    roleId: 'ROLE-003',
    roleName: '访客角色',
    description: '仅拥有访客查看权限',
    modifier: '王五',
    updateTime: '2025-06-22 16:45',
  },
];

// 账号列表数据
const accountList = [
  {
    accountId: 'account001',
    username: 'account001',
    realName: '张三',
    roleId: 'ROLE-001',
    roleName: '普通用户角色',
    permissions: ['普通权限', '财务权限'],
    status: 1,
    createTime: '2025-06-18 10:30',
    lastLoginTime: '2025-06-25 14:22',
  },
  {
    accountId: 'account002',
    username: 'account002',
    realName: '李四',
    roleId: 'ROLE-002',
    roleName: '管理员角色',
    permissions: ['管理员权限'],
    status: 1,
    createTime: '2025-06-19 09:15',
    lastLoginTime: '2025-06-25 16:08',
  },
  {
    accountId: 'account003',
    username: 'account003',
    realName: '王五',
    roleId: 'ROLE-003',
    roleName: '访客角色',
    permissions: ['访客权限'],
    status: 0,
    createTime: '2025-06-20 16:45',
    lastLoginTime: '2025-06-24 11:30',
  },
  {
    accountId: 'account004',
    username: 'account004',
    realName: '赵六',
    roleId: 'ROLE-001',
    roleName: '普通用户角色',
    permissions: ['普通权限', '访客权限'],
    status: 1,
    createTime: '2025-06-21 14:20',
    lastLoginTime: '2025-06-25 10:15',
  },
];

export default [
  // 获取权限菜单列表
  {
    url: '/subaccount/permission/menu',
    method: 'get',
    response: () => {
      return {
        code: 200,
        data: [
          {
            id: 'home',
            label: '首页',
            children: [
              { id: 'home-view', label: '查看' },
              { id: 'home-add', label: '新增' },
              { id: 'home-edit', label: '修改' },
              { id: 'home-delete', label: '删除' },
            ],
          },
          {
            id: 'user',
            label: '用户管理',
            children: [
              { id: 'user-view', label: '查看' },
              { id: 'user-add', label: '新增' },
              { id: 'user-edit', label: '修改' },
              { id: 'user-delete', label: '删除' },
            ],
          },
          {
            id: 'activity',
            label: '活动管理',
            children: [
              { id: 'activity-view', label: '查看' },
              { id: 'activity-add', label: '新增' },
              { id: 'activity-edit', label: '修改' },
              { id: 'activity-delete', label: '删除' },
            ],
          },
          {
            id: 'product',
            label: '产品管理',
            children: [
              {
                id: 'product-list',
                label: '商品列表',
                children: [
                  { id: 'product-list-view', label: '查看' },
                  { id: 'product-list-add', label: '新增' },
                  { id: 'product-list-edit', label: '修改' },
                  { id: 'product-list-delete', label: '删除' },
                ],
              },
              {
                id: 'product-category',
                label: '分类管理',
                children: [
                  { id: 'product-category-view', label: '查看' },
                  { id: 'product-category-add', label: '新增' },
                  { id: 'product-category-edit', label: '修改' },
                  { id: 'product-category-delete', label: '删除' },
                ],
              },
            ],
          },
        ],
        message: '获取权限菜单成功',
      };
    },
  },

  // 获取角色列表
  {
    url: '/subaccount/role/list',
    method: 'get',
    response: (req) => {
      const { roleName, roleId, page = 1, size = 10 } = req.query;

      let filteredList = [...roleList];

      // 根据查询条件过滤
      if (roleName) {
        filteredList = filteredList.filter((item) =>
          item.roleName.toLowerCase().includes(roleName.toLowerCase()),
        );
      }

      if (roleId) {
        filteredList = filteredList.filter((item) =>
          item.roleId.toLowerCase().includes(roleId.toLowerCase()),
        );
      }

      // 分页处理
      const total = filteredList.length;
      const start = (page - 1) * size;
      const end = start + parseInt(size);
      const pageList = filteredList.slice(start, end);

      return {
        code: 200,
        data: {
          list: pageList,
          total,
        },
        message: '获取角色列表成功',
      };
    },
  },

  // 获取角色详情
  {
    url: /\/subaccount\/role\/detail\/[\w-]+/,
    method: 'get',
    response: (req) => {
      const roleId = req.url.match(/\/detail\/([\w-]+)/)[1];
      const role = roleList.find((item) => item.roleId === roleId);

      if (role) {
        return {
          code: 200,
          data: role,
          message: '获取角色详情成功',
        };
      } else {
        return {
          code: 404,
          data: null,
          message: '角色不存在',
        };
      }
    },
  },

  // 创建角色
  {
    url: '/subaccount/role/create',
    method: 'post',
    response: (req) => {
      const { roleName, description } = req.body;

      // 检查角色名是否已存在
      const exists = roleList.some((item) => item.roleName === roleName);
      if (exists) {
        return {
          code: 400,
          data: null,
          message: '角色名称已存在',
        };
      }

      // 创建新角色
      const newRole = {
        roleId: `ROLE-${Random.natural(100, 999)}`,
        roleName,
        description,
        modifier: '当前用户',
        updateTime: Random.datetime('yyyy-MM-dd HH:mm'),
      };

      roleList.push(newRole);

      return {
        code: 200,
        data: newRole,
        message: '创建角色成功',
      };
    },
  },

  // 更新角色
  {
    url: '/subaccount/role/update',
    method: 'post',
    response: (req) => {
      const { roleId, roleName, description } = req.body;
      const index = roleList.findIndex((item) => item.roleId === roleId);

      if (index === -1) {
        return {
          code: 404,
          data: null,
          message: '角色不存在',
        };
      }

      // 检查角色名是否已被其他角色使用
      const nameExists = roleList.some(
        (item) => item.roleId !== roleId && item.roleName === roleName,
      );

      if (nameExists) {
        return {
          code: 400,
          data: null,
          message: '角色名称已存在',
        };
      }

      // 更新角色信息
      roleList[index].roleName = roleName;
      roleList[index].description = description;
      roleList[index].modifier = '当前用户';
      roleList[index].updateTime = Random.datetime('yyyy-MM-dd HH:mm');

      return {
        code: 200,
        data: roleList[index],
        message: '更新角色成功',
      };
    },
  },

  // 删除角色
  {
    url: /\/subaccount\/role\/delete\/[\w-]+/,
    method: 'delete',
    response: (req) => {
      const roleId = req.url.match(/\/delete\/([\w-]+)/)[1];
      const index = roleList.findIndex((item) => item.roleId === roleId);

      if (index === -1) {
        return {
          code: 404,
          data: null,
          message: '角色不存在',
        };
      }

      // 检查是否有账号使用该角色
      const isUsed = accountList.some((account) => account.roleId === roleId);
      if (isUsed) {
        return {
          code: 400,
          data: null,
          message: '该角色已被账号使用，无法删除',
        };
      }

      // 删除角色
      roleList.splice(index, 1);

      return {
        code: 200,
        data: null,
        message: '删除角色成功',
      };
    },
  },

  // 获取账号列表
  {
    url: '/subaccount/account/list',
    method: 'get',
    response: (req) => {
      const {
        realName,
        username,
        permissions,
        page = 1,
        size = 10,
      } = req.query;

      let filteredList = [...accountList];

      // 根据查询条件过滤
      if (realName) {
        filteredList = filteredList.filter((item) =>
          item.realName.toLowerCase().includes(realName.toLowerCase()),
        );
      }

      if (username) {
        filteredList = filteredList.filter((item) =>
          item.username.toLowerCase().includes(username.toLowerCase()),
        );
      }

      if (permissions) {
        filteredList = filteredList.filter((item) =>
          item.permissions.some((perm) =>
            perm.toLowerCase().includes(permissions.toLowerCase()),
          ),
        );
      }

      // 分页处理
      const total = filteredList.length;
      const start = (page - 1) * size;
      const end = start + parseInt(size);
      const pageList = filteredList.slice(start, end);

      return {
        code: 200,
        data: {
          list: pageList,
          total,
        },
        message: '获取账号列表成功',
      };
    },
  },
];
