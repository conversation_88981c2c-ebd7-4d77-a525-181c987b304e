import { createRouter, createWebHistory } from 'vue-router'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({
  easing: 'ease', // 动画方式
  speed: 500, // 递增进度条的速度
  showSpinner: false, // 是否显示加载ico
  trickleSpeed: 200, // 自动递增间隔
  minimum: 0.3 // 初始化时的最小百分比
})

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue')
  },
  {
    path: '/',
    component: () => import('@/layout/index.vue'),
    meta: {
      requiresAuth: true
    },
    children: [
      // 首页
      {
        path: 'home',
        name: 'Home',
        component: () => import('@/views/home/<USER>')
      },

      // 用户管理
      {
        path: 'user',
        name: 'User',
        component: () => import('@/views/user/index.vue')
      },

      // 活动管理
      {
        path: 'activity',
        name: 'Activity',
        component: () => import('@/views/activity/index.vue'),
        redirect: '/activity/list',
        children: [
          // 活动列表模块
          {
            path: 'list',
            name: 'ActivityList',
            component: () => import('@/views/activity/list/index.vue')
            // children: [
            //   {
            //     path: 'add',
            //     name: 'ActivityListAdd',
            //     component: () => import('@/views/activity/list/add.vue')
            //   },
            //   {
            //     path: ':id/edit',
            //     name: 'ActivityListEdit',
            //     component: () => import('@/views/activity/list/edit.vue')
            //   },
            //   {
            //     path: ':id/view',
            //     name: 'ActivityListView',
            //     component: () => import('@/views/activity/list/view.vue')
            //   }
            // ]
          },
          // 活动分类模块
          {
            path: 'category',
            name: 'ActivityCategory',
            component: () => import('@/views/activity/category/index.vue')
          }
        ]
      },

      // 门店管理
      {
        path: 'store',
        name: 'Store',
        component: () => import('@/views/store/index.vue'),
        redirect: '/store/list',
        children: [
          {
            path: 'top',
            name: 'StoreTop',
            component: () => import('@/views/store/top/index.vue')
          },
          {
            path: 'list',
            name: 'StoreList',
            component: () => import('@/views/store/list/index.vue')
          },
          {
            path: 'audit',
            name: 'StoreAudit',
            component: () => import('@/views/store/audit/index.vue')
          },
          {
            path: 'tags',
            name: 'StoreTag',
            component: () => import('@/views/store/tag/index.vue')
          }
        ]
      },

      // 轮播图管理
      {
        path: 'banner',
        name: 'Banner',
        component: () => import('@/views/banner/index.vue'),
        redirect: '/banner/list',
        children: [
          {
            path: 'list',
            name: 'BannerList',
            component: () => import('@/views/banner/list/index.vue')
          }
        ]
      },

      // 产品管理
      {
        path: 'product',
        name: 'Product',
        component: () => import('@/views/product/index.vue'),
        redirect: '/product/list',
        children: [
          {
            path: 'list',
            name: 'ProductList',
            component: () => import('@/views/product/list/index.vue')
          },
          {
            path: 'category',
            name: 'ProductCategory',
            component: () => import('@/views/product/category/index.vue')
          }
        ]
      },

      // 售后激活
      {
        path: 'service',
        name: 'Service',
        component: () => import('@/views/service/index.vue'),
        redirect: '/service/activate',
        children: [
          {
            path: 'activate',
            name: 'ServiceActivate',
            component: () => import('@/views/service/activate-list/index.vue')
          },
          {
            path: 'activate-list/detail/:id',
            name: 'ServiceActivateDetail',
            component: () => import('@/views/service/activate-list/detail.vue')
          },
          {
            path: 'verificationData',
            name: 'ServiceVerificationData',
            component: () => import('@/views/service/verification-data/index.vue')
          },
          {
            path: 'verificationData-detail/:id',
            name: 'ServiceVerificationDataDetail',
            component: () => import('@/views/service/verification-data/detail.vue')
          }
        ]
      },

      // 红包领取列表
      {
        path: 'redpacket',
        name: 'Redpacket',
        component: () => import('@/views/redpacket/index.vue')
      },

      // 积分商城
      {
        path: 'points',
        name: 'PointsMall',
        component: () => import('@/views/points-mall/index.vue'),
        redirect: '/points/goods',
        children: [
          {
            path: 'goods',
            name: 'PointsProductList',
            component: () => import('@/views/points-mall/product-list/index.vue')
          },
          {
            path: 'orders',
            name: 'PointsOrderList',
            component: () => import('@/views/points-mall/order-list/index.vue')
          },
          {
            path: 'rules',
            name: 'PointsRules',
            component: () => import('@/views/points-mall/points-rules/index.vue')
          }
        ]
      },

      // 协议管理路由
      {
        path: 'agreement',
        component: () => import('@/views/agreement/index.vue'),
        redirect: '/agreement/list',
        children: [
          {
            path: 'list',
            name: 'AgreementList',
            component: () => import('@/views/agreement/list/index.vue')
          },
          {
            path: 'edit/:id',
            name: 'AgreementEdit',
            component: () => import('@/views/agreement/edit/index.vue')
          }
        ]
      },

      // 子账号管理路由
      {
        path: 'account',
        component: () => import('@/views/subaccount/index.vue'),
        redirect: '/subaccount/role',
        children: [
          {
            path: 'roles',
            name: 'SubaccountRole',
            component: () => import('@/views/subaccount/role/index.vue')
          },
          {
            path: 'role-add',
            name: 'SubaccountRoleAdd',
            component: () => import('@/views/subaccount/role/add.vue')
          },
          {
            path: 'list',
            name: 'SubaccountAccount',
            component: () => import('@/views/subaccount/account/index.vue')
          },
          {
            path: 'account-add',
            name: 'SubaccountAccountAdd',
            component: () => import('@/views/subaccount/account/add.vue')
          }
        ]
      },

      // 其他配置
      {
        path: 'settings',
        name: 'Settings',
        redirect: '/settings/miniprogram',
        children: [
          {
            path: 'miniprogram',
            name: 'SettingsMiniprogram',
            component: () => import('@/views/settings/miniprogram/index.vue')
          },
          {
            path: 'invite-link',
            name: 'SettingsInviteLink',
            component: () => import('@/views/settings/invite-link/index.vue')
          }
        ]
      }

      // 可以在这里添加更多父级路由
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/login'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 开启进度条
  NProgress.start()

  const token = localStorage.getItem('token')

  if (to.meta.requiresAuth && !token) {
    next('/login')
  } else {
    next()
  }
})

// 路由跳转完成后
router.afterEach(() => {
  // 关闭进度条
  NProgress.done()
})

export default router
