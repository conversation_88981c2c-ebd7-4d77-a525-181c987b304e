<template>
  <div class="role-edit-container">
    <!-- 页面标题和返回按钮 -->
    <div class="page-header">
      <div class="header-left">
        <div class="title-info">
          <h1 class="page-title">编辑角色权限</h1>
          <p class="page-description">修改角色的权限信息</p>
        </div>
      </div>
    </div>

    <!-- 编辑表单 -->
    <el-card class="edit-card" shadow="never" v-loading="loading">
      <el-form
        ref="roleFormRef"
        :model="roleForm"
        :rules="rules"
        label-position="top"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <h3 class="section-title">基本信息</h3>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="角色ID" prop="roleId">
                <el-input v-model="roleForm.roleId" disabled />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="角色名称" prop="roleName">
                <el-input
                  v-model="roleForm.roleName"
                  placeholder="请输入角色名称"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="权限描述" prop="description">
                <el-input
                  v-model="roleForm.description"
                  type="textarea"
                  rows="3"
                  placeholder="请输入权限描述"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 权限类型 -->
        <div class="form-section">
          <h3 class="section-title">权限类型</h3>
          <el-form-item prop="permissionTypes">
            <el-checkbox-group v-model="roleForm.permissionTypes">
              <el-checkbox label="管理员权限" border />
              <el-checkbox label="普通权限" border />
              <el-checkbox label="访客权限" border />
              <el-checkbox label="财务权限" border />
            </el-checkbox-group>
          </el-form-item>
        </div>

        <!-- 权限菜单 -->
        <div class="form-section">
          <h3 class="section-title">权限菜单</h3>
          <el-form-item prop="permissions">
            <el-tree
              ref="permissionTreeRef"
              :data="permissionMenus"
              :props="defaultProps"
              :default-expand-all="true"
              node-key="id"
              show-checkbox
              :default-checked-keys="roleForm.permissions"
              @check="handleCheckChange"
            />
          </el-form-item>
        </div>

        <!-- 提交按钮 -->
        <div class="form-actions">
          <CustomBtn type="save" @click="handleSubmit">保存</CustomBtn>
          <CustomBtn type="grey" @click="handleBack">取消</CustomBtn>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue';
import { getRoleDetail, updateRole, getPermissionMenu } from '@/api/subaccount';
import { ElMessage } from 'element-plus';
import CustomBtn from '@/components/custom-btn.vue';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();

// 获取路由参数
const roleId = route.params.id;

// 数据
const loading = ref(false);
const roleFormRef = ref(null);
const permissionTreeRef = ref(null);
const permissionMenus = ref([]);

// 表单数据
const roleForm = reactive({
  roleId: '',
  roleName: '',
  description: '',
  permissionTypes: [],
  permissions: [],
});

// 表单验证规则
const rules = {
  roleName: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' },
  ],
  description: [
    { required: true, message: '请输入权限描述', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' },
  ],
  permissionTypes: [
    {
      required: true,
      type: 'array',
      message: '请至少选择一种权限类型',
      trigger: 'change',
    },
  ],
};

// 树形控件配置
const defaultProps = {
  children: 'children',
  label: 'label',
};

// 获取角色详情
const fetchRoleDetail = async () => {
  loading.value = true;
  try {
    const res = await getRoleDetail(roleId);
    if (res.code === 200) {
      const { roleId, roleName, description } = res.data;

      // 设置基本信息
      roleForm.roleId = roleId;
      roleForm.roleName = roleName;
      roleForm.description = description;

      // 根据角色名称和描述设置权限类型
      const permissionTypes = [];
      if (roleName.includes('管理员')) {
        permissionTypes.push('管理员权限');
      }
      if (roleName.includes('普通')) {
        permissionTypes.push('普通权限');
      }
      if (roleName.includes('访客')) {
        permissionTypes.push('访客权限');
      }
      if (description.includes('财务')) {
        permissionTypes.push('财务权限');
      }

      roleForm.permissionTypes =
        permissionTypes.length > 0 ? permissionTypes : ['普通权限'];

      // 模拟选中的权限
      if (roleName.includes('管理员')) {
        roleForm.permissions = [
          'home-view',
          'home-add',
          'home-edit',
          'home-delete',
          'user-view',
          'user-add',
          'user-edit',
          'user-delete',
          'activity-view',
          'activity-add',
          'activity-edit',
          'activity-delete',
          'product-list-view',
          'product-list-add',
          'product-list-edit',
          'product-list-delete',
          'product-category-view',
          'product-category-add',
          'product-category-edit',
          'product-category-delete',
        ];
      } else if (roleName.includes('普通')) {
        roleForm.permissions = [
          'home-view',
          'user-view',
          'activity-view',
          'product-list-view',
          'product-category-view',
        ];

        if (description.includes('财务')) {
          roleForm.permissions.push('user-add', 'user-edit');
        }
      } else if (roleName.includes('访客')) {
        roleForm.permissions = ['home-view', 'product-list-view'];
      }
    } else {
      ElMessage.error(res.message || '获取角色详情失败');
    }
  } catch (error) {
    console.error('获取角色详情出错:', error);
    ElMessage.error('获取角色详情失败');
  } finally {
    loading.value = false;
  }
};

// 获取权限菜单
const fetchPermissionMenu = async () => {
  try {
    const res = await getPermissionMenu();
    if (res.code === 200) {
      permissionMenus.value = res.data;
    } else {
      ElMessage.error(res.message || '获取权限菜单失败');
    }
  } catch (error) {
    console.error('获取权限菜单出错:', error);
    ElMessage.error('获取权限菜单失败');
  }
};

// 处理权限选择变化
const handleCheckChange = () => {
  roleForm.permissions = permissionTreeRef.value.getCheckedKeys();
};

// 根据权限类型预设权限
const handlePermissionTypeChange = () => {
  // 清空已选权限
  permissionTreeRef.value.setCheckedKeys([]);

  // 根据权限类型设置预设权限
  if (roleForm.permissionTypes.includes('管理员权限')) {
    roleForm.permissions = [
      'home-view',
      'home-add',
      'home-edit',
      'home-delete',
      'user-view',
      'user-add',
      'user-edit',
      'user-delete',
      'activity-view',
      'activity-add',
      'activity-edit',
      'activity-delete',
      'product-list-view',
      'product-list-add',
      'product-list-edit',
      'product-list-delete',
      'product-category-view',
      'product-category-add',
      'product-category-edit',
      'product-category-delete',
    ];
  } else if (roleForm.permissionTypes.includes('普通权限')) {
    roleForm.permissions = [
      'home-view',
      'user-view',
      'activity-view',
      'product-list-view',
      'product-category-view',
    ];

    if (roleForm.permissionTypes.includes('财务权限')) {
      roleForm.permissions.push('user-add', 'user-edit');
    }
  } else if (roleForm.permissionTypes.includes('访客权限')) {
    roleForm.permissions = ['home-view', 'product-list-view'];
  }

  // 更新树形控件的选中状态
  nextTick(() => {
    permissionTreeRef.value.setCheckedKeys(roleForm.permissions);
  });
};

// 提交表单
const handleSubmit = async () => {
  if (!roleFormRef.value) return;

  await roleFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true;
      try {
        const res = await updateRole({
          ...roleForm,
          permissions: permissionTreeRef.value.getCheckedKeys(),
        });

        if (res.code === 200) {
          ElMessage.success('更新成功');
          router.push('/account/roles');
        } else {
          ElMessage.error(res.message || '更新失败');
        }
      } catch (error) {
        console.error('更新角色出错:', error);
        ElMessage.error('更新失败');
      } finally {
        loading.value = false;
      }
    }
  });
};

// 返回列表
const handleBack = () => {
  router.push('/account/roles');
};

// 监听权限类型变化
const watchPermissionTypes = () => {
  const observer = new MutationObserver(() => {
    if (permissionTreeRef.value) {
      handlePermissionTypeChange();
    }
  });

  nextTick(() => {
    const checkboxGroup = document.querySelector('.el-checkbox-group');
    if (checkboxGroup) {
      observer.observe(checkboxGroup, { childList: true, subtree: true });
    }
  });

  return observer;
};

// 页面加载时获取数据
onMounted(async () => {
  await fetchRoleDetail();
  await fetchPermissionMenu();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables.scss' as *;

.role-edit-container {
  padding: 20px;
  max-width: 1280px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 16px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;

    .title-info {
      .page-title {
        font-size: 20px;
        font-weight: 600;
        color: $text-color;
        margin: 0 0 6px 0;
      }

      .page-description {
        font-size: 13px;
        color: $text-color-light;
        margin: 0;
      }
    }
  }
}

.edit-card {
  border-radius: 10px;

  .form-section {
    margin-bottom: 32px;

    &:last-child {
      margin-bottom: 0;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: $text-color;
      margin: 0 0 16px 0;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;
    }
  }

  .form-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 32px;
  }
}

:deep(.el-card) {
  border-radius: 10px;
  overflow: hidden;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: $text-color;
}

:deep(.el-checkbox.is-bordered) {
  margin-right: 12px;
  margin-bottom: 12px;
  padding: 8px 16px;
  border-radius: 6px;
}

:deep(.el-tree-node__content) {
  height: 36px;
}

:deep(.el-tree-node__label) {
  font-size: 14px;
}
</style>
