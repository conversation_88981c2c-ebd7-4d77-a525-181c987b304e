<template>
  <div class="points-product-page">
    <!-- 页面标题 -->
    <div
      class="page-header"
      v-if="!showAddForm && !showViewForm && !showEditForm"
    >
      <h2>积分商品列表</h2>
      <p>管理系统内所有积分商品</p>
    </div>

    <!-- 新增商品标题 -->
    <div class="page-header" v-else-if="showAddForm">
      <h2>新增积分商品</h2>
      <p>在此页面新增积分商品信息</p>
    </div>

    <!-- 新增商品组件 -->

    <ProductAdd
      v-if="showAddForm"
      :initial-data="editData"
      @save="handleSaveProduct"
      @cancel="handleCancelAdd"
    />

    <!-- 查看商品详情 -->
    <ProductView
      v-else-if="showViewForm"
      :product-id="currentProductId"
      @back="handleBackToList"
    />

    <!-- 编辑商品 -->
    <ProductEdit
      v-else-if="showEditForm"
      :product-id="currentProductId"
      @save="handleSaveEdit"
      @back="handleBackToList"
    />

    <!-- 搜索区域 -->
    <el-card v-else class="search-card" shadow="never">
      <div class="search-form">
        <el-row :gutter="14">
          <el-col :span="8">
            <div class="search-item">
              <div class="search-label">商品名称</div>
              <el-input
                v-model="searchForm.productName"
                placeholder="请输入商品名称"
                class="search-input"
                clearable
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="search-item">
              <div class="search-label">商品ID</div>
              <el-input
                v-model="searchForm.productId"
                placeholder="请输入商品ID"
                class="search-input"
                clearable
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="search-item">
              <div class="search-label">是否上架</div>
              <el-select
                v-model="searchForm.isOnShelf"
                placeholder="全部"
                class="search-input"
                clearable
                @change="handleShelfSelectChange"
              >
                <el-option label="已上架" value="1" />
                <el-option label="未上架" value="0" />
              </el-select>
            </div>
          </el-col>
        </el-row>

        <!-- 操作按钮 -->
        <div class="search-actions">
          <CustomBtn type="add" @click="handleAdd" />
          <CustomBtn type="blue" @click="handleQuery" />
          <CustomBtn type="reset" @click="handleReset" />
        </div>
      </div>
    </el-card>

    <!-- 表格区域 -->
    <el-card
      v-if="!showAddForm && !showViewForm && !showEditForm"
      class="table-card"
      shadow="never"
    >
      <el-table
        :data="tableData"
        :loading="loading"
        style="width: 100%"
        row-key="id"
      >
        <el-table-column prop="productId" label="商品ID" width="100" />

        <el-table-column prop="productImage" label="商品主图" width="120">
          <template #default="{ row }">
            <el-image
              :src="row.productImage"
              style="width: 60px; height: 60px; border-radius: 6px"
              fit="cover"
              :preview-src-list="[row.productImage]"
              preview-teleported
            />
          </template>
        </el-table-column>

        <el-table-column prop="productName" label="商品名称" width="150" />

        <el-table-column prop="requiredPoints" label="所需积分" width="100">
          <template #default="{ row }">
            <span style="color: #fa8c16; font-weight: 500">
              {{ row.requiredPoints }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="stockCount" label="库存数量" width="100">
          <template #default="{ row }">
            <span
              :style="{ color: row.stockCount > 0 ? '#52C41A' : '#FF4D4F' }"
            >
              {{ row.stockCount }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="isOnShelf" label="是否上架" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.isOnShelf"
              :active-value="1"
              :inactive-value="0"
              @change="handleShelfChange(row)"
            />
          </template>
        </el-table-column>

        <el-table-column prop="lastModifier" label="最后修改人" width="100" />

        <el-table-column
          prop="lastModifyTime"
          label="最后修改时间"
          width="180"
        />

        <el-table-column
          label="操作"
          min-width="200"
          align="right"
          header-align="left"
        >
          <template #default="{ row }">
            <CustomBtn
              style="margin: 0 3px"
              type="check"
              @click="handleView(row)"
            />
            <CustomBtn
              style="margin: 0 3px"
              type="edit"
              @click="handleEdit(row)"
              >修改</CustomBtn
            >
            <CustomBtn
              style="margin: 0 3px"
              type="red"
              @click="handleDelete(row)"
              >删除</CustomBtn
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <!-- 分页 -->
    <div class="pagination-container">
      <SimplePagination
        v-model="pagination.currentPage"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        @change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  getPointsProductList,
  updateProductShelfStatus,
  deleteProduct,
} from '@/api/points-mall';
import CustomBtn from '@/components/custom-btn.vue';
import SimplePagination from '@/components/SimplePagination.vue';
import ProductView from './view.vue';
import ProductEdit from './edit.vue';
import ProductAdd from './ProductAdd.vue';

const router = useRouter();

// 显示不同的表单
const showAddForm = ref(false);
const showViewForm = ref(false);
const showEditForm = ref(false);
const editData = ref({});
const currentProductId = ref(null);

// 搜索表单
const searchForm = reactive({
  productName: '',
  productId: '',
  isOnShelf: '',
});

// 表格数据
const tableData = ref([]);
const loading = ref(false);

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

// 查询数据
const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchForm,
      page: pagination.currentPage,
      size: pagination.pageSize,
    };
    const response = await getPointsProductList(params);
    tableData.value = response.data.list;
    pagination.total = response.data.total;
  } catch (error) {
    console.error('获取积分商品列表失败:', error);
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

// 重置搜索
const handleReset = () => {
  Object.keys(searchForm).forEach((key) => {
    searchForm[key] = '';
  });
  pagination.currentPage = 1;
  fetchData();
};

// 查询
const handleQuery = () => {
  pagination.currentPage = 1;
  fetchData();
};

// 处理上架状态选择变化
const handleShelfSelectChange = () => {
  pagination.currentPage = 1;
  // fetchData();
};

// 新增商品
const handleAdd = () => {
  editData.value = {};
  showAddForm.value = true;
  showViewForm.value = false;
  showEditForm.value = false;
};

// 保存商品回调
const handleSaveProduct = (data) => {
  showAddForm.value = false;
  // 刷新列表
  fetchData();
};

// 取消新增回调
const handleCancelAdd = () => {
  showAddForm.value = false;
};

// 查看详情
const handleView = (row) => {
  currentProductId.value = row.id;
  showViewForm.value = true;
  showAddForm.value = false;
  showEditForm.value = false;
};

// 修改商品
const handleEdit = (row) => {
  currentProductId.value = row.id;
  showEditForm.value = true;
  showAddForm.value = false;
  showViewForm.value = false;
};

// 保存编辑回调
const handleSaveEdit = () => {
  showEditForm.value = false;
  fetchData();
};

// 返回列表
const handleBackToList = () => {
  showAddForm.value = false;
  showViewForm.value = false;
  showEditForm.value = false;
  currentProductId.value = null;
};

// 上下架切换
const handleShelfChange = async (row) => {
  try {
    await updateProductShelfStatus({
      id: row.id,
      isOnShelf: row.isOnShelf,
    });
    ElMessage.success(row.isOnShelf ? '商品已上架' : '商品已下架');
    //为了模拟效果 ，不必再查询一次
    // fetchData();
  } catch (error) {
    console.error('更新上架状态失败:', error);
    ElMessage.error('操作失败');
    // 恢复原状态
    row.isOnShelf = row.isOnShelf === 1 ? 0 : 1;
  }
};

// 删除商品
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认删除商品 "${row.productName}" 吗？`, '确认删除', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      await deleteProduct(row.id);
      ElMessage.success('删除成功');
      fetchData();
    } catch (error) {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  });
};

// 分页大小改变
// const handleSizeChange = (size) => {
//   pagination.pageSize = size;
//   pagination.currentPage = 1;
//   fetchData();
// };

// 当前页改变
const handleCurrentChange = (page) => {
  pagination.currentPage = page;
  fetchData();
};

// 初始化
onMounted(() => {
  fetchData();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables.scss' as *;

.points-product-page {
  padding: $spacing-xl;
  background-color: $bg-color;
  min-height: 100vh;
  max-width: 1280px;
  margin: 0 auto;

  .page-header {
    margin-bottom: $spacing-xl;

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: $text-color-dark;
      margin-bottom: $spacing-xs;
    }

    p {
      font-size: 14px;
      color: $text-color-light;
    }
  }

  .search-card {
    margin-bottom: $spacing-lg;
    border-radius: $border-radius;

    .search-form {
      padding: $spacing-xl;

      .search-item {
        margin-bottom: $search-spacing-vertical;

        .search-label {
          font-size: $search-label-size;
          color: $search-label-color;
          margin-bottom: $search-label-margin;
        }

        .search-input {
          width: 100%;
        }
      }

      .search-actions {
        display: flex;
      }
    }
  }

  .table-card {
    border-radius: $border-radius;
  }
}
.pagination-container {
  margin-top: 0px;
  display: flex;
  justify-content: center;
}
// Element Plus 样式覆盖
:deep(.el-table) {
  .el-table__row {
    &:hover {
      background-color: rgba(22, 93, 255, 0.05);
    }
  }
}

:deep(.el-input__wrapper) {
  border-radius: $border-radius;
  padding: 8px 12px;
  height: 40px;
}

:deep(.el-select) {
  .el-input__wrapper {
    border-radius: $border-radius;
    padding: 8px 12px;
    height: 40px;
  }
}

:deep(.el-button) {
  border-radius: $border-radius;
  padding: 8px 12px;
  height: 40px;
  font-size: 14px;
}

:deep(.el-card__body) {
  padding: 0;
}

:deep(.el-pagination) {
  .el-pager li {
    border-radius: 6px;
    margin: 0 4px;

    &.is-active {
      background-color: #409eff;
      color: white;
    }
  }

  .btn-prev,
  .btn-next {
    border-radius: 6px;
    margin: 0 4px;
  }
}
/* 调整表头样式 */
:deep(.el-table__header th) {
  background-color: #f9fafb;
  height: 50px;
  line-height: 50px;
}
</style>
