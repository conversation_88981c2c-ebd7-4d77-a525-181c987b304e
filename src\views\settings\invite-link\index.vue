<template>
  <div class="invite-link-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>生成商家邀请链接</h2>
      <p>生成并管理商家邀请链接</p>
    </div>

    <!-- 专属邀请链接 -->
    <el-card class="link-card" style="padding: 20px;" shadow="never">
      <div class="link-section">
        <h3>您的专属邀请链接</h3>
        <div class="link-input-container">
          <el-input
            v-model="exclusiveLink"
            readonly
            placeholder="https://example.com/invite/12345"
          />
          <el-button
            type="primary"
            style="
              background-color: #e9eeff;
              color: #165dff;
              border-color: #e9eeff;
              border-radius: 8px;
            "
            @click="copyExclusiveLink"
          >
            <el-icon><CopyDocument /></el-icon>
            复制链接
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 查询条件 -->
    <el-card class="search-card" style="padding: 20px;" shadow="never">
      <div class="search-section">
        <h3>查询条件</h3>
        <div class="search-form">
          <div class="search-row">
            <div class="search-field">
              <label>邀请人</label>
              <el-input
                v-model="searchForm.inviter"
                placeholder=""
                size="small"
                style="height: 32px"
              />
            </div>
            <div class="search-field">
              <label>被邀请人</label>
              <el-input
                v-model="searchForm.invitee"
                placeholder=""
                size="small"
                style="height: 32px"
              />
            </div>
            <div class="search-field">
              <label>时间范围</label>
              <div class="date-range">
                <el-date-picker
                  v-model="searchForm.startDate"
                  type="date"
                  placeholder="年/月/日"
                  format="YYYY/MM/DD"
                  value-format="YYYY-MM-DD"
                />
                <span class="date-separator">至</span>
                <el-date-picker
                  v-model="searchForm.endDate"
                  type="date"
                  placeholder="年/月/日"
                  format="YYYY/MM/DD"
                  value-format="YYYY-MM-DD"
                />
              </div>
            </div>
          </div>
          <div class="search-actions">
            <div style="margin-top: 12px">
              <CustomBtn type="reset" @click="handleReset">重置</CustomBtn>
              <CustomBtn type="blue" @click="handleSearch">查询</CustomBtn>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 统计卡片 -->
    <div class="stats-container">
      <el-card class="stats-card" shadow="never">
        <div class="stats-item">
          <div class="stats-content">
            <div class="stats-label">被邀请人数量</div>
            <div class="stats-value">{{ stats.inviteCount }}</div>
          </div>

          <div class="stats-icon blue">
            <el-icon><User /></el-icon>
          </div>
        </div>
      </el-card>

      <el-card class="stats-card" shadow="never">
        <div class="stats-item">
          <div class="stats-content">
            <div class="stats-label">获得积分数</div>
            <div class="stats-value">
              {{ stats.totalPoints.toLocaleString() }}
            </div>
          </div>
          <div class="stats-icon green">
            <el-icon><Trophy /></el-icon>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 数据表格 -->
    <el-card class="table-card"  shadow="never">
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="inviter" label="邀请人" width="250" />
        <el-table-column prop="invitee" label="被邀请人" width="250" />
        <el-table-column prop="inviteTime" label="邀请时间" width="250" />
        <el-table-column prop="status" label="邀请状态" width="250">
          <template #default="scope">
            <el-tag
              :class="getStatusClass(scope.row.status)"
              size="small"
              class="custom-tag"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="points" label="邀请获得积分数" />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <SimplePagination
          v-model="currentPage"
          :page-size="pageSize"
          :total="total"
          @change="handlePageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { CopyDocument, User, Trophy } from '@element-plus/icons-vue';
import {
  getInviteStats,
  getInviteList,
  getExclusiveLink,
} from '@/api/settings';
import CustomBtn from '@/components/custom-btn.vue';
import SimplePagination from '@/components/SimplePagination.vue';

const exclusiveLink = ref('');
const loading = ref(false);

// 搜索表单
const searchForm = reactive({
  inviter: '',
  invitee: '',
  startDate: '',
  endDate: '',
});

// 统计数据
const stats = reactive({
  inviteCount: 0,
  totalPoints: 0,
});

// 表格数据
const tableData = ref([]);

// 分页
const total = ref(0);
const pageSize = ref(3);
const currentPage = ref(1);

// 获取状态标签类型
const getStatusClass = (status) => {
  switch (status) {
    case '已接受':
      return 'status-success';
    case '待确认':
      return 'status-warning';
    case '已拒绝':
      return 'status-danger';
    default:
      return '';
  }
};

// 复制专属链接
const copyExclusiveLink = () => {
  navigator.clipboard
    .writeText(exclusiveLink.value)
    .then(() => {
      ElMessage.success('链接已复制到剪贴板');
    })
    .catch(() => {
      ElMessage.error('复制失败');
    });
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    inviter: '',
    invitee: '',
    startDate: '',
    endDate: '',
  });
  ElMessage.success('已重置查询条件');
};

// 查询
const handleSearch = () => {
  console.log('查询条件:', searchForm);
  currentPage.value = 1;
  loadTableData();
};

// 分页
const handlePageChange = (page) => {
  currentPage.value = page;
  loadTableData();
};

// 加载统计数据
const loadStats = async () => {
  try {
    const response = await getInviteStats();
    if (response.code === 200) {
      Object.assign(stats, response.data);
    }
  } catch (error) {
    console.error('加载统计数据失败:', error);
    ElMessage.error('加载统计数据失败');
  }
};

// 加载专属链接
const loadExclusiveLink = async () => {
  try {
    const response = await getExclusiveLink();
    if (response.code === 200) {
      exclusiveLink.value = response.data.link;
    }
  } catch (error) {
    console.error('加载专属链接失败:', error);
    ElMessage.error('加载专属链接失败');
  }
};

// 加载表格数据
const loadTableData = async () => {
  try {
    loading.value = true;
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      ...searchForm,
    };

    const response = await getInviteList(params);
    if (response.code === 200) {
      tableData.value = response.data.list;
      total.value = response.data.total;
    }
  } catch (error) {
    console.error('加载表格数据失败:', error);
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

// 加载所有数据
const loadData = async () => {
  await Promise.all([loadStats(), loadExclusiveLink(), loadTableData()]);
};

onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables.scss' as *;

.invite-link-page {
  padding: $spacing-xl;
  background-color: $bg-color;
  max-width: 1280px;
  margin: 0 auto;
  .page-header {
    margin-bottom: $spacing-xl;

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: $text-color-dark;
      margin-bottom: 0;
    }
    p {
      font-size: 14px;
      color: $text-color-light;
    }
  }

  .link-card,
  .search-card,
  .table-card {
    border-radius: 10px;
    margin-bottom: 20px;

    h3 {
      font-size: 16px;
      font-weight: 600;
      color: $text-color-dark;
      margin-bottom: 20px;
    }
  }

  .link-section {
    .link-input-container {
      display: flex;
      gap: 12px;
      align-items: center;

      :deep(.el-input) {
        flex: 1;
      }

      .el-button {
        padding: 12px 20px;
        font-size: 14px;
      }
    }
  }

  .search-section {
    .search-form {
      .search-row {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;

        .search-field {
          flex: 1;

          label {
            display: block;
            font-size: 14px;
            color: #86909c;
            margin-bottom: 8px;
          }

          .date-range {
            display: flex;
            align-items: center;
            gap: 8px;

            .date-separator {
              color: #86909c;
              font-size: 14px;
            }

            :deep(.el-date-editor) {
              flex: 1;
            }
          }
        }
      }

      .search-actions {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
      }
    }
  }

  .stats-container {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;

    .stats-card {
      flex: 1;
      border-radius: 10px;
      padding: 20px ;

      .stats-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 16px;

        .stats-content {
          flex: 1;
          text-align: left;

          .stats-label {
            font-size: 14px;
            color: #86909c;
            margin-bottom: 4px;
          }

          .stats-value {
            font-size: 28px;
            font-weight: 600;
            color: $text-color-dark;
          }
        }

        .stats-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          flex-shrink: 0;

          &.blue {
            background-color: #e9eeff;
            color: #165dff;
          }

          &.green {
            background-color: #e8f9e8;
            color: #00b42b;
          }
        }
      }
    }
  }

  .table-card {
    .pagination-container {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 20px;
      margin-top: 0px;
    }
  }
}

// Element Plus 样式覆盖
:deep(.el-card__body) {
  padding: 0px;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  padding: 12px 16px;
}

:deep(.el-input__inner) {
  font-size: 14px;
}

:deep(.el-date-editor .el-input__wrapper) {
  padding: 10px 12px;
}

:deep(.el-table) {
  border-radius: 8px;
}

/* 调整表头样式 */
:deep(.el-table__header th) {
  background-color: #f9fafb;
  height: 50px;
  line-height: 50px;
}

:deep(.el-pagination) {
  --el-pagination-bg-color: transparent;
}

// 自定义标签样式
.custom-tag {
  font-weight: bold;
  border-radius: 12px;
  padding: 0 10px;
}

.status-success {
  background-color: #e8f9e8;
  color: #00b42b;
  border-color: #e8f9e8;
}

.status-warning {
  background-color: #fff3e8;
  color: #ff7d00;
  border-color: #fff3e8;
}

.status-danger {
  background-color: #ffece8;
  color: #f53f3f;
  border-color: #ffece8;
}
</style>
