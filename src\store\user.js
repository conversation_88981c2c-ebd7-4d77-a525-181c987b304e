import { defineStore } from 'pinia'
import { login } from '@/api/user'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    username: localStorage.getItem('username') || ''
  }),

  actions: {
    async login(userInfo) {
      try {
        const { data } = await login(userInfo)
        this.token = data.token
        this.username = data.username

        localStorage.setItem('token', data.token)
        localStorage.setItem('username', data.username)

        return Promise.resolve(data)
      } catch (error) {
        return Promise.reject(error)
      }
    },

    logout() {
      this.token = ''
      this.username = ''
      localStorage.removeItem('token')
      localStorage.removeItem('username')
    }
  }
})
