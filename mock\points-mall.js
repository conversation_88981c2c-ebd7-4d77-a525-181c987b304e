// 积分商城相关Mock数据
export default [
  // 获取积分商品列表
  {
    url: '/points-mall/list',
    method: 'get',
    response: ({ query }) => {
      const {
        page = 1,
        size = 10,
        productName = '',
        productId = '',
        isOnShelf = '',
      } = query;

      // 模拟商品数据
      const allProducts = [
        {
          id: 1,
          productId: 'JG-001',
          productName: '时尚眼镜框',
          productImage: 'https://picsum.photos/200/200?random=1',
          requiredPoints: 500,
          stockCount: 100,
          isOnShelf: 1,
          lastModifier: '张三',
          lastModifyTime: '2025-06-20 14:30',
          description: '高品质时尚眼镜框，适合商务人士佩戴',
        },
        {
          id: 2,
          productId: 'JG-002',
          productName: '高级镜片',
          productImage: 'https://picsum.photos/200/200?random=2',
          requiredPoints: 800,
          stockCount: 80,
          isOnShelf: 1,
          lastModifier: '李四',
          lastModifyTime: '2025-06-21 09:15',
          description: '防蓝光高级镜片，保护您的眼睛',
        },
        {
          id: 3,
          productId: 'JG-003',
          productName: '眼镜清洁套装',
          productImage: 'https://picsum.photos/200/200?random=3',
          requiredPoints: 200,
          stockCount: 200,
          isOnShelf: 0,
          lastModifier: '王五',
          lastModifyTime: '2025-06-22 16:45',
          description: '专业眼镜清洁套装，包含清洁布和清洁液',
        },
        {
          id: 4,
          productId: 'JG-004',
          productName: '太阳镜',
          productImage: 'https://picsum.photos/200/200?random=4',
          requiredPoints: 1200,
          stockCount: 50,
          isOnShelf: 1,
          lastModifier: '赵六',
          lastModifyTime: '2025-06-23 11:20',
          description: '时尚太阳镜，UV400防护',
        },
        {
          id: 5,
          productId: 'JG-005',
          productName: '儿童眼镜',
          productImage: 'https://picsum.photos/200/200?random=5',
          requiredPoints: 600,
          stockCount: 75,
          isOnShelf: 1,
          lastModifier: '钱七',
          lastModifyTime: '2025-06-24 08:30',
          description: '专为儿童设计的安全眼镜',
        },
        {
          id: 6,
          productId: 'JG-006',
          productName: '运动眼镜',
          productImage: 'https://picsum.photos/200/200?random=6',
          requiredPoints: 900,
          stockCount: 60,
          isOnShelf: 0,
          lastModifier: '孙八',
          lastModifyTime: '2025-06-25 15:10',
          description: '专业运动眼镜，防滑防震',
        },
        {
          id: 7,
          productId: 'JG-007',
          productName: '老花镜',
          productImage: 'https://picsum.photos/200/200?random=7',
          requiredPoints: 400,
          stockCount: 120,
          isOnShelf: 1,
          lastModifier: '周九',
          lastModifyTime: '2025-06-26 10:25',
          description: '舒适老花镜，多种度数可选',
        },
        {
          id: 8,
          productId: 'JG-008',
          productName: '防蓝光眼镜',
          productImage: 'https://picsum.photos/200/200?random=8',
          requiredPoints: 700,
          stockCount: 90,
          isOnShelf: 1,
          lastModifier: '吴十',
          lastModifyTime: '2025-06-27 13:40',
          description: '有效阻挡蓝光，保护视力健康',
        },
        {
          id: 9,
          productId: 'JG-009',
          productName: '眼镜盒',
          productImage: 'https://picsum.photos/200/200?random=9',
          requiredPoints: 150,
          stockCount: 300,
          isOnShelf: 1,
          lastModifier: '郑十一',
          lastModifyTime: '2025-06-28 17:55',
          description: '高品质眼镜盒，保护您的眼镜',
        },
        {
          id: 10,
          productId: 'JG-010',
          productName: '眼镜链',
          productImage: 'https://picsum.photos/200/200?random=10',
          requiredPoints: 100,
          stockCount: 250,
          isOnShelf: 0,
          lastModifier: '冯十二',
          lastModifyTime: '2025-06-29 12:15',
          description: '时尚眼镜链，防止眼镜丢失',
        },
        {
          id: 11,
          productId: 'JG-011',
          productName: '近视眼镜',
          productImage: 'https://picsum.photos/200/200?random=11',
          requiredPoints: 800,
          stockCount: 85,
          isOnShelf: 1,
          lastModifier: '陈十三',
          lastModifyTime: '2025-06-30 09:30',
          description: '高品质近视眼镜，清晰视野',
        },
        {
          id: 12,
          productId: 'JG-012',
          productName: '变色镜片',
          productImage: 'https://picsum.photos/200/200?random=12',
          requiredPoints: 1000,
          stockCount: 40,
          isOnShelf: 1,
          lastModifier: '楚十四',
          lastModifyTime: '2025-07-01 14:20',
          description: '智能变色镜片，室内外自动调节',
        },
      ];

      // 过滤数据
      const filteredProducts = allProducts.filter((product) => {
        if (productName && !product.productName.includes(productName))
          return false;
        if (productId && !product.productId.includes(productId)) return false;
        if (isOnShelf !== '' && product.isOnShelf !== parseInt(isOnShelf))
          return false;
        return true;
      });

      // 分页
      const start = (page - 1) * size;
      const end = start + parseInt(size);
      const list = filteredProducts.slice(start, end);

      return {
        code: 200,
        data: {
          list,
          total: filteredProducts.length,
          page: parseInt(page),
          size: parseInt(size),
        },
        message: '获取成功',
      };
    },
  },

  // 获取积分商品详情
  {
    url: '/points-mall/detail/:id',
    method: 'get',
    response: ({ url }) => {
      const id = url.split('/').pop();
      return {
        code: 200,
        data: {
          id: parseInt(id),
          productId: `JG-${id.padStart(3, '0')}`,
          productName: '时尚眼镜框',
          productImage: 'https://picsum.photos/200/200?random=1',
          requiredPoints: 500,
          stockCount: 100,
          isOnShelf: 1,
          lastModifier: '张三',
          lastModifyTime: '2025-06-20 14:30',
          description: '高品质时尚眼镜框，适合商务人士佩戴',
          category: 'digital',
          productDetail:
            '<p>这是一款高品质时尚眼镜框，采用优质材料制作，舒适耐用。</p><p>特点：</p><ul><li>轻盈舒适</li><li>时尚设计</li><li>耐用材质</li></ul><p>适合商务人士日常佩戴，展现专业形象。</p>',
          productGallery: [
            'https://picsum.photos/200/200?random=101',
            'https://picsum.photos/200/200?random=102',
            'https://picsum.photos/200/200?random=103',
          ],
          specifications: [
            {
              specName: '黑色款',
              requiredPoints: 500,
              stockCount: 50,
              image: 'https://picsum.photos/200/200?random=201',
            },
            {
              specName: '银色款',
              requiredPoints: 550,
              stockCount: 30,
              image: 'https://picsum.photos/200/200?random=202',
            },
            {
              specName: '金色款',
              requiredPoints: 600,
              stockCount: 20,
              image: 'https://picsum.photos/200/200?random=203',
            },
          ],
        },
        message: '获取成功',
      };
    },
  },

  // 更新商品上架状态
  {
    url: '/points-mall/shelf-status',
    method: 'put',
    response: () => {
      return {
        code: 200,
        data: null,
        message: '更新成功',
      };
    },
  },

  // 删除商品
  {
    url: '/points-mall/delete/:id',
    method: 'delete',
    response: () => {
      return {
        code: 200,
        data: null,
        message: '删除成功',
      };
    },
  },

  // 新增商品
  {
    url: '/points-mall/add',
    method: 'post',
    response: () => {
      return {
        code: 200,
        data: {
          id: 13,
        },
        message: '新增成功',
      };
    },
  },

  // 更新商品
  {
    url: '/points-mall/update',
    method: 'put',
    response: () => {
      return {
        code: 200,
        data: null,
        message: '更新成功',
      };
    },
  },

  // 获取积分订单列表
  {
    url: '/points-mall/order/list',
    method: 'get',
    response: ({ query }) => {
      const {
        page = 1,
        size = 10,
        productName = '',
        productId = '',
        exchangeUserName = '',
        startTime = '',
        endTime = '',
        startPoints = '',
        endPoints = '',
        orderStatus = '',
      } = query;

      // 模拟订单数据
      const allOrders = [
        {
          id: 1,
          productId: 'JG-001',
          productName: '时尚眼镜框',
          points: 500,
          exchangeTime: '2025-06-20 14:30',
          exchangeUser: '张三',
          orderStatus: '待发货', // 待发货、已发货、已完成、已取消
          statusCode: 1,
        },
        {
          id: 2,
          productId: 'JG-002',
          productName: '高级镜片',
          points: 800,
          exchangeTime: '2025-06-21 09:15',
          exchangeUser: '李四',
          orderStatus: '已发货',
          statusCode: 2,
        },
        {
          id: 3,
          productId: 'JG-003',
          productName: '眼镜清洁套装',
          points: 200,
          exchangeTime: '2025-06-22 16:45',
          exchangeUser: '王五',
          orderStatus: '已完成',
          statusCode: 3,
        },
        {
          id: 4,
          productId: 'JG-004',
          productName: '防蓝光镜片',
          points: 600,
          exchangeTime: '2025-06-23 11:20',
          exchangeUser: '赵六',
          orderStatus: '已取消',
          statusCode: 4,
        },
      ];

      // 过滤数据
      const filteredOrders = allOrders.filter((order) => {
        if (productName && !order.productName.includes(productName))
          return false;
        if (productId && !order.productId.includes(productId)) return false;
        if (exchangeUserName && !order.exchangeUser.includes(exchangeUserName))
          return false;
        if (startTime && new Date(order.exchangeTime) < new Date(startTime))
          return false;
        if (endTime && new Date(order.exchangeTime) > new Date(endTime))
          return false;
        if (startPoints && order.points < parseInt(startPoints)) return false;
        if (endPoints && order.points > parseInt(endPoints)) return false;
        if (orderStatus !== '' && order.statusCode !== parseInt(orderStatus))
          return false;
        return true;
      });

      // 分页
      const start = (page - 1) * size;
      const end = start + parseInt(size);
      const list = filteredOrders.slice(start, end);

      return {
        code: 200,
        data: {
          list,
          total: filteredOrders.length,
          page: parseInt(page),
          size: parseInt(size),
        },
        message: '获取成功',
      };
    },
  },

  // 获取积分订单详情
  {
    url: '/points-mall/order/detail/:id',
    method: 'get',
    response: ({ url }) => {
      const id = url.split('/').pop();

      // 详情数据映射
      const detailsMap = {
        1: {
          productId: 'JG-001',
          productName: '时尚眼镜框',
          points: 500,
          exchangeTime: '2025-06-20 14:30',
          exchangeUser: '张三',
          orderStatus: '待发货',
          statusCode: 1,
          address: '北京市朝阳区建国路88号',
          phone: '13812345678',
          remark: '请尽快发货，谢谢',
        },
        2: {
          productId: 'JG-002',
          productName: '高级镜片',
          points: 800,
          exchangeTime: '2025-06-21 09:15',
          exchangeUser: '李四',
          orderStatus: '已发货',
          statusCode: 2,
          address: '上海市浦东新区陆家嘴金融中心',
          phone: '13987654321',
          remark: '工作时间配送',
        },
        3: {
          productId: 'JG-003',
          productName: '眼镜清洁套装',
          points: 200,
          exchangeTime: '2025-06-22 16:45',
          exchangeUser: '王五',
          orderStatus: '已完成',
          statusCode: 3,
          address: '广州市天河区珠江新城CBD',
          phone: '13756789012',
          remark: '无',
        },
        4: {
          productId: 'JG-004',
          productName: '防蓝光镜片',
          points: 600,
          exchangeTime: '2025-06-23 11:20',
          exchangeUser: '赵六',
          orderStatus: '已取消',
          statusCode: 4,
          address: '深圳市南山区科技园',
          phone: '13645678901',
          remark: '临时取消订单',
        },
      };

      const detail = detailsMap[id] || detailsMap[1];

      return {
        code: 200,
        data: {
          id: parseInt(id),
          ...detail,
        },
        message: '获取成功',
      };
    },
  },

  // 获取积分规则
  {
    url: '/points-mall/rules',
    method: 'get',
    response: () => {
      return {
        code: 200,
        data: {
          registerPoints: null,
          invitePoints: null,
          verifyPoints: null,
          rulesDescription: '',
        },
        message: '获取成功',
      };
    },
  },

  // 更新积分规则
  {
    url: '/points-mall/rules/update',
    method: 'put',
    response: () => {
      return {
        code: 200,
        data: null,
        message: '更新成功',
      };
    },
  },
];
