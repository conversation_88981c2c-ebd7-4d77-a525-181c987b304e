<template>
  <div class="store-list-page">
    <transition name="fade" mode="out-in">
      <div v-if="!showAddForm" key="list">
        <div class="page-header">
          <h2>门店列表</h2>
          <p>管理系统内所有门店</p>
        </div>

        <!-- 筛选区域 -->
        <el-card class="filter-card" shadow="never">
          <div class="filter-row">
            <div class="filter-item">
              <div class="filter-label">选择地区</div>
              <el-select v-model="searchForm.province" placeholder="请选择省/直辖市" clearable class="filter-select">
                <el-option v-for="item in provinceOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>

            <div class="filter-item">
              <div class="filter-label">选择城市</div>
              <el-select v-model="searchForm.city" placeholder="请选择市/区" clearable class="filter-select">
                <el-option v-for="item in cityOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>

            <div class="filter-item">
              <div class="filter-label">选择区县</div>
              <el-select v-model="searchForm.district" placeholder="请选择区/县" clearable class="filter-select">
                <el-option v-for="item in districtOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>

            <div class="filter-item">
              <div class="filter-label">门店类型</div>
              <el-select v-model="searchForm.type" placeholder="全部类型" clearable class="filter-select">
                <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>
          </div>

          <div class="filter-row">
            <div class="filter-item">
              <div class="filter-label">门店等级</div>
              <el-select v-model="searchForm.level" placeholder="全部等级" clearable class="filter-select">
                <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>

            <div class="filter-item">
              <div class="filter-label">是否上架</div>
              <el-select v-model="searchForm.isOnline" placeholder="全部" clearable class="filter-select">
                <el-option v-for="item in onlineOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>

            <div class="filter-item">
              <div class="filter-label">门店名称</div>
              <el-input v-model="searchForm.name" placeholder="请输入门店名称" clearable prefix-icon="Search"></el-input>
            </div>

            <div class="filter-actions">
              <el-button @click="handleReset" plain class="reset-button" :disabled="isResetDisabled">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
              <el-button type="primary" @click="handleSearch" class="search-button">
                <el-icon><Search /></el-icon>
                查询
              </el-button>
              <el-button type="success" @click="handleAdd" class="add-button">
                <el-icon><Plus /></el-icon>
                新增门店
              </el-button>
            </div>
          </div>
        </el-card>

        <!-- 门店列表 -->
        <el-card class="store-card" shadow="never">
          <el-table :data="storeList" style="width: 100%" v-loading="loading" :header-cell-style="tableHeaderStyle" :cell-style="tableCellStyle">
            <el-table-column prop="id" label="门店ID" width="100">
              <template #default="scope">
                <span class="text-secondary">{{ scope.row.id }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="门店名称" min-width="180">
              <template #default="scope">
                <span class="store-name">{{ scope.row.name }}</span>
              </template>
            </el-table-column>

            <el-table-column prop="phone" label="联系电话" width="150">
              <template #default="scope">
                <span class="text-secondary">{{ scope.row.phone }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="address" label="门店地址" min-width="220">
              <template #default="scope">
                <span class="text-secondary">{{ scope.row.address }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="门店类型" width="100">
              <template #default="scope">
                <div class="type-tag-wrapper">
                  <span class="type-tag">{{ scope.row.type }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="level" label="门店星级" width="120">
              <template #default="scope">
                <div class="level-tag-wrapper">
                  <span class="level-tag">
                    <el-icon><Star /></el-icon>
                    <span>{{ scope.row.level }}</span>
                  </span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="isOnline" label="是否上架" width="100">
              <template #default="scope">
                <el-switch v-model="scope.row.isOnline" :active-value="1" :inactive-value="0" @change="handleStatusChange(scope.row)"></el-switch>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="260">
              <template #default="scope">
                <div class="action-buttons">
                  <el-button size="small" @click="handleView(scope.row)" class="table-action-btn view-btn">
                    <el-icon><View /></el-icon>
                    查看
                  </el-button>
                  <el-button size="small" @click="handleEdit(scope.row)" class="table-action-btn edit-btn">
                    <el-icon><Edit /></el-icon>
                    修改
                  </el-button>
                  <el-button size="small" @click="handleDelete(scope.row)" class="table-action-btn delete-btn">
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 分页栏 - 移出卡片，采用简洁的数字分页形式 -->
        <SimplePagination v-model="pagination.page" :page-size="pagination.pageSize" :total="pagination.total" @change="handleCurrentChange" />
      </div>

      <StoreForm v-else key="form" :is-edit="isEdit" :edit-id="editingStore?.id" @cancel="handleCancelAdd" @submit="handleStoreFormSubmit" @save-draft="handleSaveDraft" />
    </transition>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, View, Edit, Delete, Plus, Refresh, Star } from '@element-plus/icons-vue'
import { getStoreList, getProvinces, getCities, getDistricts, addStore, updateStore, deleteStore, updateStoreForm, submitStoreForm, saveStoreDraft } from '@/api/store'
import StoreForm from '@/views/store/components/StoreForm.vue'
import SimplePagination from '@/components/SimplePagination.vue'

// 搜索表单
const searchForm = reactive({
  province: '',
  city: '',
  district: '',
  name: '',
  type: '',
  level: '',
  isOnline: ''
})

// 省份选项
const provinceOptions = ref([])

// 城市选项
const cityOptions = ref([])

// 区县选项
const districtOptions = ref([])

// 门店类型选项
const typeOptions = ref([
  { value: '旗舰店', label: '旗舰店' },
  { value: '标准店', label: '标准店' }
])

// 门店等级选项
const levelOptions = ref([
  { value: '金牌门店', label: '金牌门店' },
  { value: '银牌门店', label: '银牌门店' }
])

// 上架状态选项
const onlineOptions = ref([
  { value: 1, label: '已上架' },
  { value: 0, label: '未上架' }
])

// 门店列表
const storeList = ref([])

// 加载状态
const loading = ref(false)

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 5,
  total: 0
})

// 是否重置按钮禁用状态
const isResetDisabled = ref(false)

// 是否显示新增/编辑表单
const showAddForm = ref(false)

// 是否编辑模式
const isEdit = ref(false)

// 当前编辑的门店数据
const editingStore = ref(null)

// 表格表头样式
const tableHeaderStyle = {
  backgroundColor: '#FAFBFC',
  color: '#89929E',
  fontSize: '14px',
  fontWeight: '600',
  height: '50px'
}

// 表格单元格样式
const tableCellStyle = {
  padding: '12px 0',
  fontSize: '14px'
}

// 获取省份数据
const fetchProvinces = async () => {
  try {
    const res = await getProvinces()
    provinceOptions.value = res.data
  } catch (error) {
    console.error('获取省份数据失败:', error)
  }
}

// 获取城市数据
const fetchCities = async (provinceCode) => {
  try {
    const res = await getCities(provinceCode)
    cityOptions.value = res.data
  } catch (error) {
    console.error('获取城市数据失败:', error)
  }
}

// 获取区县数据
const fetchDistricts = async (cityCode) => {
  try {
    const res = await getDistricts(cityCode)
    districtOptions.value = res.data
  } catch (error) {
    console.error('获取区县数据失败:', error)
  }
}

// 省份变化时获取城市
watch(
  () => searchForm.province,
  (newVal) => {
    if (newVal) {
      fetchCities(newVal)
      searchForm.city = ''
      searchForm.district = ''
      districtOptions.value = []
    } else {
      cityOptions.value = []
      districtOptions.value = []
      searchForm.city = ''
      searchForm.district = ''
    }
  }
)

// 城市变化时获取区县
watch(
  () => searchForm.city,
  (newVal) => {
    if (newVal) {
      fetchDistricts(newVal)
      searchForm.district = ''
    } else {
      districtOptions.value = []
      searchForm.district = ''
    }
  }
)

// 获取门店列表
const fetchStoreList = async () => {
  loading.value = true
  try {
    const res = await getStoreList({
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchForm
    })
    storeList.value = res.data.list
    pagination.total = res.data.total
  } catch (error) {
    console.error('获取门店列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置按钮点击处理
const handleReset = () => {
  isResetDisabled.value = true
  Object.keys(searchForm).forEach((key) => {
    searchForm[key] = ''
  })
  pagination.page = 1
  fetchStoreList()

  // 移除表单项的焦点
  document.activeElement.blur()

  // 重置后延迟恢复按钮状态
  setTimeout(() => {
    isResetDisabled.value = false
  }, 300)
}

// 查询按钮点击处理
const handleSearch = () => {
  pagination.page = 1
  fetchStoreList()
}

// 新增门店按钮点击处理
const handleAdd = () => {
  showAddForm.value = true
  isEdit.value = false
  editingStore.value = null
}

// 取消新增/编辑操作
const handleCancelAdd = () => {
  showAddForm.value = false
  isEdit.value = false
  editingStore.value = null
}

// 编辑门店按钮点击处理
const handleEdit = (row) => {
  isEdit.value = true
  editingStore.value = { ...row }
  showAddForm.value = true
}

// 处理表单提交
const handleStoreFormSubmit = async (formData) => {
  loading.value = true
  try {
    if (isEdit.value) {
      // 编辑模式
      await updateStoreForm(editingStore.value.id, formData)
      ElMessage.success('门店信息更新成功')
    } else {
      // 新增模式
      await submitStoreForm(formData)
      ElMessage.success('门店新增成功')
    }

    // 返回列表并刷新数据
    showAddForm.value = false
    isEdit.value = false
    editingStore.value = null
    fetchStoreList()
  } catch (error) {
    console.error('保存门店信息失败:', error)
    ElMessage.error('保存门店信息失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 处理保存草稿
const handleSaveDraft = async (formData) => {
  loading.value = true
  try {
    formData.status = 0 // 草稿状态
    if (isEdit.value) {
      // 编辑模式
      await updateStoreForm(editingStore.value.id, formData)
      ElMessage.success('草稿保存成功')
    } else {
      // 新增模式
      await saveStoreDraft(formData)
      ElMessage.success('草稿保存成功')
    }

    // 返回列表并刷新数据
    showAddForm.value = false
    isEdit.value = false
    editingStore.value = null
    fetchStoreList()
  } catch (error) {
    console.error('保存草稿失败:', error)
    ElMessage.error('保存草稿失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 查看门店详情
const handleView = (row) => {
  ElMessage.info(`查看门店：${row.name}`)
  // 可以在这里添加查看门店详情的逻辑
}

// 删除门店处理
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除门店 "${row.name}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      loading.value = true
      try {
        await deleteStore(row.id)
        ElMessage.success('门店删除成功')
        fetchStoreList()
      } catch (error) {
        console.error('删除门店失败:', error)
        ElMessage.error('删除门店失败，请稍后重试')
      } finally {
        loading.value = false
      }
    })
    .catch(() => {
      // 用户取消删除操作
    })
}

// 切换上下架状态
const handleStatusChange = async (row) => {
  try {
    await updateStore(row.id, { isOnline: row.isOnline })
    ElMessage.success(`门店已${row.isOnline === 1 ? '上架' : '下架'}`)
  } catch (error) {
    console.error('更新上架状态失败:', error)
    ElMessage.error('更新状态失败，请稍后重试')
    row.isOnline = row.isOnline === 1 ? 0 : 1 // 恢复原状态
  }
}

// 分页大小改变处理
const handleSizeChange = (newSize) => {
  pagination.pageSize = newSize
  fetchStoreList()
}

// 分页页码改变处理
const handleCurrentChange = (newPage) => {
  pagination.page = newPage
  fetchStoreList()
}

// 初始化
onMounted(async () => {
  await fetchProvinces()
  await fetchStoreList()
})

// 组件卸载前清除可能的副作用
onBeforeUnmount(() => {
  // 如果有需要清理的内容可以添加在这里
})
</script>

<style lang="scss" scoped>
@use 'sass:color';

.store-list-page {
  max-width: 1280px;
  margin: 0 auto;
  min-height: 500px;
  padding-bottom: 20px;
}

.page-header {
  margin-bottom: 24px;

  h2 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    padding: 0;
  }

  p {
    font-size: 14px;
    color: #86909c;
    margin: 5px 0 0;
    padding: 0;
  }
}

.filter-card {
  margin-bottom: 24px;
  border-radius: 8px;

  :deep(.el-card__body) {
    padding: 20px;
    overflow: hidden;
  }
}

.store-card {
  margin-bottom: 24px;
  border-radius: 8px;

  :deep(.el-card__body) {
    padding: 0;
    overflow: hidden;
  }

  :deep(.el-table) {
    border-radius: 8px;
    overflow: hidden;
  }

  :deep(.el-table th) {
    background-color: #fafbfc;
    color: #89929e;
    font-weight: 600;
    height: 50px;
  }

  :deep(.el-table td) {
    padding: 12px 0;
  }

  :deep(.el-table__row) {
    height: 62px;
  }
}

.filter-row {
  display: flex;
  align-items: flex-end;
  width: 100%;
  gap: 14px;
  flex-wrap: wrap;
  margin-bottom: 14px;

  &:last-child {
    margin-bottom: 0;
  }
}

.filter-item {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 200px;
}

.filter-label {
  font-size: 14px;
  color: #86909c;
  margin-bottom: 4px;
}

.filter-select,
.el-input {
  width: 100%;

  :deep(.el-input__inner) {
    height: 42px;
    border-radius: 6px;
  }
}

// 搜索栏按钮样式已移至全局样式，此处无需重复定义

.table-area-tags {
  padding: 5px 0;
}

.area-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-width: 100%;

  .area-tag {
    margin-right: 0;
    background-color: #e9eeff !important;
    border-color: #e9eeff !important;
    color: #165dff !important;
    font-size: 12px;
    padding: 0 8px;
    height: 24px;
    line-height: 24px;
    border-radius: 4px;
    white-space: normal;
    max-width: 100%;
    margin-bottom: 4px;
    display: inline-block;
  }
}

.type-tag-wrapper,
.level-tag-wrapper {
  display: flex;
}

.type-tag {
  background-color: #e9eeff;
  color: #165dff;
  font-size: 12px;
  padding: 0 8px;
  height: 24px;
  line-height: 24px;
  border-radius: 15px;
  display: inline-block;
}

.level-tag {
  background-color: #e9eeff;
  color: #165dff;
  font-size: 12px;
  padding: 0 8px;
  height: 24px;
  line-height: 24px;
  border-radius: 15px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.action-buttons {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  white-space: nowrap;
}

.table-action-btn {
  height: 32px;
  padding: 0 12px;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  margin: 0 4px;
  border-radius: 4px;
  border: none;

  &.view-btn {
    background-color: #e9eeff;
    color: #165dff;
    &:hover {
      background-color: #d0dbff;
    }
  }

  &.edit-btn {
    background-color: #fff2e9;
    color: #ff7d00;
    &:hover {
      background-color: #ffe9d9;
    }
  }

  &.delete-btn {
    background-color: #ffecec;
    color: #f53f3f;
    &:hover {
      background-color: #ffd8d8;
    }
  }

  .el-icon {
    margin-right: 4px;
  }
}

.pagination-container {
  padding: 20px;
  display: flex;
  justify-content: flex-end;
}

.text-secondary {
  color: #86909c;
}

.store-name {
  font-weight: 500 !important;
  color: #000000 !important;
  font-size: 12px !important;
}

// 添加淡入淡出动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 分页样式已封装到SimplePagination组件中
</style>
