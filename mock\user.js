// 用户相关的mock接口
export default [
  // 用户登录接口
  {
    url: '/user/login',
    method: 'post',
    response: ({ body }) => {
      const { username, password } = body

      // 验证用户名和密码
      if (username === 'admin' && password === '123456') {
        return {
          code: 200,
          message: '登录成功',
          data: {
            token: 'mock-token-' + Date.now(),
            username: 'admin',
            roles: ['admin']
          }
        }
      } else {
        return {
          code: 401,
          message: '用户名或密码错误',
          data: null
        }
      }
    }
  },

  // 获取用户信息接口
  {
    url: '/user/info',
    method: 'get',
    response: ({ headers }) => {
      // 这里可以通过headers中的token进行验证
      // 简化版本直接返回用户信息
      return {
        code: 200,
        message: '获取用户信息成功',
        data: {
          username: 'admin',
          realName: '管理员',
          avatar: '',
          roles: ['admin'],
          permissions: ['*']
        }
      }
    }
  },

  // 获取用户列表接口
  {
    url: '/user/list',
    method: 'get',
    response: (req) => {
      // 模拟的用户数据
      const allUsers = [
        {
          id: 1,
          phone: '13800138000',
          name: '张三',
          wechatName: '阳光张三',
          points: 2560,
          lastModifiedTime: '2023-06-23 10:24'
        },
        {
          id: 2,
          phone: '13900139000',
          name: '李四',
          wechatName: '李四李四',
          points: 1840,
          lastModifiedTime: '2023-06-22 15:30'
        },
        {
          id: 3,
          phone: '13700137000',
          name: '王五',
          wechatName: '王五来了',
          points: 3200,
          lastModifiedTime: '2023-06-21 09:15'
        },
        {
          id: 4,
          phone: '13600136000',
          name: '赵六',
          wechatName: '赵六666',
          points: 4580,
          lastModifiedTime: '2023-06-20 18:45'
        },
        {
          id: 5,
          phone: '13500135000',
          name: '钱七',
          wechatName: '钱七七',
          points: 1200,
          lastModifiedTime: '2023-06-19 14:20'
        },
        {
          id: 6,
          phone: '13400134000',
          name: '孙八',
          wechatName: '孙八爷',
          points: 3600,
          lastModifiedTime: '2023-06-18 11:35'
        },
        {
          id: 7,
          phone: '13300133000',
          name: '周九',
          wechatName: '周九九',
          points: 2800,
          lastModifiedTime: '2023-06-17 16:50'
        },
        {
          id: 8,
          phone: '13200132000',
          name: '吴十',
          wechatName: '吴十十',
          points: 1950,
          lastModifiedTime: '2023-06-16 09:30'
        },
        {
          id: 9,
          phone: '13100131000',
          name: '郑十一',
          wechatName: '郑十一',
          points: 4200,
          lastModifiedTime: '2023-06-15 14:15'
        },
        {
          id: 10,
          phone: '13000130000',
          name: '王十二',
          wechatName: '王十二',
          points: 3100,
          lastModifiedTime: '2023-06-14 10:40'
        },
        {
          id: 11,
          phone: '12900129000',
          name: '李十三',
          wechatName: '李十三',
          points: 2750,
          lastModifiedTime: '2023-06-13 17:25'
        },
        {
          id: 12,
          phone: '12800128000',
          name: '赵十四',
          wechatName: '赵十四',
          points: 1680,
          lastModifiedTime: '2023-06-12 11:10'
        },
        {
          id: 13,
          phone: '12700127000',
          name: '钱十五',
          wechatName: '钱十五',
          points: 3950,
          lastModifiedTime: '2023-06-11 15:55'
        },
        {
          id: 14,
          phone: '12600126000',
          name: '孙十六',
          wechatName: '孙十六',
          points: 2340,
          lastModifiedTime: '2023-06-10 09:45'
        },
        {
          id: 15,
          phone: '12500125000',
          name: '周十七',
          wechatName: '周十七',
          points: 1520,
          lastModifiedTime: '2023-06-09 13:30'
        }
      ]

      // 获取查询参数
      const phone = req.query.phone || ''
      const wechatId = req.query.wechatId || ''

      // 根据查询参数过滤数据
      let filteredUsers = [...allUsers]
      if (phone) {
        filteredUsers = filteredUsers.filter((user) => user.phone.includes(phone))
      }
      if (wechatId) {
        filteredUsers = filteredUsers.filter((user) => user.wechatName && user.wechatName.includes(wechatId))
      }

      // 获取分页参数
      const page = parseInt(req.query.page) || 1
      const pageSize = parseInt(req.query.pageSize) || 5

      // 计算分页数据
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const pageData = filteredUsers.slice(start, end)

      return {
        code: 200,
        message: '获取用户列表成功',
        data: pageData,
        total: filteredUsers.length,
        page,
        pageSize
      }
    }
  },

  // 获取用户积分明细接口
  {
    url: '/user/points-detail',
    method: 'get',
    response: (req) => {
      // 模拟的积分明细数据
      const pointsDetails = [
        {
          id: 1,
          userId: req.query.userId || 1,
          wechatName: '张三',
          pointsType: '注册获得积分',
          updateTime: '2024-10-01 10:00',
          phone: '13800138000',
          addPoints: 200,
          totalPoints: 200
        },
        {
          id: 2,
          userId: req.query.userId || 1,
          wechatName: '张三',
          pointsType: '邀请好友获得积分',
          updateTime: '2024-10-02 14:30',
          phone: '13900139000',
          addPoints: 100,
          totalPoints: 300
        },
        {
          id: 3,
          userId: req.query.userId || 1,
          wechatName: '张三',
          pointsType: '积分商城兑换耗积分',
          updateTime: '2024-10-03 16:45',
          phone: '13700137000',
          addPoints: -50,
          totalPoints: 150
        },
        {
          id: 4,
          userId: req.query.userId || 1,
          wechatName: '张三',
          pointsType: '购买商品获得积分',
          updateTime: '2024-10-04 09:15',
          phone: '13800138000',
          addPoints: 300,
          totalPoints: 450
        },
        {
          id: 5,
          userId: req.query.userId || 1,
          wechatName: '张三',
          pointsType: '生日奖励积分',
          updateTime: '2024-10-05 11:30',
          phone: '13800138000',
          addPoints: 100,
          totalPoints: 550
        },
        {
          id: 6,
          userId: req.query.userId || 1,
          wechatName: '张三',
          pointsType: '积分商城兑换耗积分',
          updateTime: '2024-10-06 15:20',
          phone: '13800138000',
          addPoints: -200,
          totalPoints: 350
        },
        {
          id: 7,
          userId: req.query.userId || 1,
          wechatName: '张三',
          pointsType: '活动奖励积分',
          updateTime: '2024-10-07 14:10',
          phone: '13800138000',
          addPoints: 150,
          totalPoints: 500
        },
        {
          id: 8,
          userId: req.query.userId || 1,
          wechatName: '张三',
          pointsType: '年度会员积分',
          updateTime: '2024-10-08 09:45',
          phone: '13800138000',
          addPoints: 500,
          totalPoints: 1000
        },
        {
          id: 9,
          userId: req.query.userId || 1,
          wechatName: '张三',
          pointsType: '评价获得积分',
          updateTime: '2024-10-09 16:30',
          phone: '13800138000',
          addPoints: 50,
          totalPoints: 1050
        },
        {
          id: 10,
          userId: req.query.userId || 1,
          wechatName: '张三',
          pointsType: '完善资料获得积分',
          updateTime: '2024-10-10 10:15',
          phone: '13800138000',
          addPoints: 100,
          totalPoints: 1150
        }
      ]

      // 筛选数据
      let filteredDetails = [...pointsDetails]

      // 如果有时间范围筛选
      if (req.query.startDate && req.query.endDate) {
        const startDate = new Date(req.query.startDate).getTime()
        const endDate = new Date(req.query.endDate).getTime()

        filteredDetails = filteredDetails.filter((item) => {
          const itemDate = new Date(item.updateTime).getTime()
          return itemDate >= startDate && itemDate <= endDate
        })
      }

      // 如果有积分类型筛选
      if (req.query.pointsType && req.query.pointsType !== '全部') {
        filteredDetails = filteredDetails.filter((item) => item.pointsType === req.query.pointsType)
      }

      // 获取分页参数
      const page = parseInt(req.query.page) || 1
      const pageSize = parseInt(req.query.pageSize) || 10

      // 计算分页数据
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const pageData = filteredDetails.slice(start, end)

      return {
        code: 200,
        message: '获取用户积分明细成功',
        data: pageData,
        total: filteredDetails.length,
        page,
        pageSize
      }
    }
  }
]
