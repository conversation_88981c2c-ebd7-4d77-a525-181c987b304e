<template>
  <div class="role-list-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>角色权限列表</h2>
      <p>管理系统内所有角色权限</p>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <div class="search-item">
          <div class="search-label">角色名称</div>
          <el-input
            v-model="searchForm.roleName"
            placeholder="请输入角色名称"
            clearable
          />
        </div>
        <div class="search-item">
          <div class="search-label">角色ID</div>
          <el-input
            v-model="searchForm.roleId"
            placeholder="请输入角色ID"
            clearable
          />
        </div>
      </div>
      <div style="margin-top: 20px">
        <CustomBtn type="add" @click="handleAdd" />
        <CustomBtn type="blue" @click="handleSearch" />
        <CustomBtn type="reset" @click="handleReset" />
      </div>
    </el-card>

    <!-- 角色列表 -->
    <el-table :data="roleList" style="width: 100%" v-loading="loading">
      <el-table-column prop="roleId" label="角色ID" width="150" />
      <el-table-column prop="roleName" label="角色名称" width="200" />
      <el-table-column prop="description" label="权限描述" width="200" />
      <el-table-column prop="modifier" label="最后修改人" width="150" />
      <el-table-column prop="updateTime" label="最后修改时间" width="180" />
      <el-table-column label="操作" min-width="240" align="right">
        <template #default="scope">
          <div class="action-buttons">
            <CustomBtn type="check" style="margin-right: 0px;" @click="handleView(scope.row)" />
            <CustomBtn type="edit"  @click="handleEdit(scope.row)"
              >修改</CustomBtn
            >
            <CustomBtn type="red" style="margin: 0px 8px;" @click="handleDelete(scope.row)"
              >删除</CustomBtn
            >
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div style="display: flex; justify-content: center; margin-top: 0px">
      <SimplePagination
        v-model="currentPage"
        :page-size="pageSize"
        :total="total"
        @change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';
import CustomBtn from '@/components/custom-btn.vue';
import SimplePagination from '@/components/SimplePagination.vue';
import { getRoleList, deleteRole } from '@/api/subaccount';

const router = useRouter();

// 搜索表单
const searchForm = reactive({
  roleName: '',
  roleId: '',
});

// 列表数据
const roleList = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 获取角色列表
const fetchRoleList = async () => {
  loading.value = true;
  try {
    const params = {
      roleName: searchForm.roleName,
      roleId: searchForm.roleId,
      page: currentPage.value,
      size: pageSize.value,
    };

    const res = await getRoleList(params);
    if (res.code === 200) {
      roleList.value = res.data.list;
      total.value = res.data.total;
    } else {
      ElMessage.error(res.message || '获取角色列表失败');
    }
  } catch (error) {
    console.error('获取角色列表出错:', error);
    ElMessage.error('获取角色列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  currentPage.value = 1;
  fetchRoleList();
};

// 重置
const handleReset = () => {
  searchForm.roleName = '';
  searchForm.roleId = '';
  currentPage.value = 1;
  fetchRoleList();
};

// 新增角色
const handleAdd = () => {
  router.push('/account/role-add');
};

// 查看角色
const handleView = (row) => {
  router.push(`/account/role-view/${row.roleId}`);
};

// 编辑角色
const handleEdit = (row) => {
  router.push(`/account/role-edit/${row.roleId}`);
};

// 删除角色
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除角色 "${row.roleName}" 吗？`, '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const res = await deleteRole(row.roleId);
        if (res.code === 200) {
          ElMessage.success('删除成功');
          fetchRoleList();
        } else {
          ElMessage.error(res.message || '删除失败');
        }
      } catch (error) {
        console.error('删除角色出错:', error);
        ElMessage.error('删除失败');
      }
    })
    .catch(() => {
      // 取消删除
    });
};

// 分页大小变化
// const handleSizeChange = (val) => {
//   pageSize.value = val;
//   fetchRoleList();
// };

// 当前页变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchRoleList();
};

// 页面加载时获取数据
onMounted(() => {
  fetchRoleList();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables.scss' as *;

.role-list-page {
  padding: $spacing-xl;
  background-color: $bg-color;
  min-height: 100vh;
  max-width: 1280px;
  margin: 0 auto;

  .page-header {
    margin-bottom: $spacing-xl;

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: $text-color-dark;
      margin-bottom: $spacing-xs;
    }

    p {
      font-size: 14px;
      color: $text-color-light;
    }
  }

  .search-card {
    border-radius: 10px;
    margin-bottom: $spacing-xl;

    .search-form {
      display: flex;
      flex-wrap: wrap;
      gap: 14px;
      align-items: flex-end;

      .search-item {
        flex: 1;
        min-width: 200px;

        .search-label {
          font-size: 14px;
          color: #86909c;
          margin-bottom: 4px;
        }
      }

      .search-actions {
        display: flex;
        gap: 14px;
        margin-top: 20px;
      }
    }
  }

  .role-card {
    border-radius: 10px;

    .action-buttons {
      display: flex;
      justify-content: center;
    }

    .pagination-container {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}

// Element Plus 样式覆盖
:deep(.el-card__body) {
  padding: $spacing-xl;
}

:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-button) {
  border-radius: 6px;
}
/* 调整表头样式 */
:deep(.el-table__header th) {
  background-color: #f9fafb;
  height: 50px;
  line-height: 50px;
}
</style>
