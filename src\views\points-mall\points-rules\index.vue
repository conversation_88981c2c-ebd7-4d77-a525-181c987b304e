<template>
  <div class="points-rules-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>积分规则设置</h2>
      <p>设置系统内的积分规则</p>
    </div>

    <!-- 表单卡片 -->
    <el-card class="form-card" shadow="never">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="0"
        class="rules-form"
        v-loading="loading"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="form-section">
              <div class="section-label">注册送积分</div>
              <el-input
                v-model.number="formData.registerPoints"
                type="number"
                placeholder="请输入注册送的积分数量"
                clearable
                class="section-input"
              />
            </div>
          </el-col>
          <el-col :span="12">
            <div class="form-section">
              <div class="section-label">邀请好友送积分</div>
              <el-input
                v-model.number="formData.invitePoints"
                type="number"
                placeholder="请输入邀请好友送的积分数量"
                clearable
                class="section-input"
              />
            </div>
          </el-col>
        </el-row>

        <div class="form-section">
          <div class="section-label">上传凭证审核通过送积分</div>
          <el-input
            v-model.number="formData.verifyPoints"
            type="number"
            placeholder="请输入上传凭证审核通过送的积分数量"
            clearable
            class="section-input"
          />
        </div>

        <el-row>
          <el-col :span="24">
            <div class="form-section">
              <div class="section-label">积分规则明细描述（8000字以内）</div>
              <el-input
                v-model="formData.rulesDescription"
                type="textarea"
                :autosize="{ minRows: 10, maxRows: 20 }"
                placeholder="请输入积分规则明细描述"
                maxlength="8000"
                show-word-limit
                class="section-input"
              />
            </div>
          </el-col>
        </el-row>

        <div class="form-actions">
          <CustomBtn type="save" @click="handleSave" />
          <CustomBtn style="margin: 0 0" type="reset" @click="handleReset" />
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { getPointsRules, updatePointsRules } from '@/api/points-mall';
import CustomBtn from '@/components/custom-btn.vue';

// 表单数据
const formData = reactive({
  registerPoints: '',
  invitePoints: '',
  verifyPoints: '',
  rulesDescription: '',
});

// 表单验证规则
const rules = {
  registerPoints: [
    { required: true, message: '请输入注册送积分数量', trigger: 'blur' },
    { type: 'number', message: '积分必须为数字', trigger: 'blur' },
    { min: 0, message: '积分不能小于0', trigger: 'blur' },
  ],
  invitePoints: [
    { required: true, message: '请输入邀请好友送积分数量', trigger: 'blur' },
    { type: 'number', message: '积分必须为数字', trigger: 'blur' },
    { min: 0, message: '积分不能小于0', trigger: 'blur' },
  ],
  verifyPoints: [
    {
      required: true,
      message: '请输入上传凭证审核通过送积分数量',
      trigger: 'blur',
    },
    { type: 'number', message: '积分必须为数字', trigger: 'blur' },
    { min: 0, message: '积分不能小于0', trigger: 'blur' },
  ],
  rulesDescription: [
    { required: true, message: '请输入积分规则明细描述', trigger: 'blur' },
  ],
};

const formRef = ref(null);
const loading = ref(false);
const saving = ref(false);

// 获取积分规则数据
const fetchRules = async () => {
  loading.value = true;
  try {
    const res = await getPointsRules();
    if (res.code === 200) {
      Object.assign(formData, res.data);
    } else {
      ElMessage.error(res.message || '获取积分规则失败');
    }
  } catch (error) {
    console.error('获取积分规则出错:', error);
    ElMessage.error('获取积分规则失败');
  } finally {
    loading.value = false;
  }
};

// 保存积分规则
const handleSave = async () => {
  try {
    await formRef.value.validate();
    saving.value = true;

    const res = await updatePointsRules(formData);
    if (res.code === 200) {
      ElMessage.success('保存成功');
    } else {
      ElMessage.error(res.message || '保存失败');
    }
  } catch (error) {
    console.error('保存积分规则出错:', error);
    ElMessage.error('保存失败，请检查表单数据');
  } finally {
    saving.value = false;
  }
};

// 重置表单
const handleReset = () => {
  formRef.value.resetFields();
  fetchRules();
};

// 页面加载时获取数据
onMounted(() => {
  fetchRules();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables.scss' as *;

.points-rules-page {
  padding: $spacing-xl;
  background-color: $bg-color;
  min-height: 100vh;
  max-width: 1280px;
  margin: 0 auto;

  .page-header {
    margin-bottom: $spacing-xl;

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: $text-color-dark;
      margin-bottom: $spacing-xs;
    }

    p {
      font-size: 14px;
      color: $text-color-light;
    }
  }

  .form-card {
    border-radius: 10px;

    .rules-form {
      .form-section {
        margin-bottom: 24px;

        .section-label {
          font-size: 18px;
          font-weight: bold;
          color: #86909c;
          margin-bottom: 8px;
        }

        .section-input {
          width: 100%;
        }
      }

      .form-actions {
        margin-top: 40px;
        display: flex;
        justify-content: flex-start;
        gap: 14px;
      }
    }
  }
}

// Element Plus 样式覆盖
:deep(.el-card__body) {
  padding: $spacing-xl;
}

:deep(.el-form-item__label) {
  font-size: 14px;
  color: #86909c;
  font-weight: normal;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
  font-family: inherit;
  min-height: 200px;
  resize: vertical;
}

:deep(.el-input__count) {
  background-color: transparent;
  font-size: 12px;
  color: #909399;
  padding: 5px 10px;
  text-align: right;
}
</style>
