<template>
  <div class="account-list-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>账号列表</h2>
      <p>管理系统内所有账号</p>
    </div>

    <!-- 搜索区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <div class="search-item">
          <div class="search-label">姓名</div>
          <el-input
            v-model="searchForm.realName"
            placeholder="请输入姓名"
            clearable
          />
        </div>
        <div class="search-item">
          <div class="search-label">账号</div>
          <el-input
            v-model="searchForm.username"
            placeholder="请输入账号"
            clearable
          />
        </div>
        <div class="search-item">
          <div class="search-label">账号权限</div>
          <el-select
            v-model="searchForm.permissions"
            placeholder="全部权限"
            clearable
            style="width: 100%"
          >
            <el-option label="全部权限" value=""></el-option>
            <el-option label="普通权限" value="普通权限"></el-option>
            <el-option label="管理员权限" value="管理员权限"></el-option>
            <el-option label="财务权限" value="财务权限"></el-option>
            <el-option label="访客权限" value="访客权限"></el-option>
          </el-select>
        </div>
      </div>
      <div style="margin-top: 20px">
        <CustomBtn type="add" @click="handleAdd" />
        <CustomBtn type="blue" @click="handleSearch" />
        <CustomBtn type="reset" @click="handleReset" />
      </div>
    </el-card>

    <!-- 账号列表 -->

    <el-table :data="accountList" style="width: 100%" v-loading="loading">
      <el-table-column prop="username" label="账号" width="150" />
      <el-table-column prop="realName" label="姓名" width="150" />
      <el-table-column label="账号权限" min-width="150">
        <template #default="scope">
          <div class="permission-tags">
            <span
              v-for="permission in scope.row.permissions"
              :key="permission"
              :class="['permission-tag', getPermissionTagClass(permission)]"
            >
              {{ permission }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="400">
        <template #default="scope">
          <div class="action-buttons">
            <CustomBtn
              style="
                font-size: 16px;
                width: 80px;
                height: 40px;
                border-radius: 8px;
                margin: 0px 0px;
              "
              type="check"
              @click="handleView(scope.row)"
            />
            <CustomBtn
              style="
                font-size: 16px;
                width: 80px;
                height: 40px;
                border-radius: 8px;
                margin: 0px 0px;
              "
              type="edit"
              @click="handleEdit(scope.row)"
            />
            <CustomBtn
              style="
                font-size: 16px;
                width: 80px;
                height: 40px;
                border-radius: 8px;
                margin: 0px 0px;
              "
              type="red"
              @click="handleDelete(scope.row)"
              >删除</CustomBtn
            >
            <el-button
              size="small"
              class="freeze-btn"
              @click="handleFreeze(scope.row)"
            >
              <el-icon><Lock /></el-icon>
              冻结
            </el-button>
            <el-button
              size="small"
              class="reset-pwd-btn"
              @click="handleResetPassword(scope.row)"
            >
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <SimplePagination
        v-model="currentPage"
        :page-size="pageSize"
        :total="total"
        @change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Lock, Refresh } from '@element-plus/icons-vue';
import CustomBtn from '@/components/custom-btn.vue';
import SimplePagination from '@/components/SimplePagination.vue';
import { getAccountList, deleteAccount } from '@/api/subaccount';
import { useRouter } from 'vue-router';

const router = useRouter();

// 搜索表单
const searchForm = reactive({
  realName: '',
  username: '',
  permissions: '',
});

// 列表数据
const accountList = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 获取账号列表
const fetchAccountList = async () => {
  loading.value = true;
  try {
    const params = {
      realName: searchForm.realName,
      username: searchForm.username,
      permissions: searchForm.permissions,
      page: currentPage.value,
      size: pageSize.value,
    };

    const res = await getAccountList(params);
    if (res.code === 200) {
      accountList.value = res.data.list;
      total.value = res.data.total;
    } else {
      ElMessage.error(res.message || '获取账号列表失败');
    }
  } catch (error) {
    console.error('获取账号列表出错:', error);
    ElMessage.error('获取账号列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取权限标签类
const getPermissionTagClass = (permission) => {
  const classMap = {
    普通权限: '',
    管理员权限: 'success',
    财务权限: 'warning',
    访客权限: 'info',
  };
  return classMap[permission] || '';
};

// 搜索
const handleSearch = () => {
  currentPage.value = 1;
  fetchAccountList();
};

// 重置
const handleReset = () => {
  searchForm.realName = '';
  searchForm.username = '';
  searchForm.permissions = '';
  currentPage.value = 1;
  fetchAccountList();
};

// 新增账号
const handleAdd = () => {
  router.push('/account/account-add');
};

// 查看账号
const handleView = (row) => {
  router.push(`/account/account-view/${row.accountId || '1'}`);
};

// 编辑账号
const handleEdit = (row) => {
  router.push(`/account/account-edit/${row.accountId || '1'}`);
};

// 删除账号
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除账号 "${row.username}" 吗？`, '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const res = await deleteAccount(row.accountId);
        if (res.code === 200) {
          ElMessage.success('删除成功');
          fetchAccountList();
        } else {
          ElMessage.error(res.message || '删除失败');
        }
      } catch (error) {
        console.error('删除账号出错:', error);
        ElMessage.error('删除失败');
      }
    })
    .catch(() => {
      // 取消删除
    });
};

// 冻结账号
const handleFreeze = (row) => {
  ElMessageBox.confirm(
    `确定要冻结账号「${row.username || row.realName || row.accountName}」吗？`,
    '冻结确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  )
    .then(async () => {
      ElMessage.success('账号已冻结');
    })
    .catch(() => {});
};

// 重置密码
const handleResetPassword = (row) => {
  ElMessageBox.confirm(
    `确定要重置账号「${row.username || row.realName || row.accountName}」的密码吗？`,
    '重置密码确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  )
    .then(async () => {
      ElMessage.success('密码已重置');
    })
    .catch(() => {});
};

// 分页大小变化
// const handleSizeChange = (val) => {
//   pageSize.value = val;
//   fetchAccountList();
// };

// 当前页变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchAccountList();
};

// 页面加载时获取数据
onMounted(() => {
  fetchAccountList();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables.scss' as *;

.account-list-page {
  padding: $spacing-xl;
  background-color: $bg-color;
  max-width: 1280px;
  margin: 0 auto;

  .page-header {
    margin-bottom: $spacing-xl;

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: $text-color-dark;
      margin-bottom: $spacing-xs;
    }

    p {
      font-size: 14px;
      color: $text-color-light;
    }
  }

  .search-card {
    border-radius: 10px;
    margin-bottom: $spacing-xl;

    .search-form {
      display: flex;
      flex-wrap: wrap;
      gap: 14px;
      align-items: flex-end;

      .search-item {
        flex: 1;
        min-width: 200px;

        .search-label {
          font-size: 14px;
          color: #86909c;
          margin-bottom: 4px;
        }
      }
    }
  }

  .permission-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;

    .permission-tag {
      font-size: 12px;
      height: 24px;
      line-height: 24px;
      padding: 0 8px;
      border-radius: 12px;
      display: inline-block;

      // 普通权限 - 蓝色
      &:not(.success):not(.warning):not(.info) {
        background-color: #e9eeff;
        color: #165dff;
      }

      // 管理员权限 - 绿色
      &.success {
        background-color: #e8f9e8;
        color: #00b42b;
      }

      // 财务权限 - 青色
      &.warning {
        background-color: #e8fffe;
        color: #00c9c1;
      }

      // 访客权限 - 橙色
      &.info {
        background-color: #fff7e6;
        color: #ff7d00;
      }
    }
  }

  .action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 4px;
    flex-wrap: wrap;

    .freeze-btn {
      font-size: 16px;
      width: 80px;
      height: 40px;
      border-radius: 8px;
      background-color: #f2f3f5 !important;
      color: #86909c !important;
      border-color: #f2f3f5 !important;
      margin: 0px 0px;
    }

    .reset-pwd-btn {
      font-size: 16px;
      height: 40px;
      border-radius: 8px;
      background-color: #ebf9f9 !important;
      color: #11c6c2 !important;
      border-color: #ebf9f9 !important;
      margin: 0px 0px;
    }
  }
}
.pagination-container {
  margin-top: 0px;
  display: flex;
  justify-content: center;
}
// Element Plus 样式覆盖
:deep(.el-card__body) {
  padding: $spacing-xl;
}

:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

/* 调整表头样式 */
:deep(.el-table__header th) {
  background-color: #f9fafb;
  height: 50px;
  line-height: 50px;
}

:deep(.el-button) {
  border-radius: 6px;
}

:deep(.el-select) {
  width: 100%;
}
</style>
