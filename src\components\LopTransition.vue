<template>
  <Transition :name="name">
    <slot></slot>
  </Transition>
</template>

<script setup>
const props = defineProps({
  name: {
    type: String,
    default: 'v'
  }
})
</script>

<style>
.v-enter-active,
.v-leave-active {
  transition: opacity 0.3s ease;
}
.v-enter-from,
.v-leave-to {
  opacity: 0;
}
/* 快速的淡入淡出 */
.fast-enter-active,
.fast-leave-active {
  transition: opacity 0.3s ease;
}
.fast-enter-from,
.fast-leave-to {
  opacity: 0;
}
</style>
