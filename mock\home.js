// 首页相关的mock接口
export default [
  // 获取首页统计数据
  {
    url: '/home/<USER>',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: '获取统计数据成功',
        data: {
          userTotal: 12845,
          todayRegister: 156,
          todayVisit: 2148,
          monthOrderCount: 4287,
          todayIncome: 864,
          pendingOrder: 356
        }
      }
    }
  },

  // 获取用户趋势数据
  {
    url: '/home/<USER>/trend',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: '获取用户趋势数据成功',
        data: {
          days: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
          data: [120, 182, 191, 234, 190, 230, 160]
        }
      }
    }
  },

  // 获取最新注册用户
  {
    url: '/home/<USER>/users',
    method: 'get',
    response: (req) => {
      // 模拟的用户数据
      const allUsers = [
        {
          id: 1,
          name: '张三',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          time: '今天 10:24',
          color: '#409EFF',
          inviter: {
            name: '李四',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#67C23A'
          },
          inviteTime: '今天 10:20'
        },
        {
          id: 2,
          name: '李四',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          time: '今天 09:15',
          color: '#67C23A',
          inviter: {
            name: '王五',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#409EFF'
          },
          inviteTime: '今天 09:10'
        },
        {
          id: 3,
          name: '王五',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          time: '昨天 18:32',
          color: '#E6A23C',
          inviter: {
            name: '赵六',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#F56C6C'
          },
          inviteTime: '昨天 18:25'
        },
        {
          id: 4,
          name: '赵六',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          time: '昨天 15:47',
          color: '#F56C6C',
          inviter: {
            name: '钱七',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#909399'
          },
          inviteTime: '昨天 15:40'
        },
        {
          id: 5,
          name: '钱七',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          time: '昨天 11:00',
          color: '#909399',
          inviter: {
            name: '孙八',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#909399'
          },
          inviteTime: '昨天 11:05'
        },
        {
          id: 6,
          name: '孙八',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          time: '前天 16:45',
          color: '#409EFF',
          inviter: {
            name: '周九',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#67C23A'
          },
          inviteTime: '前天 16:40'
        },
        {
          id: 7,
          name: '周九',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          time: '前天 14:20',
          color: '#67C23A',
          inviter: {
            name: '吴十',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#E6A23C'
          },
          inviteTime: '前天 14:15'
        },
        {
          id: 8,
          name: '吴十',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          time: '前天 11:30',
          color: '#E6A23C',
          inviter: {
            name: '郑十一',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#F56C6C'
          },
          inviteTime: '前天 11:25'
        },
        {
          id: 9,
          name: '郑十一',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          time: '3天前 19:45',
          color: '#F56C6C',
          inviter: {
            name: '王十二',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#909399'
          },
          inviteTime: '3天前 19:40'
        },
        {
          id: 10,
          name: '王十二',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          time: '3天前 15:10',
          color: '#909399',
          inviter: {
            name: '李十三',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#409EFF'
          },
          inviteTime: '3天前 15:05'
        },
        {
          id: 11,
          name: '李十三',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          time: '3天前 10:30',
          color: '#409EFF',
          inviter: {
            name: '赵十四',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#67C23A'
          },
          inviteTime: '3天前 10:25'
        },
        {
          id: 12,
          name: '赵十四',
          avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
          time: '4天前 17:20',
          color: '#67C23A',
          inviter: {
            name: '钱十五',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#E6A23C'
          },
          inviteTime: '4天前 17:15'
        }
      ]

      // 获取分页参数
      const page = parseInt(req.query.page) || 1
      const pageSize = parseInt(req.query.pageSize) || 5

      // 计算分页数据
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const pageData = allUsers.slice(start, end)

      return {
        code: 200,
        message: '获取最新注册用户成功',
        data: pageData,
        total: allUsers.length,
        page,
        pageSize
      }
    }
  },

  // 获取最近访问用户
  {
    url: '/home/<USER>/visits',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: '获取最近访问用户成功',
        data: [
          {
            id: 1,
            name: '李四',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            time: '今天 10:30',
            color: '#67C23A'
          },
          {
            id: 2,
            name: '王五',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            time: '今天 09:10',
            color: '#409EFF'
          },
          {
            id: 3,
            name: '赵六',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            time: '昨天 16:25',
            color: '#E6A23C'
          },
          {
            id: 4,
            name: '钱七',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            time: '昨天 15:40',
            color: '#F56C6C'
          },
          {
            id: 5,
            name: '孙八',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            time: '昨天 11:05',
            color: '#909399'
          }
        ],
        total: 156
      }
    }
  },

  // 获取最近积分订单
  {
    url: '/home/<USER>/orders',
    method: 'get',
    response: (req) => {
      // 模拟的订单数据
      const allOrders = [
        {
          id: 'ORD-2023622901',
          user: {
            name: '张三',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#409EFF'
          },
          product: '高级音乐卡',
          points: 2000,
          status: '已完成'
        },
        {
          id: 'ORD-2023622987',
          user: {
            name: '李四',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#67C23A'
          },
          product: '智能手环',
          points: 5800,
          status: '处理中'
        },
        {
          id: 'ORD-2023622973',
          user: {
            name: '王五',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#E6A23C'
          },
          product: '100元话费券',
          points: 1500,
          status: '已完成'
        },
        {
          id: 'ORD-2023622965',
          user: {
            name: '赵六',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#F56C6C'
          },
          product: '蓝牙耳机',
          points: 4200,
          status: '已取消'
        },
        {
          id: 'ORD-2023622941',
          user: {
            name: '钱七',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#909399'
          },
          product: '购物卡50元',
          points: 1800,
          status: '已完成'
        },
        {
          id: 'ORD-2023622935',
          user: {
            name: '孙八',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#409EFF'
          },
          product: '电影票兑换券',
          points: 1200,
          status: '已完成'
        },
        {
          id: 'ORD-2023622928',
          user: {
            name: '周九',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#67C23A'
          },
          product: '无线充电器',
          points: 3500,
          status: '处理中'
        },
        {
          id: 'ORD-2023622915',
          user: {
            name: '吴十',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#E6A23C'
          },
          product: '智能台灯',
          points: 2800,
          status: '已完成'
        },
        {
          id: 'ORD-2023622908',
          user: {
            name: '郑十一',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#F56C6C'
          },
          product: '便携式风扇',
          points: 1600,
          status: '已完成'
        },
        {
          id: 'ORD-2023622899',
          user: {
            name: '王十二',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#909399'
          },
          product: '运动水壶',
          points: 1000,
          status: '已完成'
        },
        {
          id: 'ORD-2023622886',
          user: {
            name: '李十三',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#409EFF'
          },
          product: '健身会员卡',
          points: 6000,
          status: '处理中'
        },
        {
          id: 'ORD-2023622878',
          user: {
            name: '赵十四',
            avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
            color: '#67C23A'
          },
          product: '移动电源',
          points: 2500,
          status: '已完成'
        }
      ]

      // 获取分页参数
      const page = parseInt(req.query.page) || 1
      const pageSize = parseInt(req.query.pageSize) || 5

      // 计算分页数据
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const pageData = allOrders.slice(start, end)

      return {
        code: 200,
        message: '获取最近积分订单成功',
        data: pageData,
        total: allOrders.length,
        page,
        pageSize
      }
    }
  }
]
