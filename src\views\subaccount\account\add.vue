<template>
  <div class="add-account-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>新增账号</h2>
      <p>添加新的系统账号</p>
    </div>

    <!-- 表单卡片 -->
    <el-card class="form-card" shadow="never">
      <el-form ref="formRef" :model="form" :rules="rules" class="account-form">
        <!-- 基本信息 -->
        <div class="form-row">
          <div class="form-field">
            <label class="field-label">账号</label>
            <el-input
              v-model="form.username"
              placeholder="请输入账号"
              :class="{ 'is-error': errors.username }"
            />
            <div v-if="errors.username" class="error-message">
              {{ errors.username }}
            </div>
          </div>

          <div class="form-field">
            <label class="field-label">姓名</label>
            <el-input
              v-model="form.realName"
              placeholder="请输入姓名"
              :class="{ 'is-error': errors.realName }"
            />
            <div v-if="errors.realName" class="error-message">
              {{ errors.realName }}
            </div>
          </div>

          <div class="form-field">
            <label class="field-label">手机号</label>
            <el-input
              v-model="form.phone"
              placeholder="请输入手机号"
              :class="{ 'is-error': errors.phone }"
            />
            <div v-if="errors.phone" class="error-message">
              {{ errors.phone }}
            </div>
          </div>
        </div>

        <!-- 账号权限 -->
        <div class="permissions-section">
          <label class="field-label">账号权限</label>
          <div class="permission-options">
            <div
              v-for="permission in permissionOptions"
              :key="permission.value"
              :class="[
                'permission-option',
                { selected: form.permissions.includes(permission.value) },
              ]"
              @click="togglePermission(permission.value)"
            >
              {{ permission.label }}
            </div>
          </div>
          <div v-if="errors.permissions" class="error-message">
            {{ errors.permissions }}
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <CustomBtn type="save" @click="handleSave">保存</CustomBtn>
          <CustomBtn type="reset" @click="handleCancel">取消</CustomBtn>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';
import CustomBtn from '@/components/custom-btn.vue';

const router = useRouter();
const formRef = ref();
const loading = ref(false);

// 表单数据
const form = reactive({
  username: '',
  realName: '',
  phone: '',
  permissions: [],
});

// 错误信息
const errors = reactive({
  username: '',
  realName: '',
  phone: '',
  permissions: '',
});

// 权限选项
const permissionOptions = [
  { label: '管理员权限', value: '管理员权限' },
  { label: '普通权限', value: '普通权限' },
  { label: '访客权限', value: '访客权限' },
  { label: '财务权限', value: '财务权限' },
];

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入账号', trigger: 'blur' },
    { min: 3, max: 20, message: '账号长度应为3-20个字符', trigger: 'blur' },
  ],
  realName: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 10, message: '姓名长度应为2-10个字符', trigger: 'blur' },
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: '请输入正确的手机号',
      trigger: 'blur',
    },
  ],
  permissions: [
    { required: true, message: '请选择账号权限', trigger: 'change' },
  ],
};

// 验证单个字段
const validateField = (field, value) => {
  const fieldRules = rules[field];
  if (!fieldRules) return true;

  for (const rule of fieldRules) {
    if (
      rule.required &&
      (!value || (Array.isArray(value) && value.length === 0))
    ) {
      errors[field] = rule.message;
      return false;
    }
    if (rule.min && value.length < rule.min) {
      errors[field] = rule.message;
      return false;
    }
    if (rule.max && value.length > rule.max) {
      errors[field] = rule.message;
      return false;
    }
    if (rule.pattern && !rule.pattern.test(value)) {
      errors[field] = rule.message;
      return false;
    }
  }

  errors[field] = '';
  return true;
};

// 监听表单字段变化，实时验证
watch(
  () => form.username,
  (newVal) => {
    if (errors.username) validateField('username', newVal);
  },
);

watch(
  () => form.realName,
  (newVal) => {
    if (errors.realName) validateField('realName', newVal);
  },
);

watch(
  () => form.phone,
  (newVal) => {
    if (errors.phone) validateField('phone', newVal);
  },
);

watch(
  () => form.permissions,
  (newVal) => {
    if (errors.permissions) validateField('permissions', newVal);
  },
  { deep: true },
);

// 切换权限选择
const togglePermission = (permission) => {
  const index = form.permissions.indexOf(permission);
  if (index > -1) {
    form.permissions.splice(index, 1);
  } else {
    form.permissions.push(permission);
  }
};

// 验证整个表单
const validateForm = () => {
  let isValid = true;

  // 验证所有字段
  Object.keys(rules).forEach((field) => {
    const fieldValue = form[field];
    if (!validateField(field, fieldValue)) {
      isValid = false;
    }
  });

  return isValid;
};

// 保存
const handleSave = async () => {
  try {
    if (!validateForm()) {
      ElMessage.error('请完善表单信息');
      return;
    }

    loading.value = true;

    // 这里调用API保存数据
    console.log('保存账号数据:', form);

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));

    ElMessage.success('账号创建成功');
    router.push('/account/list');
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('保存失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 取消
const handleCancel = () => {
  router.push('/account/list');
};
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables.scss' as *;

.add-account-page {
  padding: $spacing-xl;
  background-color: $bg-color;
  max-width: 1280px;
  margin: 0 auto;

  .page-header {
    margin-bottom: $spacing-xl;

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: $text-color-dark;
      margin-bottom: $spacing-xs;
    }

    p {
      font-size: 14px;
      color: $text-color-light;
    }
  }

  .form-card {
    border-radius: 10px;
    width: 100%;

    .account-form {
      .form-row {
        display: flex;
        gap: 20px;
        margin-bottom: 24px;

        .form-field {
          flex: 1;

          .field-label {
            display: block;
            font-size: 14px;
            color: #86909c;
            margin-bottom: 8px;
            font-weight: normal;
          }

          .error-message {
            font-size: 12px;
            color: #f56565;
            margin-top: 4px;
            line-height: 1.4;
          }
        }
      }

      .permissions-section {
        margin-bottom: 30px;

        .field-label {
          display: block;
          font-size: 14px;
          color: #86909c;
          margin-bottom: 12px;
          font-weight: normal;
        }

        .permission-options {
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          gap: 12px;

          .permission-option {
            padding: 8px 16px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
            background-color: #f9fafb;
            color: #6b7280;

            &:hover {
              border-color: #165dff;
              background-color: #f0f4ff;
            }

            &.selected {
              border-color: transparent;

              // 管理员权限 - 绿色
              &:nth-child(1) {
                background-color: #e8f9e8;
                color: #00b42b;
              }

              // 普通权限 - 蓝色
              &:nth-child(2) {
                background-color: #e9eeff;
                color: #165dff;
              }

              // 访客权限 - 橙色
              &:nth-child(3) {
                background-color: #fff7e6;
                color: #ff7d00;
              }

              // 财务权限 - 青色
              &:nth-child(4) {
                background-color: #e8fffe;
                color: #00c9c1;
              }
            }
          }
        }

        .error-message {
          font-size: 12px;
          color: #f56565;
          margin-top: 8px;
          line-height: 1.4;
        }
      }

      .form-actions {
        display: flex;
        gap: 12px;
        padding-top: 20px;
        border-top: 1px solid #f0f0f0;

        .save-btn {
          background-color: #165dff;
          border-color: #165dff;
          color: white;
          padding: 12px 24px;
          border-radius: 8px;
          font-size: 14px;

          &:hover {
            background-color: #4080ff;
            border-color: #4080ff;
          }
        }

        .cancel-btn {
          background-color: #f5f5f5;
          border-color: #d9d9d9;
          color: #666;
          padding: 12px 24px;
          border-radius: 8px;
          font-size: 14px;

          &:hover {
            background-color: #e6e6e6;
            border-color: #b3b3b3;
          }
        }
      }
    }
  }
}

// Element Plus 样式覆盖
:deep(.el-card__body) {
  padding: $spacing-xl;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  padding: 12px 16px;

  &.is-error {
    border-color: #f56565;
    box-shadow: 0 0 0 1px #f56565;
  }
}

:deep(.el-input__inner) {
  font-size: 14px;
}

:deep(.el-input__inner::placeholder) {
  color: #9ca3af;
}

:deep(.el-input.is-error .el-input__wrapper) {
  border-color: #f56565;
  box-shadow: 0 0 0 1px #f56565;
}
</style>
