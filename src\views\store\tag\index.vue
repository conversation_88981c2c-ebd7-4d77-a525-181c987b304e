<template>
  <div class="store-tag-page">
    <transition name="fade" mode="out-in">
      <div v-if="!showEditForm" key="list">
        <div class="page-header">
          <h2>门店类型标签</h2>
          <p>管理系统内所有门店类型标签</p>
        </div>

        <!-- 筛选区域 -->
        <el-card class="filter-card" shadow="never">
          <div class="filter-row">
            <div class="filter-item">
              <div class="filter-label">选择地区</div>
              <el-select v-model="searchForm.province" placeholder="请选择省/直辖市" clearable class="filter-select">
                <el-option v-for="item in provinceOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>

            <div class="filter-item">
              <div class="filter-label">选择城市</div>
              <el-select v-model="searchForm.city" placeholder="请选择市/区" clearable class="filter-select">
                <el-option v-for="item in cityOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>

            <div class="filter-item">
              <div class="filter-label">选择区县</div>
              <el-select v-model="searchForm.district" placeholder="请选择区/县" clearable class="filter-select">
                <el-option v-for="item in districtOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>

            <div class="filter-item">
              <div class="filter-label">门店名称</div>
              <el-input v-model="searchForm.name" placeholder="请输入门店名称" clearable prefix-icon="Search"></el-input>
            </div>
          </div>

          <div class="filter-row">
            <div class="filter-item">
              <div class="filter-label">门店认证</div>
              <el-select v-model="searchForm.certification" placeholder="请选择门店认证" clearable class="filter-select">
                <el-option v-for="item in certificationOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>

            <div class="filter-item">
              <div class="filter-label">门店星级</div>
              <el-select v-model="searchForm.level" placeholder="请选择门店星级" clearable class="filter-select">
                <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>

            <div class="filter-item">
              <div class="filter-label">门店类型</div>
              <el-select v-model="searchForm.type" placeholder="请选择门店类型" clearable class="filter-select">
                <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>

            <div class="filter-actions">
              <el-button @click="handleReset" plain class="reset-button" :disabled="isResetDisabled">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
              <el-button type="primary" @click="handleSearch" class="search-button">
                <el-icon><Search /></el-icon>
                查询
              </el-button>
            </div>
          </div>
        </el-card>

        <!-- 门店标签列表 -->
        <el-card class="store-card" shadow="never">
          <el-table :data="storeList" style="width: 100%" v-loading="loading" :header-cell-style="tableHeaderStyle" :cell-style="tableCellStyle">
            <el-table-column label="所属区域" min-width="120">
              <template #default="scope">
                <span class="text-secondary">{{ scope.row.areas.join(', ') }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="门店名称" min-width="180">
              <template #default="scope">
                <span class="store-name">{{ scope.row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="联系电话" width="150">
              <template #default="scope">
                <span class="text-secondary">{{ scope.row.phone }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="certification" label="门店认证" width="180">
              <template #default="scope">
                <div class="certification-tag-wrapper">
                  <span class="certification-tag" style="border-radius: 30px">{{ scope.row.certification }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="level" label="门店星级" width="120">
              <template #default="scope">
                <div class="star-rating">
                  <el-rate v-model="scope.row.level" disabled :max="5" :allow-half="false" :show-score="false" :space="0"></el-rate>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="门店类型" width="120">
              <template #default="scope">
                <div class="type-tag-wrapper">
                  <span class="type-tag" style="border-radius: 30px">{{ scope.row.type }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="tags" label="门店标签" min-width="200">
              <template #default="scope">
                <div class="store-tags">
                  <el-tag v-for="(tag, index) in scope.row.tags" :key="index" class="store-tag" :type="getTagType(tag)" style="border-radius: 30px">
                    {{ tag }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" align="center" fixed="right">
              <template #default="scope">
                <div class="action-buttons">
                  <el-button size="small" @click="handleView(scope.row)" class="table-action-btn view-btn">
                    <el-icon><View /></el-icon>
                    查看
                  </el-button>
                  <el-button size="small" @click="handleEdit(scope.row)" class="table-action-btn edit-btn">
                    <el-icon><Edit /></el-icon>
                    修改
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 分页栏 - 移出卡片，采用简洁的数字分页形式 -->
        <SimplePagination v-model="pagination.page" :page-size="pagination.pageSize" :total="pagination.total" @change="handleCurrentChange" />
      </div>

      <div v-else key="edit-form">
        <StoreTagForm v-model:storeData="currentStoreData" :availableTags="availableTags" :availableCertifications="availableCertifications" @cancel="handleCancelEdit" @submit="handleSubmitTagForm" @tag-deleted="handleTagDeleted" @certification-deleted="handleCertificationDeleted" />
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, View, Edit, Delete, Refresh } from '@element-plus/icons-vue'
import { getStoreTagList, getProvinces, getCities, getDistricts, getStoreCertifications, updateStoreTag } from '@/api/store'
import StoreTagForm from '@/views/store/components/StoreTagForm.vue'
import SimplePagination from '@/components/SimplePagination.vue'

// 搜索表单
const searchForm = reactive({
  province: '',
  city: '',
  district: '',
  name: '',
  certification: '',
  level: '',
  type: ''
})

// 省份选项
const provinceOptions = ref([])

// 城市选项
const cityOptions = ref([])

// 区县选项
const districtOptions = ref([])

// 门店认证选项
const certificationOptions = ref([{ value: '', label: '全部' }])

// 门店星级选项 - 直接定义枚举值
const levelOptions = ref([
  { value: '', label: '全部' },
  { value: '1', label: '1星' },
  { value: '2', label: '2星' },
  { value: '3', label: '3星' },
  { value: '4', label: '4星' },
  { value: '5', label: '5星' }
])

// 门店类型选项 - 直接定义枚举值
const typeOptions = ref([
  { value: '', label: '全部' },
  { value: '李白智能版合作门店', label: '李白智能版合作门店' },
  { value: '李白专业版合作门店', label: '李白专业版合作门店' }
])

// 门店列表
const storeList = ref([])

// 加载状态
const loading = ref(false)

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 5,
  total: 0
})

// 是否重置按钮禁用状态
const isResetDisabled = ref(false)

// 表格表头样式
const tableHeaderStyle = {
  backgroundColor: '#FAFBFC',
  color: '#89929E',
  fontSize: '14px',
  fontWeight: '600',
  height: '50px'
}

// 表格单元格样式
const tableCellStyle = {
  padding: '12px 0',
  fontSize: '14px'
}

// 修改门店标签表单显示控制
const showEditForm = ref(false)
const currentStoreData = ref({
  id: '',
  name: '',
  phone: '',
  areas: [],
  level: '',
  crown: '',
  type: '',
  tags: [],
  certifications: []
})

// 所有可选标签
const allTags = ref([
  '免费验光',
  '品牌集合',
  '24小时',
  '周末营业',
  '专家坐诊',
  '儿童区',
  '成人专区',
  '隐形眼镜',
  '角度塑形镜',
  'RGP',
  '青少年近视防控',
  '成人视力矫正',
  '老花镜',
  '防蓝光',
  '抗疲劳',
  '变色镜片',
  '太阳镜',
  '运动眼镜',
  '渐进镜片',
  '高端镜片',
  '豪华眼镜',
  '京东联名',
  '美团联名',
  '抖音联名'
])

// 所有可选认证
const allCertifications = ref(['医学验光配镜', '青少年验光专家', '成人视力矫正中心', '儿童斜弱视防治', '近视防控示范店', '医学验光配镜'])

// 计算可选标签（排除已选标签）
const availableTags = computed(() => {
  return allTags.value.filter((tag) => !currentStoreData.value.tags?.includes(tag))
})

// 计算可选认证（排除已选认证）
const availableCertifications = computed(() => {
  return allCertifications.value.filter((cert) => !currentStoreData.value.certifications?.includes(cert))
})

// 获取省份数据
const fetchProvinces = async () => {
  try {
    const res = await getProvinces()
    provinceOptions.value = res.data
  } catch (error) {
    console.error('获取省份数据失败:', error)
  }
}

// 获取城市数据
const fetchCities = async (provinceCode) => {
  try {
    const res = await getCities(provinceCode)
    cityOptions.value = res.data
  } catch (error) {
    console.error('获取城市数据失败:', error)
  }
}

// 获取区县数据
const fetchDistricts = async (cityCode) => {
  try {
    const res = await getDistricts(cityCode)
    districtOptions.value = res.data
  } catch (error) {
    console.error('获取区县数据失败:', error)
  }
}

// 获取门店认证数据
const fetchCertifications = async () => {
  try {
    const res = await getStoreCertifications()
    if (res && res.data) {
      const options = res.data.map((item) => ({
        value: item.name,
        label: item.name
      }))
      certificationOptions.value = [{ value: '', label: '全部' }, ...options]
    }
  } catch (error) {
    console.error('获取门店认证数据失败:', error)
  }
}

// 监听省份变化
watch(
  () => searchForm.province,
  async (val) => {
    searchForm.city = ''
    searchForm.district = ''
    cityOptions.value = []
    districtOptions.value = []

    if (val) {
      await fetchCities(val)
    }
  }
)

// 监听城市变化
watch(
  () => searchForm.city,
  async (val) => {
    searchForm.district = ''
    districtOptions.value = []

    if (val && searchForm.province) {
      await fetchDistricts(val)
    }
  }
)

// 获取门店标签列表
const fetchStoreList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      name: searchForm.name,
      province: searchForm.province,
      city: searchForm.city,
      district: searchForm.district,
      certification: searchForm.certification,
      level: searchForm.level,
      type: searchForm.type
    }

    const res = await getStoreTagList(params)

    if (res && res.code === 200) {
      if (res.data && Array.isArray(res.data.list)) {
        storeList.value = res.data.list
        pagination.total = res.data.total || 0
      } else if (Array.isArray(res.data)) {
        storeList.value = res.data
        pagination.total = res.data.length
      } else {
        console.error('无法识别的数据格式:', res.data)
        ElMessage.warning('数据格式不正确，显示默认数据')
        setDefaultData()
      }
    } else {
      console.error('API返回错误状态:', res)
      ElMessage.warning('获取数据失败，显示默认数据')
      setDefaultData()
    }
  } catch (error) {
    console.error('获取门店标签列表失败:', error)
    ElMessage.error('获取门店标签列表失败，显示默认数据')
    setDefaultData()
  } finally {
    loading.value = false
  }
}

// 设置默认数据的辅助函数
const setDefaultData = () => {
  storeList.value = [
    {
      id: 'DEFAULT-001',
      name: '李白眼镜默认门店',
      areas: ['示例地区'],
      phone: '13800138000',
      certification: '青少年验配专家',
      level: 3,
      type: '旗舰店',
      tags: ['高端', '新门店']
    }
  ]
  pagination.total = 1
}

// 获取标签类型
const getTagType = (tag) => {
  switch (tag) {
    case '高端':
      return 'primary'
    case '旗舰店':
      return 'success'
    case '新门店':
    case '新品店':
      return 'warning'
    case '热门':
      return 'danger'
    case '科技园区':
      return 'info'
    case '折扣店':
      return ''
    default:
      return ''
  }
}

// 重置搜索条件
const handleReset = () => {
  // 确保按钮不会被禁用
  isResetDisabled.value = false

  searchForm.province = ''
  searchForm.city = ''
  searchForm.district = ''
  searchForm.name = ''
  searchForm.certification = ''
  searchForm.level = ''
  searchForm.type = ''
  pagination.page = 1

  cityOptions.value = []
  districtOptions.value = []

  fetchStoreList()

  // 强制移除焦点
  document.activeElement.blur()
}

// 查询
const handleSearch = () => {
  pagination.page = 1
  fetchStoreList()
}

// 查看详情
const handleView = (row) => {
  ElMessage.info(`查看门店: ${row.name}`)
  // 此处可以实现查看详情的逻辑
}

// 修改
const handleEdit = (row) => {
  // 确保crown有值，默认为金皇冠
  const crown = row.crown || '金'

  currentStoreData.value = {
    id: row.id,
    name: row.name,
    phone: row.phone,
    areas: row.areas || [],
    level: row.level ? row.level.toString() : '1', // 确保是字符串，默认为1星
    crown: crown,
    type: row.type || '李白智能版合作门店',
    tags: [...(row.tags || [])],
    certifications: row.certification ? [row.certification] : []
  }

  console.log('编辑门店数据:', currentStoreData.value)
  showEditForm.value = true
}

// 取消编辑
const handleCancelEdit = () => {
  showEditForm.value = false
}

// 处理表单提交
const handleSubmitTagForm = async (formData) => {
  try {
    // 准备提交数据
    const submitData = {
      id: formData.id,
      level: formData.level,
      crown: formData.crown,
      type: formData.type,
      tags: formData.tags,
      certifications: formData.certifications
    }

    console.log('提交门店数据:', submitData)
    await updateStoreTag(submitData)
    ElMessage.success('更新门店标签成功')
    showEditForm.value = false

    // 刷新列表
    fetchStoreList()
  } catch (error) {
    console.error('更新门店标签失败:', error)
    ElMessage.error('更新门店标签失败')
  }
}

// 分页大小变化
const handleSizeChange = (val) => {
  pagination.pageSize = val
  fetchStoreList()
}

// 页码变化
const handleCurrentChange = (val) => {
  pagination.page = val
  fetchStoreList()
}

// 处理标签删除
const handleTagDeleted = (tagName) => {
  // 从全局标签列表中删除
  const tagIndex = allTags.value.indexOf(tagName)
  if (tagIndex > -1) {
    allTags.value.splice(tagIndex, 1)
  }
}

// 处理认证删除
const handleCertificationDeleted = (certName) => {
  // 从全局认证列表中删除
  const certIndex = allCertifications.value.indexOf(certName)
  if (certIndex > -1) {
    allCertifications.value.splice(certIndex, 1)
  }
}

// 初始化
onMounted(async () => {
  // 获取省份数据
  await fetchProvinces()

  // 获取门店认证类型数据
  await fetchCertifications()

  // 获取门店标签列表
  fetchStoreList()

  // 添加全局点击事件，确保重置按钮在任何点击后都能恢复正常
  document.addEventListener('click', () => {
    isResetDisabled.value = false
  })
})

// 组件卸载时移除事件监听
onBeforeUnmount(() => {
  document.removeEventListener('click', () => {
    isResetDisabled.value = false
  })
})
</script>

<style lang="scss" scoped>
.store-tag-page {
  max-width: 1280px;
  margin: 0 auto;
  min-height: 500px;
  padding-bottom: 20px;

  .page-header {
    margin-bottom: 24px;

    h2 {
      font-size: 24px;
      font-weight: 600;
      margin: 0;
      padding: 0;
      color: #1f2937;
    }

    p {
      font-size: 14px;
      color: #6b7280;
      margin: 5px 0 0;
      padding: 0;
    }
  }

  .filter-card {
    margin-bottom: 24px;
    border-radius: 8px;

    :deep(.el-card__body) {
      padding: 20px;
      overflow: hidden;
    }
  }

  .store-card {
    margin-bottom: 24px;
    border-radius: 8px;

    :deep(.el-card__body) {
      padding: 0;
      overflow: hidden;
    }

    :deep(.el-table) {
      border-radius: 8px;
      overflow: hidden;
    }

    :deep(.el-table th) {
      background-color: #fafbfc;
      color: #89929e;
      font-weight: 600;
      height: 50px;
    }

    :deep(.el-table td) {
      padding: 12px 0;
    }

    :deep(.el-table__row) {
      height: 62px;
    }
  }

  .filter-row {
    display: flex;
    align-items: flex-end;
    width: 100%;
    gap: 14px;
    flex-wrap: wrap;
    margin-bottom: 14px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .filter-item {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 200px;
  }

  .filter-label {
    font-size: 14px;
    color: #86909c;
    margin-bottom: 4px;
  }

  .filter-select,
  .el-input {
    width: 100%;

    :deep(.el-input__inner) {
      height: 42px;
      border-radius: 6px;
    }
  }

  // 搜索栏按钮样式已移至全局样式，此处无需重复定义

  .text-secondary {
    color: #86909c;
  }

  .store-name {
    font-weight: 500 !important;
    color: #000000 !important;
    font-size: 12px !important;
  }

  .certification-tag-wrapper {
    display: flex;
  }

  .certification-tag {
    background-color: #e9eeff;
    color: #165dff;
    font-size: 12px;
    padding: 0 8px;
    height: 24px;
    line-height: 24px;
    border-radius: 15px;
    display: inline-block;
  }

  .star-rating {
    display: flex;
    align-items: center;

    :deep(.el-rate) {
      .el-rate__icon {
        margin-right: 0; /* 移除星星之间的间距 */
      }
    }
  }

  .type-tag-wrapper {
    display: flex;
  }

  .type-tag {
    background-color: #e9eeff;
    color: #165dff;
    font-size: 12px;
    padding: 0 8px;
    height: 24px;
    line-height: 24px;
    border-radius: 15px;
    display: inline-block;
  }

  .store-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;

    .store-tag {
      margin-right: 0;
      font-size: 12px;
      height: 24px;
      line-height: 24px;
      border-radius: 15px;
    }
  }

  .action-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
  }

  .table-action-btn {
    height: 32px;
    padding: 0 12px;
    display: inline-flex;
    align-items: center;
    margin: 0 4px;
    border-radius: 4px;
    border: none;
    flex-shrink: 0;
    width: 80px;

    &.view-btn {
      background-color: #e5f7e9;
      color: #00b42a;

      &:hover {
        background-color: #c7ecd1 !important;
        color: #00b42a !important;
      }
    }

    &.edit-btn {
      background-color: #e9eeff;
      color: #165dff;

      &:hover {
        background-color: #d0dbff !important;
        color: #165dff !important;
      }
    }

    .el-icon {
      margin-right: 4px;
    }
  }

  .pagination-container {
    padding: 20px;
    display: flex;
    justify-content: flex-end;
  }
}

// 添加渐入渐出过渡效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
  transform: translateY(0);
}

// 分页样式已封装到SimplePagination组件中
</style>
