<template>
  <div class="view-account-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2>账号详情</h2>
      <p>查看账号详细信息</p>
    </div>

    <!-- 表单卡片 -->
    <el-card class="form-card" shadow="never">
      <div class="account-form">
        <!-- 基本信息 -->
        <div class="form-row">
          <div class="form-field">
            <label class="field-label">账号</label>
            <el-input v-model="accountData.username" disabled />
          </div>

          <div class="form-field">
            <label class="field-label">姓名</label>
            <el-input v-model="accountData.realName" disabled />
          </div>

          <div class="form-field">
            <label class="field-label">手机号</label>
            <el-input v-model="accountData.phone" disabled />
          </div>
        </div>

        <!-- 账号权限 -->
        <div class="permissions-section">
          <label class="field-label">账号权限</label>
          <div class="permission-options">
            <div
              v-for="permission in permissionOptions"
              :key="permission.value"
              :class="[
                'permission-option',
                { selected: accountData.permissions.includes(permission.value) },
              ]"
            >
              {{ permission.label }}
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <CustomBtn type="blue" @click="handleBack">返回</CustomBtn>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useRouter, useRoute } from 'vue-router';
import CustomBtn from '@/components/custom-btn.vue';

const router = useRouter();
const route = useRoute();
const loading = ref(false);

// 账号数据
const accountData = reactive({
  username: '',
  realName: '',
  phone: '',
  permissions: [],
});

// 权限选项
const permissionOptions = [
  { label: '管理员权限', value: '管理员权限' },
  { label: '普通权限', value: '普通权限' },
  { label: '访客权限', value: '访客权限' },
  { label: '财务权限', value: '财务权限' },
];

// 获取账号详情
const fetchAccountDetail = async () => {
  const accountId = route.params.id;
  if (!accountId) {
    ElMessage.error('账号ID不存在');
    router.push('/account/list');
    return;
  }

  loading.value = true;
  try {
    // 这里应该调用API获取账号详情
    // const res = await getAccountDetail(accountId);
    
    // 模拟API响应数据
    const mockResponse = {
      code: 200,
      data: {
        accountId: accountId,
        username: 'admin' + accountId,
        realName: '管理员' + accountId,
        phone: '********' + accountId.toString().padStart(3, '0'),
        permissions: ['管理员权限', '财务权限'],
      }
    };

    if (mockResponse.code === 200) {
      const { username, realName, phone, permissions } = mockResponse.data;
      accountData.username = username;
      accountData.realName = realName;
      accountData.phone = phone;
      accountData.permissions = permissions || [];
    } else {
      ElMessage.error('获取账号详情失败');
      router.push('/account/list');
    }
  } catch (error) {
    console.error('获取账号详情出错:', error);
    ElMessage.error('获取账号详情失败');
    router.push('/account/list');
  } finally {
    loading.value = false;
  }
};

// 返回列表页
const handleBack = () => {
  router.push('/account/list');
};

// 页面加载时获取数据
onMounted(() => {
  fetchAccountDetail();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables.scss' as *;

.view-account-page {
  padding: $spacing-xl;
  background-color: $bg-color;
  max-width: 1280px;
  margin: 0 auto;

  .page-header {
    margin-bottom: $spacing-xl;

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: $text-color-dark;
      margin-bottom: $spacing-xs;
    }

    p {
      font-size: 14px;
      color: $text-color-light;
    }
  }

  .form-card {
    border-radius: 10px;
    width: 100%;

    .account-form {
      .form-row {
        display: flex;
        gap: 20px;
        margin-bottom: 24px;

        .form-field {
          flex: 1;

          .field-label {
            display: block;
            font-size: 14px;
            color: #86909c;
            margin-bottom: 8px;
            font-weight: normal;
          }
        }
      }

      .permissions-section {
        margin-bottom: 30px;

        .field-label {
          display: block;
          font-size: 14px;
          color: #86909c;
          margin-bottom: 12px;
          font-weight: normal;
        }

        .permission-options {
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          gap: 12px;

          .permission-option {
            padding: 8px 16px;
            border-radius: 12px;
            font-size: 14px;
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
            background-color: #f9fafb;
            color: #6b7280;
            cursor: default;

            &.selected {
              border-color: transparent;

              // 管理员权限 - 绿色
              &:nth-child(1) {
                background-color: #e8f9e8;
                color: #00b42b;
              }

              // 普通权限 - 蓝色
              &:nth-child(2) {
                background-color: #e9eeff;
                color: #165dff;
              }

              // 访客权限 - 橙色
              &:nth-child(3) {
                background-color: #fff7e6;
                color: #ff7d00;
              }

              // 财务权限 - 青色
              &:nth-child(4) {
                background-color: #e8fffe;
                color: #00c9c1;
              }
            }
          }
        }
      }

      .form-actions {
        display: flex;
        gap: 12px;
        padding-top: 20px;
        border-top: 1px solid #f0f0f0;
      }
    }
  }
}

// Element Plus 样式覆盖
:deep(.el-card__body) {
  padding: $spacing-xl;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  padding: 12px 16px;
  background-color: #f5f7fa;
}

:deep(.el-input__inner) {
  font-size: 14px;
  color: #606266;
}

:deep(.el-input.is-disabled .el-input__wrapper) {
  box-shadow: 0 0 0 1px #e4e7ed inset;
}
</style> 