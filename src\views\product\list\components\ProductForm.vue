<template>
  <div>
    <div class="page-header">
      <h2>{{ isEdit ? '编辑商品' : '新增商品' }}</h2>
      <p>{{ isEdit ? '编辑商品信息' : '添加新的商品信息' }}</p>
    </div>

    <el-card class="form-card" shadow="never">
      <el-form :model="formData" ref="formRef" :rules="formRules">
        <!-- 一级分类、二级分类、三级分类 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">一级分类</div>
            <el-form-item prop="level1" class="no-label-form-item">
              <el-select v-model="formData.level1" placeholder="请选择一级分类" class="form-select" @change="handleLevel1Change">
                <el-option v-for="item in level1Options" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="form-item">
            <div class="filter-label">二级分类</div>
            <el-form-item prop="level2" class="no-label-form-item">
              <el-select v-model="formData.level2" placeholder="请选择二级分类" class="form-select" @change="handleLevel2Change" :disabled="!formData.level1">
                <el-option v-for="item in level2Options" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="form-item">
            <div class="filter-label">三级分类</div>
            <el-form-item prop="level3" class="no-label-form-item">
              <el-select v-model="formData.level3" placeholder="请选择三级分类" class="form-select" :disabled="!formData.level2">
                <el-option v-for="item in level3Options" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 关联门店和是否上架在一行 -->
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">关联门店</div>
            <el-form-item prop="stores" class="no-label-form-item">
              <el-select v-model="formData.stores" placeholder="请选择关联门店" multiple collapse-tags filterable class="form-select">
                <el-option v-for="item in storeOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="form-item">
            <div class="filter-label">是否上架</div>
            <div class="horizontal-container">
              <div class="switch-container">
                <el-switch v-model="formData.status" :active-value="1" :inactive-value="0" style="--el-switch-on-color: #165dff"></el-switch>
                <span class="switch-label">是否上架</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 商品ID（仅编辑模式显示） -->
        <div v-if="isEdit" class="form-row">
          <div class="form-item">
            <div class="filter-label">商品ID</div>
            <el-form-item prop="id" class="no-label-form-item">
              <el-input v-model="formData.id" placeholder="请输入商品ID" />
            </el-form-item>
          </div>
        </div>

        <!-- 商品名称（占满整行） -->
        <div class="form-row">
          <div class="form-item full-width">
            <div class="filter-label">商品名称</div>
            <el-form-item prop="name" class="no-label-form-item">
              <el-input v-model="formData.name" placeholder="请输入商品名称" />
            </el-form-item>
          </div>
        </div>

        <!-- 移除了规格配置部分 -->

        <!-- 商品主图 -->
        <div class="form-section">
          <div class="section-title">商品列表图（1-6张）</div>
          <el-form-item prop="mainImages" class="no-label-form-item">
            <div class="upload-container">
              <div class="image-upload-grid">
                <!-- 已上传的图片 -->
                <div v-for="(image, index) in formData.mainImages" :key="index" class="image-preview-wrapper">
                  <el-image :src="image" fit="cover" class="preview-image" :preview-src-list="[image]" :initial-index="0" preview-teleported />
                  <div class="close-button" @click.stop="removeMainImage(index)">
                    <el-icon><Close /></el-icon>
                  </div>
                </div>
                <!-- 上传框 -->
                <el-upload v-if="!formData.mainImages || formData.mainImages.length < 6" class="upload-box" drag action="#" :http-request="uploadMainImage" :show-file-list="false" :before-upload="beforeUpload" accept=".jpg,.jpeg,.png,.gif">
                  <div class="upload-content">
                    <el-icon class="upload-icon"><UploadFilled /></el-icon>
                    <div class="upload-text">点击上传图片</div>
                    <div class="upload-tip">支持JPG、PNG格式</div>
                  </div>
                </el-upload>
              </div>
            </div>
          </el-form-item>
        </div>

        <!-- 商品详情图 -->
        <div class="form-section">
          <div class="section-title">商品主图（1张）</div>
          <el-form-item prop="detailImage" class="no-label-form-item">
            <div class="upload-container">
              <div v-if="formData.detailImage" class="image-preview-wrapper">
                <el-image :src="formData.detailImage" fit="cover" class="preview-image" :preview-src-list="[formData.detailImage]" :initial-index="0" preview-teleported />
                <div class="close-button" @click.stop="removeDetailImage">
                  <el-icon><Close /></el-icon>
                </div>
              </div>
              <el-upload v-else class="upload-box" drag action="#" :http-request="uploadDetailImage" :show-file-list="false" :before-upload="beforeUpload" accept=".jpg,.jpeg,.png,.gif">
                <div class="upload-content">
                  <el-icon class="upload-icon"><UploadFilled /></el-icon>
                  <div class="upload-text">点击上传图片</div>
                  <div class="upload-tip">支持JPG、PNG格式</div>
                </div>
              </el-upload>
            </div>
          </el-form-item>
        </div>

        <!-- 商品详情 -->
        <div class="form-section">
          <div class="section-title">商品详情</div>
          <el-form-item prop="detail" class="no-label-form-item">
            <div class="editor-container">
              <Toolbar style="border-bottom: 1px solid #cccccc" :editor="editorRef" :defaultConfig="editorConfig" mode="default" />
              <Editor style="height: 300px; overflow-y: hidden; background-color: #ffffff" v-model="editorContent" :defaultConfig="editorConfig" mode="default" @onCreated="handleCreated" />
            </div>
          </el-form-item>
        </div>

        <!-- 按钮区域 -->
        <div class="form-actions">
          <el-button @click="handleCancel" plain class="reset-button">
            <el-icon><Close /></el-icon>取消
          </el-button>
          <el-button type="primary" @click="handleSubmit" class="search-button">
            <el-icon><Check /></el-icon>保存
          </el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, shallowRef } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete, Close, Check, UploadFilled } from '@element-plus/icons-vue'
import { getProductCategories, getSecondLevelCategories, getThirdLevelCategories, addProduct } from '@/api/product'
import { getStoreList } from '@/api/store'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import '@wangeditor/editor/dist/css/style.css'

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false
  },
  editData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['cancel', 'submit'])

// 表单数据
const formRef = ref(null)
const formData = reactive({
  level1: '',
  level2: '',
  level3: '',
  stores: [],
  status: 1,
  id: '',
  name: '',
  mainImages: [],
  detailImage: '',
  detail: '',
  price: 0,
  stock: 0
})

// 表单验证规则
const formRules = {
  level1: [{ required: true, message: '请选择一级分类', trigger: 'change' }],
  level2: [{ required: true, message: '请选择二级分类', trigger: 'change' }],
  level3: [{ required: true, message: '请选择三级分类', trigger: 'change' }],
  stores: [{ required: true, message: '请选择关联门店', trigger: 'change' }],
  name: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
  id: [{ required: () => props.isEdit, message: '请输入商品ID', trigger: 'blur' }],
  mainImages: [
    {
      required: true,
      message: '请上传至少一张商品主图',
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (!value || value.length === 0) {
          return callback(new Error('请上传至少一张商品主图'))
        }
        callback()
      }
    }
  ]
}

// 分类选项
const level1Options = ref([])
const level2Options = ref([])
const level3Options = ref([])
const storeOptions = ref([])

// 富文本编辑器相关
const editorRef = shallowRef()
const editorContent = ref('')
const editorConfig = {
  placeholder: '请输入商品详情...',
  MENU_CONF: {}
}

// 处理编辑器创建
const handleCreated = (editor) => {
  editorRef.value = editor
}

// 获取一级分类
const fetchLevel1Categories = async () => {
  try {
    const res = await getProductCategories()
    level1Options.value = res.data
  } catch (error) {
    console.error('获取一级分类失败:', error)
  }
}

// 获取二级分类
const fetchLevel2Categories = async (parentId) => {
  try {
    const res = await getSecondLevelCategories(parentId)
    level2Options.value = res.data
  } catch (error) {
    console.error('获取二级分类失败:', error)
  }
}

// 获取三级分类
const fetchLevel3Categories = async (parentId) => {
  try {
    const res = await getThirdLevelCategories(parentId)
    level3Options.value = res.data
  } catch (error) {
    console.error('获取三级分类失败:', error)
  }
}

// 获取门店列表
const fetchStoreList = async () => {
  try {
    const res = await getStoreList({ pageSize: 100 })
    storeOptions.value = res.data.list.map((store) => ({
      id: store.id,
      name: store.name
    }))
  } catch (error) {
    console.error('获取门店列表失败:', error)
  }
}

// 一级分类变化处理
const handleLevel1Change = async (val) => {
  formData.level2 = ''
  formData.level3 = ''
  level2Options.value = []
  level3Options.value = []

  if (val) {
    await fetchLevel2Categories(val)
  }
}

// 二级分类变化处理
const handleLevel2Change = async (val) => {
  formData.level3 = ''
  level3Options.value = []

  if (val) {
    await fetchLevel3Categories(val)
  }
}

// 上传前检查
const beforeUpload = (file) => {
  // 检查文件格式
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
  const isValidType = allowedTypes.includes(file.type)

  if (!isValidType) {
    ElMessage.error('只支持JPG、PNG、JPEG、GIF格式的图片!')
    return false
  }

  // 检查文件大小（2MB以内）
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }

  return true
}

// 上传主图
const uploadMainImage = (options) => {
  const reader = new FileReader()
  reader.readAsDataURL(options.file)
  reader.onload = (e) => {
    if (!formData.mainImages) formData.mainImages = []
    formData.mainImages.push(e.target.result)
  }
}

// 移除主图
const removeMainImage = (index) => {
  ElMessageBox.confirm('确定要删除这张图片吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      formData.mainImages.splice(index, 1)
      ElMessage.success('图片已删除')
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 上传详情图
const uploadDetailImage = (options) => {
  const reader = new FileReader()
  reader.readAsDataURL(options.file)
  reader.onload = (e) => {
    formData.detailImage = e.target.result
  }
}

// 移除详情图
const removeDetailImage = () => {
  ElMessageBox.confirm('确定要删除这张图片吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      formData.detailImage = ''
      ElMessage.success('图片已删除')
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
}

// 处理提交
const handleSubmit = async () => {
  // 表单验证
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 获取富文本内容
    if (editorRef.value) {
      formData.detail = editorRef.value.getHtml()
    }

    // 准备提交数据
    const submitData = {
      ...formData,
      category: {
        level1: level1Options.value.find((item) => item.id === formData.level1),
        level2: level2Options.value.find((item) => item.id === formData.level2),
        level3: level3Options.value.find((item) => item.id === formData.level3)
      }
    }

    // 执行提交
    if (props.isEdit) {
      // 更新商品逻辑
    } else {
      // 新增商品逻辑
      await addProduct(submitData)
    }

    ElMessage.success(props.isEdit ? '更新商品成功' : '添加商品成功')
    emit('submit', submitData)
  } catch (error) {
    console.error('表单验证失败:', error)
    return false
  }
}

onMounted(async () => {
  // 获取一级分类
  await fetchLevel1Categories()

  // 获取门店列表
  await fetchStoreList()

  // 如果是编辑模式，加载编辑数据
  if (props.isEdit && props.editData) {
    // 处理编辑数据逻辑
    const { level1, level2, level3, stores, status, id, name, mainImages, detailImage, detail } = props.editData

    // 填充表单数据
    Object.assign(formData, {
      level1,
      level2,
      level3,
      stores,
      status,
      id,
      name,
      mainImages: [...(mainImages || [])],
      detailImage,
      detail
    })

    // 加载二级、三级分类
    if (level1) await fetchLevel2Categories(level1)
    if (level2) await fetchLevel3Categories(level2)

    // 设置富文本内容
    if (editorRef.value && detail) {
      editorRef.value.setHtml(detail)
    }
  }
})

onBeforeUnmount(() => {
  // 销毁编辑器
  if (editorRef.value) {
    editorRef.value.destroy()
  }
})
</script>

<style lang="scss" scoped>
.page-header {
  margin-bottom: 24px;
  h2 {
    font-size: 24px;
    margin: 0 0 8px 0;
    font-weight: 600;
  }
  p {
    color: #86909c;
    margin: 0;
  }
}

.form-card {
  margin-bottom: 24px;
}

.form-section {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #86909c;
}

.form-row {
  display: flex;
  gap: 14px;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-item {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.filter-label {
  font-size: 14px;
  color: #86909c;
  margin-bottom: 4px;
}

.form-select {
  width: 100%;
}

.custom-select-container {
  position: relative;

  :deep(.el-select) {
    width: 100%;
  }

  :deep(.el-input__wrapper) {
    box-sizing: border-box;
    height: auto !important;
    min-height: 42px;
    padding: 5px 30px 5px 10px;
  }

  :deep(.el-select__tags) {
    position: static !important;
    max-width: 100% !important;
    width: 100%;
    flex-wrap: wrap;
    display: flex;
    align-items: center;
  }

  :deep(.el-tag) {
    margin: 2px;
  }

  :deep(.el-select__input) {
    margin-left: 0;
  }
}

.no-label-form-item {
  :deep(.el-form-item__label) {
    display: none;
  }

  :deep(.el-form-item__error) {
    padding-top: 4px;
  }
}

.horizontal-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 42px;
}

.switch-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
  height: 100%;
}

.switch-label {
  font-size: 14px;
  color: #606266;
}

.upload-container {
  width: 100%;
}

.image-upload-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.image-preview-wrapper {
  width: 192px;
  height: 108px;
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.close-button {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 20px;
  height: 20px;
  background-color: #f53f3f;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #d93026;
  }

  .el-icon {
    color: white;
    font-size: 12px;
  }
}

.upload-box {
  width: 192px;
  height: 108px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 2px dashed #d0d7de; /* 虚线边框 */
  transition: border-color 0.3s ease;

  &:hover {
    border-color: #165dff; /* 悬停时蓝色边框 */
  }

  :deep(.el-upload-dragger) {
    border: none;
    background-color: transparent;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    cursor: pointer;
  }
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.upload-icon {
  font-size: 36px;
  color: #c0c4cc;
  margin-bottom: 12px;
}

.upload-text {
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
  line-height: 1.4;

  em {
    color: #165dff;
    font-style: normal;
  }
}

.upload-tip {
  color: #9ca3af;
  font-size: 12px;
  line-height: 1.4;
  margin-top: 4px;
}

.upload-content-wrapper {
  padding: 0 10px;
  display: flex;
  flex-wrap: wrap;
  height: 42px;
  align-items: center;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.image-item {
  width: 120px;
  height: 120px;
  position: relative;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  overflow: hidden;

  &:hover .image-actions {
    opacity: 1;
  }
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-actions {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s;
}

.upload-button {
  margin-right: 10px;
  background-color: #efefef;
  color: #000000;
  border-color: #efefef;

  &:hover {
    background-color: #e5e7eb;
    border-color: #e5e7eb;
    color: #000000;
  }
}

.upload-status {
  color: #9ca3af;
}

.upload-placeholder {
  width: 120px;
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;

  &:hover {
    border-color: #409eff;
  }

  .el-icon {
    font-size: 24px;
    color: #8c939d;
    margin-bottom: 8px;
  }

  .upload-tip {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }
}

.editor-container {
  border: 1px solid #ccc;
  border-radius: 6px;
  overflow: hidden;

  // 设置富文本编辑器内容的字体大小
  :deep(.w-e-text-container [data-slate-editor] p) {
    font-size: 18px;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
}

.reset-button {
  background-color: #f2f3f5;
  border-color: #f2f3f5;
  color: #4e5969;
  height: 42px;
  padding: 8px 16px;
  font-size: 16px;

  &:hover {
    background-color: #e5e7eb;
    border-color: #e5e7eb;
  }

  .el-icon {
    margin-right: 4px;
  }
}

.search-button {
  background-color: #165dff;
  border-color: #165dff;
  height: 42px;
  padding: 8px 16px;
  font-size: 16px;
  margin-left: 12px;

  &:hover {
    background-color: #4080ff;
    border-color: #4080ff;
  }

  .el-icon {
    margin-right: 4px;
  }
}

.form-select {
  width: 100%;
}

:deep(.form-select .el-select__wrapper) {
  min-height: 42px;
  align-items: center;
}

/* 全宽字段样式 */
.full-width {
  width: 100% !important;
}

/* 新增的图片上传样式 - 与活动表单保持一致 */
.image-preview-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 10px;
}

.image-preview-wrapper {
  width: 192px;
  height: 108px; /* 16:9 比例，60%尺寸 */
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.close-button {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 20px;
  height: 20px;
  background-color: #f53f3f; /* 红色背景 */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease; /* 只保留背景色过渡 */

  &:hover {
    background-color: #d93026; /* 悬停时稍微深一点的红色 */
  }

  .el-icon {
    color: white;
    font-size: 12px;
  }
}

/* 更新上传区域样式 */
.upload-container .upload-box {
  width: 192px;
  height: 108px; /* 16:9 比例，60%尺寸 */
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-bottom: 10px; /* 与预览图片保持一致的下边距 */
  border: 2px dashed #d0d7de; /* 虚线边框 */
  transition: border-color 0.3s ease;
  padding: 0;

  &:hover {
    border-color: #165dff; /* 悬停时蓝色边框 */
  }

  :deep(.el-upload-dragger) {
    border: none;
    background-color: transparent;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    cursor: pointer;
  }
}

.upload-container .upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: auto;
}

.upload-container .upload-icon {
  font-size: 36px;
  color: #c0c4cc;
  margin-bottom: 12px;
}

.upload-container .upload-text {
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
  line-height: 1.4;

  em {
    color: #165dff;
    font-style: normal;
  }
}

.upload-container .upload-tip {
  color: #9ca3af;
  font-size: 12px;
  line-height: 1.4;
  margin-top: 4px;
}

/* 更新容器样式 */
.upload-container {
  width: auto;
  border: none;
  height: auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
</style>
