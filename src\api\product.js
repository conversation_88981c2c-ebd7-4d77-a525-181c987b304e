import request from '@/utils/request'

// 获取产品列表
export function getProductList(params) {
  return request({
    url: '/mock/product/list',
    method: 'get',
    params
  })
}

// 获取产品详情
export function getProductDetail(id) {
  return request({
    url: `/product/detail/${id}`,
    method: 'get'
  })
}

// 添加产品
export function addProduct(data) {
  return request({
    url: '/product/add',
    method: 'post',
    data
  })
}

// 更新产品
export function updateProduct(data) {
  return request({
    url: '/product/update',
    method: 'put',
    data
  })
}

// 删除产品
export function deleteProduct(id) {
  return request({
    url: `/product/delete/${id}`,
    method: 'delete'
  })
}

// 切换产品上架状态
export function toggleProductStatus(id, status) {
  return request({
    url: `/product/toggle-status`,
    method: 'put',
    data: { id, status }
  })
}

// 获取产品分类
export function getProductCategories() {
  return request({
    url: '/product/categories',
    method: 'get'
  })
}

// 获取二级分类
export function getSecondLevelCategories(parentId) {
  return request({
    url: `/product/categories/second/${parentId}`,
    method: 'get'
  })
}

// 获取三级分类
export function getThirdLevelCategories(parentId) {
  return request({
    url: `/product/categories/third/${parentId}`,
    method: 'get'
  })
}

// 获取商品分类树结构
export function getProductCategoryTree() {
  return request({
    url: '/mock/product/category/tree',
    method: 'get'
  })
}

// 添加商品分类
export function addProductCategory(data) {
  return request({
    url: '/mock/product/category',
    method: 'post',
    data
  })
}

// 更新商品分类
export function updateProductCategory(id, data) {
  return request({
    url: `/mock/product/category/${id}`,
    method: 'put',
    data
  })
}

// 删除商品分类
export function deleteProductCategory(id) {
  return request({
    url: `/mock/product/category/${id}`,
    method: 'delete'
  })
}
