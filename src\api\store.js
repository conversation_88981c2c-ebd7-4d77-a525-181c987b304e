import request from '@/utils/request'

// 获取门店列表
export function getStoreList(params) {
  return request({
    url: '/mock/stores',
    method: 'get',
    params
  })
}

// 获取可用于置顶的门店列表
export function getAvailableStores(params) {
  return request({
    url: '/mock/stores/available',
    method: 'get',
    params
  })
}

// 获取门店详情
export function getStoreDetail(id) {
  return request({
    url: `/api/stores/${id}`,
    method: 'get'
  })
}

// 添加门店置顶
export function addStoreTop(data) {
  return request({
    url: '/mock/store/top',
    method: 'post',
    data
  })
}

// 更新门店置顶
export function updateStoreTop(id, data) {
  return request({
    url: `/api/store/top/${id}`,
    method: 'put',
    data
  })
}

// 删除门店置顶
export function deleteStoreTop(id) {
  return request({
    url: `/api/store/top/${id}`,
    method: 'delete'
  })
}

// 获取门店置顶列表
export function getStoreTopList(params) {
  return request({
    url: '/mock/stores/top',
    method: 'get',
    params
  })
}

// 获取省份列表
export function getProvinces() {
  return request({
    url: '/mock/provinces',
    method: 'get'
  })
}

// 获取城市列表
export function getCities(province) {
  return request({
    url: '/mock/cities',
    method: 'get',
    params: { province }
  })
}

// 获取区县列表
export function getDistricts(city) {
  return request({
    url: '/mock/districts',
    method: 'get',
    params: { city }
  })
}

// 获取门店审核列表
export function getStoreAuditList(params) {
  return request({
    url: '/mock/stores/audit',
    method: 'get',
    params
  })
}

// 获取门店审核详情
export function getStoreAuditDetail(id) {
  return request({
    url: `/api/store/audit/${id}`,
    method: 'get'
  })
}

// 审核通过门店
export function approveStoreAudit(id, comment = '') {
  return request({
    url: `/mock/store/audit/approve/${id}`,
    method: 'put',
    data: { comment }
  })
}

// 审核驳回门店
export function rejectStoreAudit(id, comment = '') {
  return request({
    url: `/mock/store/audit/reject/${id}`,
    method: 'put',
    data: { comment }
  })
}

// 获取门店类型标签列表
export function getStoreTagList(params) {
  return request({
    url: '/mock/stores/tag',
    method: 'get',
    params
  })
}

// 获取门店认证类型列表
export function getStoreCertifications() {
  return request({
    url: '/mock/store/certifications',
    method: 'get'
  })
}

// 获取门店标签详情
export function getStoreTagDetail(id) {
  return request({
    url: `/api/store/tag/${id}`,
    method: 'get'
  })
}

// 添加新门店
export function addStore(data) {
  return request({
    url: '/mock/store',
    method: 'post',
    data
  })
}

// 更新门店信息
export function updateStore(id, data) {
  return request({
    url: `/mock/store/${id}`,
    method: 'put',
    data
  })
}

// 删除门店
export function deleteStore(id) {
  return request({
    url: `/mock/store/${id}`,
    method: 'delete'
  })
}

// 更新门店标签
export function updateStoreTag(data) {
  return request({
    url: '/mock/store/updateTag',
    method: 'post',
    data
  })
}

// 删除门店标签
export function deleteStoreTag(tagName) {
  return request({
    url: '/mock/store/tag/delete',
    method: 'post',
    data: { tagName }
  })
}

// 删除门店认证
export function deleteStoreCertification(certName) {
  return request({
    url: '/mock/store/certification/delete',
    method: 'post',
    data: { certName }
  })
}

// 提交门店表单
export function submitStoreForm(data) {
  return request({
    url: '/mock/store/submit',
    method: 'post',
    data
  })
}

// 保存门店草稿
export function saveStoreDraft(data) {
  return request({
    url: '/mock/store/draft',
    method: 'post',
    data
  })
}

// 获取门店草稿列表
export function getStoreDrafts(params) {
  return request({
    url: '/mock/store/drafts',
    method: 'get',
    params
  })
}

// 获取门店详情（包括草稿）
export function getStoreFormDetail(id) {
  return request({
    url: `/api/store/detail/${id}`,
    method: 'get'
  })
}

// 更新门店信息
export function updateStoreForm(id, data) {
  return request({
    url: `/api/store/update/${id}`,
    method: 'put',
    data
  })
}
