<template>
  <div class="activate-detail-page">
    <div class="page-header">
      <h2>激活详情</h2>
      <p>查看激活记录的详细信息</p>
    </div>

    <el-card class="detail-card" shadow="never" v-loading="loading">
      <div class="detail-content" v-if="detailData">
        <div class="detail-row">
          <div class="detail-item">
            <div class="detail-label">微信昵称</div>
            <div class="detail-value">{{ detailData.wechatNickname }}</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">上传时间</div>
            <div class="detail-value">{{ detailData.createTime }}</div>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <div class="detail-label">防伪码</div>
            <div class="detail-value">{{ detailData.antiCounterfeitCode }}</div>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item full-width">
            <div class="detail-label">凭证图片</div>
            <div class="detail-value">
              <div class="image-gallery">
                <el-image
                  v-for="(image, index) in detailData.verificationImages"
                  :key="index"
                  :src="image"
                  :preview-src-list="detailData.verificationImages"
                  :initial-index="index"
                  class="gallery-image"
                  fit="cover"
                />
              </div>
            </div>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item full-width">
            <div class="detail-label">备注说明</div>
            <div class="detail-value">{{ detailData.description }}</div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div
          class="action-buttons"
          v-if="detailData.approvalStatus === 'pending'"
        >
          <CustomBtn type="grey" @click="handleCancel" />
          <CustomBtn
            style="
              width: 120px;
              height: 40px;
              font-size: 16px;
              color: #fff !important;
              background-color: #00b42b !important;
              margin: 0 0;
            "
            type="green"
            @click="handleApproval('approved')"
            >审批通过</CustomBtn
          >
          <CustomBtn
            style="
              width: 120px;
              height: 40px;
              font-size: 16px;
              color: #fff !important;
              background-color: #f53f3f !important;
              margin: 0 0;
            "
            type="red"
            @click="handleApproval('rejected')"
            >审批驳回</CustomBtn
          >
        </div>

        <!-- 已审批状态显示 -->
        <div class="approval-status" v-else>
          <!-- 审批信息区域 -->
          <div class="approval-header">
            <h3>审批信息</h3>
          </div>

          <div class="approval-details">
            <div class="approval-row">
              <div class="approval-item">
                <div class="approval-label">审批人</div>
                <div class="approval-value">
                  {{ detailData.approver || '李四' }}
                </div>
              </div>
              <div class="approval-item">
                <div class="approval-label">审批结果</div>
                <div class="approval-value">
                  <el-tag
                    :type="
                      detailData.approvalStatus === 'approved'
                        ? 'success'
                        : 'danger'
                    "
                    size="large"
                  >
                    {{
                      detailData.approvalStatus === 'approved' ? '通过' : '驳回'
                    }}
                  </el-tag>
                </div>
              </div>
            </div>

            <div class="approval-row">
              <div class="approval-item full-width">
                <div class="approval-label">审批意见</div>
                <div class="approval-value">
                  {{
                    detailData.approvalReason ||
                    '产品符合防伪标准，准予通过激活。'
                  }}
                </div>
              </div>
            </div>

            <div class="approval-row">
              <div class="approval-item">
                <div class="approval-label">审批时间</div>
                <div class="approval-value">
                  {{ detailData.updateTime || '2025-01-02 10:30:00' }}
                </div>
              </div>
            </div>
          </div>

          <!-- 返回按钮 -->
          <div class="return-button">
            <el-button type="primary" @click="handleCancel">
              <el-icon><ArrowLeft /></el-icon>
              返回列表
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 审批模态框 -->
    <el-dialog
      v-model="approvalDialogVisible"
      :title="dialogTitle"
      width="500px"
      :before-close="handleDialogClose"
    >
      <div class="approval-form">
        <div class="form-item">
          <div class="form-value">
            <div
              style="margin-bottom: 0px; padding-top: 5px"
              class="form-label"
            >
              审批结果：
            </div>
            <!-- 这里不做选择 ，禁用选择交互 -->
            <div style="margin-left: 15px">
              <el-radio-group v-model="approvalForm.result">
                <el-radio disabled label="approved">
                  <el-icon><CircleCheck /></el-icon>
                  通过
                </el-radio>
                <el-radio disabled label="rejected">
                  <el-icon><CircleClose /></el-icon>
                  驳回
                </el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>

        <div class="form-item">
          <div class="form-label">审批意见：</div>
          <div class="form-value">
            <el-input
              v-model="approvalForm.reason"
              type="textarea"
              :rows="4"
              placeholder="请输入..."
              maxlength="200"
              show-word-limit
            />
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button
            style="width: 120px; background-color: #b5b5b5; color: #fff"
            @click="handleDialogClose"
            >取消</el-button
          >
          <el-button
            style="width: 120px; background-color: #3662ec; color: #fff"
            @click="handleSubmitApproval"
            >确认</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { CircleCheck, CircleClose, ArrowLeft } from '@element-plus/icons-vue';
import { getActivateDetail, updateActivateStatus } from '@/api/service';
import CustomBtn from '@/components/custom-btn.vue';

const route = useRoute();
const router = useRouter();

// 数据
const loading = ref(false);
const detailData = ref(null);
const approvalDialogVisible = ref(false);
const dialogTitle = ref('审批操作');

// 审批表单
const approvalForm = ref({
  result: 'approved',
  reason: '',
});

// 获取详情数据
const fetchDetail = async () => {
  loading.value = true;
  try {
    const id = route.params.id;
    const response = await getActivateDetail(id);
    detailData.value = response.data;
    loading.value = false;
  } catch (error) {
    console.error('获取详情失败:', error);
    ElMessage.error('获取详情失败');
    loading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  router.back();
};

// 处理审批
const handleApproval = async (type) => {
  // 首先重置表单
  approvalForm.value = {
    result: type,
    reason: '',
  };
  // 设置对话框标题
  dialogTitle.value = type === 'approved' ? '审批通过' : '审批驳回';
  // 确保数据更新后再显示模态框
  await nextTick();
  // 显示模态框
  approvalDialogVisible.value = true;
  // 再次确认数据状态
  await nextTick();
};

// 关闭对话框
const handleDialogClose = () => {
  approvalDialogVisible.value = false;
  // 不重置表单，保持用户选择的状态
};

// 提交审批
const handleSubmitApproval = async () => {
  if (!approvalForm.value.reason.trim()) {
    ElMessage.warning('请输入审批意见');
    return;
  }

  try {
    await updateActivateStatus({
      id: detailData.value.id,
      status: approvalForm.value.result,
      reason: approvalForm.value.reason,
    });

    ElMessage.success(
      approvalForm.value.result === 'approved'
        ? '审批通过成功'
        : '审批驳回成功',
    );
    approvalDialogVisible.value = false;

    // 更新本地数据
    detailData.value.approvalStatus = approvalForm.value.result;
    detailData.value.updateTime = new Date().toLocaleString('zh-CN');
    detailData.value.approver = '管理员';
    detailData.value.approvalReason = approvalForm.value.reason;
    if (approvalForm.value.result === 'rejected') {
      detailData.value.rejectReason = approvalForm.value.reason;
    }

    // 延迟返回列表页
    setTimeout(() => {
      router.back();
    }, 1500);
  } catch (error) {
    console.error('审批失败:', error);
    ElMessage.error('审批失败');
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchDetail();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

.activate-detail-page {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: $spacing-xl;
}

.page-header {
  margin-bottom: $spacing-xl;

  h2 {
    margin: 0 0 $spacing-sm 0;
    color: $text-color-dark;
    font-size: 24px;
    font-weight: 600;
  }

  p {
    margin: 0;
    color: $text-color-light;
    font-size: 14px;
  }
}

.detail-card {
  border-radius: $border-radius;

  .detail-content {
    padding: $spacing-xl;
  }
}

.detail-row {
  display: flex;
  margin-bottom: $spacing-xl;
  gap: $spacing-xxl;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-item {
  flex: 1;

  &.full-width {
    flex: none;
    width: 100%;
  }

  .detail-label {
    font-size: 14px;
    color: $text-color-light;
    margin-bottom: $spacing-sm;
    font-weight: 500;
  }

  .detail-value {
    font-size: 16px;
    color: $text-color-dark;
    line-height: 1.5;
  }
}

.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: $spacing-md;

  .gallery-image {
    width: 120px;
    height: 120px;
    border-radius: $border-radius-sm;
    border: 1px solid $border-color;
    cursor: pointer;

    &:hover {
      border-color: $primary-color;
    }
  }
}

.action-buttons {
  display: flex;
  justify-content: flex-start;
  gap: $spacing-lg;
  margin-top: $spacing-xxl;
  padding-top: $spacing-xl;
  border-top: 1px solid $border-color;
}

.approval-status {
  margin-top: $spacing-xxl;
  padding-top: $spacing-xl;
  border-top: 1px solid $border-color;

  .approval-header {
    margin-bottom: $spacing-xl;

    h3 {
      margin: 0 0 $spacing-sm 0;
      color: $text-color-dark;
      font-size: 20px;
      font-weight: 600;
    }
  }

  .approval-details {
    .approval-row {
      display: flex;
      margin-bottom: $spacing-xl;
      gap: $spacing-xxl;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .approval-item {
      flex: 1;

      &.full-width {
        flex: none;
        width: 100%;
      }

      .approval-label {
        font-size: 14px;
        color: $text-color-light;
        margin-bottom: $spacing-sm;
        font-weight: 500;
      }

      .approval-value {
        font-size: 16px;
        color: $text-color-dark;
        line-height: 1.5;

        .el-tag {
          font-weight: 500;
        }
      }
    }
  }

  .return-button {
    margin-top: $spacing-xxl;
    text-align: left;

    .el-button {
      display: inline-flex;
      align-items: center;
      gap: $spacing-sm;
    }
  }
}

.approval-form {
  .form-item {
    margin-bottom: $spacing-xl;

    .form-label {
      font-size: 14px;
      color: $text-color-dark;
      margin-bottom: $spacing-md;
      font-weight: 500;
    }

    .form-value {
      display: flex;
      flex-direction: row;
      :deep(.el-radio) {
        margin-right: $spacing-xl;

        .el-radio__label {
          display: flex;
          align-items: center;
          gap: $spacing-sm;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: $spacing-md;
}

// 响应式设计
@media (max-width: 768px) {
  .detail-row {
    flex-direction: column;
    gap: $spacing-lg;
  }

  .image-gallery {
    .gallery-image {
      width: 100px;
      height: 100px;
    }
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
}
</style>
