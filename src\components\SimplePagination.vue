<!--
简洁分页组件
特性：
- 圆形按钮设计
- 平滑过渡动画（0.3s ease）
- 支持v-model双向绑定
- 可配置页面大小和总数
- 仅在有数据时显示

使用示例：
<SimplePagination
  v-model="currentPage"
  :page-size="10"
  :total="100"
  @change="handlePageChange"
/>
-->
<template>
  <div class="simple-pagination" v-if="total > 0">
    <el-pagination v-model:current-page="currentPage" :page-size="pageSize" layout="prev, pager, next" :total="total" @current-change="handleCurrentChange" :background="background" :small="small" />
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props定义
const props = defineProps({
  // 当前页码
  modelValue: {
    type: Number,
    default: 1
  },
  // 每页显示条数
  pageSize: {
    type: Number,
    default: 10
  },
  // 总条数
  total: {
    type: Number,
    default: 0
  },
  // 是否显示背景
  background: {
    type: Boolean,
    default: true
  },
  // 是否小尺寸
  small: {
    type: Boolean,
    default: true
  }
})

// 事件定义
const emit = defineEmits(['update:modelValue', 'change'])

// 当前页码的计算属性
const currentPage = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})

// 页码变化处理
const handleCurrentChange = (page) => {
  emit('change', page)
}
</script>

<style lang="scss" scoped>
.simple-pagination {
  margin-top: 24px;
  display: flex;
  justify-content: center;

  :deep(.el-pagination) {
    .el-pagination__prev,
    .el-pagination__next {
      background-color: transparent;
      border: none;
      color: #8d96a1;
      border-radius: 50%;
      min-width: 32px;
      height: 32px;
      transition: all 0.3s ease;

      &:hover {
        color: #ffffff;
        background-color: #165dff;
      }

      &.is-disabled {
        color: #c0c4cc;
        cursor: not-allowed;

        &:hover {
          color: #c0c4cc;
          background-color: transparent;
        }
      }
    }

    .el-pager li {
      background-color: transparent;
      border: none;
      color: #8d96a1;
      margin: 0 4px;
      min-width: 32px;
      height: 32px;
      line-height: 32px;
      border-radius: 50%;
      font-weight: 400;
      transition: all 0.3s ease;

      &:hover {
        color: #ffffff;
        background-color: #165dff;
      }

      &.is-active {
        background-color: #165dff;
        color: #ffffff;
        transition: all 0.3s ease;

        &:hover {
          background-color: #165dff;
          color: #ffffff;
        }
      }
    }
  }
}
</style>
