@use './variables' as *;

// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

// 全局输入框placeholder样式
input::placeholder,
textarea::placeholder,
.el-input__inner::placeholder,
.el-textarea__inner::placeholder {
  color: $placeholder-color !important;
}

.el-input__wrapper .el-input__inner::placeholder,
.el-select .el-input__inner::placeholder {
  color: $placeholder-color !important;
}

// 全局输入框文本颜色样式
input,
textarea,
.el-input__inner,
.el-textarea__inner,
.el-input .el-input__inner,
.el-select .el-input__inner,
.el-select-dropdown__item,
.el-select-dropdown__item.selected {
  color: $text-color !important;
}

.el-input__inner,
.el-textarea__inner,
.el-select .el-input__inner,
.el-form-item.is-success .el-input__inner,
.el-form-item.is-success .el-textarea__inner,
.el-form-item.is-success .el-input__inner,
.el-form-item.is-success .el-textarea__inner {
  color: $text-color !important;
}

// 下拉菜单项样式
.el-select-dropdown__item {
  color: $text-color !important;
}

.el-select-dropdown__item.selected {
  color: $query-btn-color !important;
  font-weight: bold;
}

:root {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html,
body {
  height: 100%;
  font-size: 14px;
  color: $text-color;
  background-color: $bg-color;
  min-height: 100vh;
  width: 100%;
}

// 链接样式
a {
  font-weight: 500;
  color: $primary-color;
  text-decoration: inherit;

  &:hover {
    color: $primary-hover;
  }
}

// 标题样式
h1 {
  font-size: 2em;
  line-height: 1.1;
}

// 应用容器
#app {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
}

// 卡片样式
.card-container {
  background-color: #fff;
  border-radius: $border-radius;
  padding: $spacing-xl;
  box-shadow: $box-shadow;
  margin-bottom: $spacing-lg;
}

// 按钮样式
.btn-reset {
  background-color: $reset-btn-color !important;
  border-radius: $border-radius-btn !important;
  height: $btn-height !important;
  padding: $btn-padding !important;
  font-size: $btn-font-size !important;

  &:hover {
    background-color: $reset-btn-hover-color !important;
    border-color: transparent !important;
  }
}

.btn-query {
  background-color: $query-btn-color !important;
  border-radius: $border-radius-btn !important;
  height: $btn-height !important;
  padding: $btn-padding !important;
  font-size: $btn-font-size !important;
}

.btn-add {
  background-color: $add-btn-color !important;
  border-radius: $border-radius-btn !important;
  height: $btn-height !important;
  padding: $btn-padding !important;
  font-size: $btn-font-size !important;
}

// 应用按钮统一圆角样式
.el-button {
  border-radius: $border-radius-btn !important;
}

// 搜索栏中的按钮样式
.search-actions {
  .el-button {
    height: $btn-height !important;
    padding: $btn-padding !important;
    font-size: $btn-font-size !important;

    // 除第一个按钮外都添加左边距
    & + .el-button {
      margin-left: $btn-margin !important;
    }

    // 图标与文字间距
    .el-icon + span {
      margin-left: $btn-icon-margin !important;
    }
  }
}

// 应用卡片统一圆角样式
.el-card {
  border-radius: $border-radius !important;
  overflow: hidden;
}

// 应用输入框统一样式
.el-input__wrapper {
  border-radius: $border-radius-sm !important;
  height: $input-height !important;

  // 聚焦时的边框颜色
  &.is-focus {
    box-shadow: 0 0 0 1px $input-focus-color inset !important;
  }
}

// 搜索栏样式
.search-container {
  margin-bottom: $spacing-lg;

  .search-form {
    display: flex;
    flex-wrap: wrap;
    gap: $search-spacing-vertical $search-spacing-horizontal;
    padding: $search-padding;
  }

  .search-item {
    display: flex;
    flex-direction: column;
  }

  .search-label {
    font-size: $search-label-size;
    color: $search-label-color;
    margin-bottom: $search-label-margin;
  }

  .search-actions {
    display: flex;
    align-items: flex-end;
    gap: $spacing-md;
  }
}

// 表格容器
.table-container {
  margin-top: $spacing-lg;
}

// 分页容器
.pagination-container {
  margin-top: $spacing-xl;
  display: flex;
  justify-content: flex-end;
}

// 搜索栏按钮统一样式
.filter-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-left: auto;

  .el-button {
    height: $btn-height;
    padding: $btn-padding;
    border-radius: $border-radius-btn;
    font-size: $btn-font-size;
    border: none;

    .el-icon {
      margin-right: $btn-icon-margin;
    }
  }

  // 移除所有按钮的hover、focus边框和阴影
  :deep(.el-button:hover) {
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
  }

  // 重置按钮样式
  .reset-button {
    background-color: $reset-btn-color;
    color: $reset-btn-text-color;
    border: none;

    &:hover {
      background-color: $reset-btn-hover-color !important;
      color: $reset-btn-text-color !important;
      border: none !important;
    }
  }

  // 查询按钮样式
  .search-button {
    background-color: $query-btn-color;
    margin-left: 12px;

    &:hover {
      background-color: $primary-hover !important;
    }
  }

  // 新增按钮样式
  .add-button {
    background-color: $add-btn-color;
    margin-left: 12px;

    &:hover {
      background-color: $success-color !important;
    }
  }

  // 特殊类型按钮的hover效果
  :deep(.el-button--primary:hover) {
    background-color: $primary-hover !important;
  }

  :deep(.el-button--success:hover) {
    background-color: $success-color !important;
  }
}

// 全局分页禁用按钮背景色修复
.el-pagination.is-background .btn-next.is-disabled,
.el-pagination.is-background .btn-next:disabled,
.el-pagination.is-background .btn-prev.is-disabled,
.el-pagination.is-background .btn-prev:disabled,
.el-pagination.is-background .el-pager li.is-disabled,
.el-pagination.is-background .el-pager li:disabled {
  background-color: transparent !important;
}
