<template>
  <div class="role-add-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>新增角色</h2>
      <p>配置新角色的信息和权限</p>
    </div>

    <!-- 表单卡片 -->
    <el-card class="form-card" shadow="never">
      <el-form
        :model="form"
        :rules="rules"
        ref="formRef"
        label-width="120px"
        class="role-form"
      >
        <!-- 角色名称 -->
        <el-form-item label="角色名称" prop="roleName">
          <el-input
            v-model="form.roleName"
            placeholder="请输入角色名称"
            clearable
            style="width: 400px"
          />
        </el-form-item>

        <!-- 功能菜单权限 -->
        <el-form-item label="功能菜单权限" prop="permissions">
          <div class="permission-tree">
            <el-tree
              ref="treeRef"
              :data="permissionTree"
              :props="treeProps"
              show-checkbox
              node-key="id"
              :default-expanded-keys="expandedKeys"
              :check-strictly="false"
              class="permission-tree-content"
            />
          </div>
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <div class="form-actions">
            <CustomBtn type="reset" @click="handleCancel">取消</CustomBtn>
            <CustomBtn type="save" @click="handleSave">保存</CustomBtn>
          </div>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { createRole, getPermissionMenu } from '@/api/subaccount';
import CustomBtn from '@/components/custom-btn.vue';

const router = useRouter();
const formRef = ref();
const treeRef = ref();

// 表单数据
const form = reactive({
  roleName: '',
  permissions: [],
});

// 表单验证规则
const rules = {
  roleName: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    {
      min: 2,
      max: 20,
      message: '角色名称长度在 2 到 20 个字符',
      trigger: 'blur',
    },
  ],
};

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'label',
};

// 默认展开的节点
const expandedKeys = ref(['home', 'user', 'activity', 'product']);

// 权限树数据
const permissionTree = ref([]);

// 获取权限菜单
const fetchPermissionMenu = async () => {
  try {
    const res = await getPermissionMenu();
    if (res.code === 200) {
      permissionTree.value = res.data;
    } else {
      ElMessage.error(res.message || '获取权限菜单失败');
    }
  } catch (error) {
    console.error('获取权限菜单出错:', error);
    ElMessage.error('获取权限菜单失败');
  }
};

// 保存
const handleSave = async () => {
  // 表单验证
  const valid = await formRef.value.validate().catch(() => false);
  if (!valid) return;

  // 获取选中的权限
  const checkedKeys = treeRef.value.getCheckedKeys();
  const halfCheckedKeys = treeRef.value.getHalfCheckedKeys();
  const allCheckedKeys = [...checkedKeys, ...halfCheckedKeys];

  if (allCheckedKeys.length === 0) {
    ElMessage.warning('请至少选择一个权限');
    return;
  }

  try {
    const res = await createRole({
      roleName: form.roleName,
      permissions: allCheckedKeys,
      description: `拥有${allCheckedKeys.length}项权限的角色`,
    });

    if (res.code === 200) {
      ElMessage.success('创建角色成功');
      router.push('/account/roles');
    } else {
      ElMessage.error(res.message || '创建角色失败');
    }
  } catch (error) {
    console.error('创建角色出错:', error);
    ElMessage.error('创建角色失败');
  }
};

// 取消
const handleCancel = () => {
  router.push('/account/roles');
};

// 页面加载时获取权限菜单
onMounted(() => {
  fetchPermissionMenu();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables.scss' as *;

.role-add-page {
  padding: $spacing-xl;
  background-color: $bg-color;
  max-width: 1280px;
  margin: 0 auto;

  .page-header {
    margin-bottom: $spacing-xl;

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: $text-color-dark;
      margin-bottom: $spacing-xs;
    }

    p {
      font-size: 14px;
      color: $text-color-light;
    }
  }

  .form-card {
    border-radius: 10px;

    .role-form {
      

      .permission-tree {
        width: 100%;
        border: 1px solid #dcdfe6;
        border-radius: 6px;
        padding: 16px;
        background-color: #fff;

        .permission-tree-content {
          width: 100%;

          :deep(.el-tree-node) {
            margin-bottom: 8px;

            .el-tree-node__content {
              height: 32px;
              padding: 0 8px;
              border-radius: 4px;

              &:hover {
                background-color: #f5f7fa;
              }
            }

            .el-tree-node__label {
              font-size: 14px;
              color: $text-color;
            }

            .el-checkbox {
              margin-right: 8px;
            }
          }

          :deep(.el-tree-node__children) {
            padding-left: 24px;
          }
        }
      }

      .form-actions {
        display: flex;
        // gap: 16px;
        padding-top: 20px;
      }
    }
  }
}

// Element Plus 样式覆盖
:deep(.el-card__body) {
  padding: $spacing-xl;
}

:deep(.el-form-item__label) {
  font-size: 14px;
  color: $text-color;
  font-weight: 500;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-button) {
  border-radius: 6px;
}
</style>
