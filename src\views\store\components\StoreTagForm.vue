<template>
  <div>
    <div class="page-header">
      <h2>修改门店类型标签</h2>
      <p>修改指定门店的类型标签</p>
    </div>
    <el-card class="form-card" shadow="never">
      <!-- 门店基本信息 -->
      <div class="form-section">
        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">所属区域</div>
            <div class="info-text-plain">{{ storeData.areas ? storeData.areas.join(', ') : '' }}</div>
          </div>
          <div class="form-item">
            <div class="filter-label">门店名称</div>
            <div class="info-text-plain">{{ storeData.name }}</div>
          </div>
          <div class="form-item">
            <div class="filter-label">联系电话</div>
            <div class="info-text-plain">{{ storeData.phone }}</div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-item">
            <div class="filter-label">门店星级</div>
            <el-select v-model="storeData.level" placeholder="请选择门店星级" class="form-select">
              <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>
          <div class="form-item">
            <div class="filter-label">门店类型</div>
            <el-select v-model="storeData.type" placeholder="请选择门店类型" class="form-select">
              <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>
          <div class="form-item">
            <div class="filter-label">门店皇冠等级</div>
            <el-select v-model="storeData.crown" placeholder="请选择皇冠等级" class="form-select">
              <el-option v-for="item in crownOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>
        </div>
      </div>

      <!-- 已选门店标签 -->
      <div class="form-section">
        <div class="section-title">已选门店标签</div>
        <div class="selected-tags" v-if="storeData.tags && storeData.tags.length > 0">
          <div v-for="(tag, index) in storeData.tags" :key="index" class="tag-item">
            {{ tag }}
            <el-icon class="remove-icon" @click="removeTag(tag)"><Close /></el-icon>
          </div>
        </div>
        <div v-else class="no-tags">暂无已选标签</div>
      </div>

      <!-- 可选门店标签 -->
      <div class="form-section">
        <div class="section-title">可选门店标签</div>
        <div class="available-tags">
          <div v-for="tag in filteredAvailableTags" :key="tag" class="tag-item available" @click="addTag(tag)">
            {{ tag }}
            <el-icon class="remove-icon" @click.stop="confirmDeleteTag(tag)"><Close /></el-icon>
          </div>
        </div>
        <div class="tag-input-row">
          <el-input v-model="newTag" placeholder="输入新标签，按回车键添加" @keyup.enter="addNewTag" class="tag-input"> </el-input>
          <el-button type="primary" @click="addNewTag" class="blue-btn">
            <el-icon><Plus /></el-icon>
            添加新标签
          </el-button>
        </div>
      </div>

      <!-- 已选门店认证 -->
      <div class="form-section">
        <div class="section-title">已选门店认证</div>
        <div class="selected-tags" v-if="storeData.certifications && storeData.certifications.length > 0">
          <div v-for="(cert, index) in storeData.certifications" :key="index" class="tag-item certification">
            {{ cert }}
            <el-icon class="remove-icon" @click="removeCertification(cert)"><Close /></el-icon>
          </div>
        </div>
        <div v-else class="no-tags">暂无已选认证</div>
      </div>

      <!-- 可选门店认证 -->
      <div class="form-section">
        <div class="section-title">可选门店认证</div>
        <div class="available-tags">
          <div v-for="cert in filteredAvailableCertifications" :key="cert" class="tag-item available" @click="addCertification(cert)">
            {{ cert }}
            <el-icon class="remove-icon" @click.stop="confirmDeleteCertification(cert)"><Close /></el-icon>
          </div>
        </div>
        <div class="tag-input-row">
          <el-input v-model="newCertification" placeholder="输入新认证，按回车键添加" @keyup.enter="addNewCertification" class="tag-input"> </el-input>
          <el-button type="primary" @click="addNewCertification" class="blue-btn">
            <el-icon><Plus /></el-icon>
            添加新认证
          </el-button>
        </div>
      </div>

      <!-- 按钮区域 -->
      <div class="form-actions">
        <el-button @click="handleCancel" plain class="reset-button">
          <el-icon><Close /></el-icon>取消
        </el-button>
        <el-button type="primary" @click="handleSubmit" class="search-button">
          <el-icon><Check /></el-icon>保存
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Plus, Close, Check } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { deleteStoreTag, deleteStoreCertification } from '@/api/store'

const props = defineProps({
  storeData: {
    type: Object,
    required: true
  },
  availableTags: {
    type: Array,
    default: () => []
  },
  availableCertifications: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['cancel', 'submit', 'update:storeData', 'tag-deleted', 'certification-deleted'])

const newTag = ref('')
const newCertification = ref('')

// 门店星级选项
const levelOptions = [
  { value: '1', label: '1星' },
  { value: '2', label: '2星' },
  { value: '3', label: '3星' },
  { value: '4', label: '4星' },
  { value: '5', label: '5星' }
]

// 门店类型选项
const typeOptions = [
  { value: '李白智能版合作门店', label: '李白智能版合作门店' },
  { value: '李白专业版合作门店', label: '李白专业版合作门店' }
]

// 门店皇冠等级选项
const crownOptions = [
  { value: '金', label: '金皇冠' },
  { value: '银', label: '银皇冠' },
  { value: '铜', label: '铜皇冠' }
]

// 过滤出未被选中的标签
const filteredAvailableTags = computed(() => {
  // 所有可用标签包括：原始可用标签 + 已被移除的自定义标签
  const allAvailableTags = [...props.availableTags, ...removedCustomTags.value]
  if (!props.storeData.tags) return allAvailableTags
  return allAvailableTags.filter((tag) => !props.storeData.tags.includes(tag))
})

// 过滤出未被选中的认证
const filteredAvailableCertifications = computed(() => {
  // 所有可用认证包括：原始可用认证 + 已被移除的自定义认证
  const allAvailableCertifications = [...props.availableCertifications, ...removedCustomCertifications.value]
  if (!props.storeData.certifications) return allAvailableCertifications
  return allAvailableCertifications.filter((cert) => !props.storeData.certifications.includes(cert))
})

// 存储已从已选中移除的自定义标签
const removedCustomTags = ref([])

// 存储已从已选中移除的自定义认证
const removedCustomCertifications = ref([])

// 设置默认值
onMounted(() => {
  if (!props.storeData.type) {
    emit('update:storeData', { ...props.storeData, type: '李白智能版合作门店' })
  }
})

// 添加标签
const addTag = (tag) => {
  if (!props.storeData.tags) {
    emit('update:storeData', { ...props.storeData, tags: [tag] })
  } else if (!props.storeData.tags.includes(tag)) {
    const updatedTags = [...props.storeData.tags, tag]
    emit('update:storeData', { ...props.storeData, tags: updatedTags })
  }
}

// 移除标签
const removeTag = (tag) => {
  if (props.storeData.tags) {
    const updatedTags = props.storeData.tags.filter((t) => t !== tag)
    emit('update:storeData', { ...props.storeData, tags: updatedTags })

    // 检查是否为自定义标签（不在原始可用标签列表中）
    if (!props.availableTags.includes(tag) && !removedCustomTags.value.includes(tag)) {
      removedCustomTags.value.push(tag)
    }
  }
}

// 确认删除标签
const confirmDeleteTag = (tag) => {
  ElMessageBox.confirm(`是否从系统中永久删除标签"${tag}"？删除后所有门店将不再显示此标签。`, '删除标签', {
    confirmButtonText: '确定删除',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // 调用删除标签API
      deleteStoreTag(tag)
        .then((res) => {
          if (res.code === 200) {
            ElMessage.success('删除标签成功')

            // 如果是已选标签中的，需要从已选中移除
            if (props.storeData.tags && props.storeData.tags.includes(tag)) {
              removeTag(tag)
            }

            // 通知父组件更新可用标签列表
            emit('tag-deleted', tag)
          } else {
            ElMessage.error(res.message || '删除标签失败')
          }
        })
        .catch((error) => {
          console.error('删除标签出错:', error)
          ElMessage.error('删除标签失败')
        })
    })
    .catch(() => {
      // 用户取消删除
      ElMessage.info('已取消删除')
    })
}

// 添加新标签
const addNewTag = () => {
  if (newTag.value.trim()) {
    addTag(newTag.value.trim())
    newTag.value = ''
  }
}

// 添加认证
const addCertification = (cert) => {
  if (!props.storeData.certifications) {
    emit('update:storeData', { ...props.storeData, certifications: [cert] })
  } else if (!props.storeData.certifications.includes(cert)) {
    const updatedCerts = [...props.storeData.certifications, cert]
    emit('update:storeData', { ...props.storeData, certifications: updatedCerts })
  }
}

// 移除认证
const removeCertification = (cert) => {
  if (props.storeData.certifications) {
    const updatedCerts = props.storeData.certifications.filter((c) => c !== cert)
    emit('update:storeData', { ...props.storeData, certifications: updatedCerts })

    // 检查是否为自定义认证（不在原始可用认证列表中）
    if (!props.availableCertifications.includes(cert) && !removedCustomCertifications.value.includes(cert)) {
      removedCustomCertifications.value.push(cert)
    }
  }
}

// 确认删除认证
const confirmDeleteCertification = (cert) => {
  ElMessageBox.confirm(`是否从系统中永久删除认证"${cert}"？删除后所有门店将不再显示此认证。`, '删除认证', {
    confirmButtonText: '确定删除',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      // 调用删除认证API
      deleteStoreCertification(cert)
        .then((res) => {
          if (res.code === 200) {
            ElMessage.success('删除认证成功')

            // 如果是已选认证中的，需要从已选中移除
            if (props.storeData.certifications && props.storeData.certifications.includes(cert)) {
              removeCertification(cert)
            }

            // 通知父组件更新可用认证列表
            emit('certification-deleted', cert)
          } else {
            ElMessage.error(res.message || '删除认证失败')
          }
        })
        .catch((error) => {
          console.error('删除认证出错:', error)
          ElMessage.error('删除认证失败')
        })
    })
    .catch(() => {
      // 用户取消删除
      ElMessage.info('已取消删除')
    })
}

// 添加新认证
const addNewCertification = () => {
  if (newCertification.value.trim()) {
    addCertification(newCertification.value.trim())
    newCertification.value = ''
  }
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
}

// 处理提交
const handleSubmit = () => {
  emit('submit', props.storeData)
}
</script>

<style lang="scss" scoped>
.page-header {
  margin-bottom: 24px;
  h2 {
    font-size: 24px;
    margin: 0 0 8px 0;
  }
  p {
    color: #86909c;
    margin: 0;
  }
}

.form-card {
  margin-bottom: 24px;
}

.form-section {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
}

.form-row {
  display: flex;
  gap: 14px;
  margin-bottom: 14px;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-item {
  flex: 1;
}

.filter-label {
  font-size: 14px;
  color: #86909c;
  font-weight: 500;
  margin-bottom: 4px;
}

.info-text-plain {
  font-size: 14px;
  color: #86909c;
}

.form-select {
  width: 100%;
}

.selected-tags,
.available-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 16px;
}

.tag-item {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  background-color: #e5f7ff;
  color: #165dff;
  border-radius: 30px;
  font-size: 14px;
  transition: all 0.3s ease;

  &.available {
    background-color: #f2f3f5;
    color: #86909c;
    cursor: pointer;

    &:hover {
      transform: translateY(-3px);
    }
  }

  &.certification {
    background-color: #e8f3ff;
    color: #165dff;
  }

  .remove-icon {
    margin-left: 6px;
    font-size: 12px;
    cursor: pointer;
  }
}

// 为已选标签也添加浮动效果
.selected-tags .tag-item {
  cursor: pointer;

  &:hover {
    transform: translateY(-3px);
  }
}

.no-tags {
  color: #86909c;
  height: 33px;
  line-height: 33px;
  font-size: 14px;
}

.tag-input-row {
  display: flex;
  gap: 12px;
}

.tag-input {
  flex: 1;

  :deep(.el-input__inner) {
    height: 42px;
  }
}

.blue-btn {
  background-color: #165dff;
  border-color: #165dff;
  color: #ffffff;
  height: 42px;
  padding: 8px 16px;
  font-size: 16px;

  &:hover {
    background-color: #4080ff;
    border-color: #4080ff;
  }

  .el-icon {
    margin-right: 4px;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
}

.reset-button {
  background-color: #f2f3f5;
  border-color: #f2f3f5;
  color: #4e5969;
  height: 42px;
  padding: 8px 16px;
  font-size: 16px;

  &:hover {
    background-color: #e5e7eb;
    border-color: #e5e7eb;
  }

  .el-icon {
    margin-right: 4px;
  }
}

.search-button {
  background-color: $primary-color;
  border-color: #165dff;
  height: 42px;
  padding: 8px 16px;
  font-size: 16px;

  &:hover {
    background-color: $primary-hover !important;
    border-color: #4080ff;
  }

  .el-icon {
    margin-right: 4px;
  }
}
</style>
