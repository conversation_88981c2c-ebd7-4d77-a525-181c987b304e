import js from '@eslint/js';
import globals from 'globals';
import pluginVue from 'eslint-plugin-vue';

export default [
  // 忽略目录
  {
    ignores: ['dist/**', 'node_modules/**'],
  },

  // 基础JavaScript规则
  js.configs.recommended,

  // Vue推荐规则 (flat/recommended 已经包含了大部分 Vue 相关的最佳实践)
  ...pluginVue.configs['flat/recommended'],

  {
    files: ['**/*.{js,vue}'],
    languageOptions: {
      ecmaVersion: 2022,
      sourceType: 'module',
      globals: {
        ...globals.browser,
        ...globals.node,
      },
      parserOptions: {
        // 如果你使用 TypeScript，需要将此行 uncomment 并安装 @typescript-eslint/parser
        // parser: '@typescript-eslint/parser',
        ecmaFeatures: {
          jsx: false,
        },
      },
    },
    rules: {
      // Vue特定规则 - 放宽要求 (与 Prettier 相关的保持 off)
      'vue/multi-word-component-names': 'off',
      'vue/no-unused-vars': 'warn', // 允许有未使用的变量作为警告
      'vue/component-name-in-template-casing': 'off',
      'vue/prop-name-casing': ['error', 'camelCase'],
      'vue/attribute-hyphenation': 'off',
      'vue/v-on-event-hyphenation': 'off',
      'vue/max-attributes-per-line': 'off',
      'vue/singleline-html-element-content-newline': 'off',
      'vue/multiline-html-element-content-newline': 'off',
      'vue/html-self-closing': 'off',
      'vue/html-closing-bracket-newline': 'off',
      'vue/html-indent': 'off',
      'vue/attributes-order': 'off',

      // JavaScript通用规则
      'no-unused-vars': 'warn', // 允许有未使用的变量作为警告
      'no-console': 'off', // 开发环境允许console
      'no-debugger': 'warn', // debugger 作为警告
      'prefer-const': 'warn', // 建议使用 const 作为警告
      'no-var': 'error', // 强制使用 let/const
      'no-undef': 'error', // 捕获未定义的变量，非常重要

      // 潜在错误捕获 - 建议开启
      eqeqeq: ['error', 'always'], // 强制使用 '==='
      'no-trailing-spaces': 'error', // 禁止行末空格

      // 代码风格 - 全部交给 Prettier 处理
      indent: 'off',
      quotes: 'off',
      semi: 'off',
      'comma-dangle': 'off',
      'object-curly-spacing': 'off',
      'array-bracket-spacing': 'off',
    },
  },
];
