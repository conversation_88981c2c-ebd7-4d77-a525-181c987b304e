<template>
  <div class="store-top-page">
    <transition name="fade" mode="out-in">
      <div v-if="!showAddForm" key="list">
        <div class="page-header">
          <h2>门店置顶</h2>
          <p>管理系统内所有门店的置顶信息</p>
        </div>

        <!-- 筛选区域 -->
        <el-card class="filter-card" shadow="never">
          <div class="filter-row">
            <div class="filter-item">
              <div class="filter-label">选择地区</div>
              <el-select v-model="searchForm.province" placeholder="请选择省/直辖市" clearable class="filter-select">
                <el-option v-for="item in provinceOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>

            <div class="filter-item">
              <div class="filter-label">选择城市</div>
              <el-select v-model="searchForm.city" placeholder="请选择市/区" clearable class="filter-select">
                <el-option v-for="item in cityOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>

            <div class="filter-item">
              <div class="filter-label">选择区县</div>
              <el-select v-model="searchForm.district" placeholder="请选择区/县" clearable class="filter-select">
                <el-option v-for="item in districtOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>
          </div>

          <div class="filter-row">
            <div class="filter-item">
              <div class="filter-label">门店名称</div>
              <el-input v-model="searchForm.name" placeholder="请输入门店名称" clearable prefix-icon="Search"></el-input>
            </div>

            <div class="filter-actions">
              <el-button @click="handleReset" plain class="reset-button" :disabled="isResetDisabled">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
              <el-button type="primary" @click="handleSearch" class="search-button">
                <el-icon><Search /></el-icon>
                查询
              </el-button>
              <el-button type="success" @click="showAddForm = true" class="add-button">
                <el-icon><Plus /></el-icon>
                新增
              </el-button>
            </div>
          </div>
        </el-card>

        <!-- 门店列表 -->
        <el-card class="store-card" shadow="never">
          <el-table :data="storeList" style="width: 100%" v-loading="loading" :header-cell-style="tableHeaderStyle" :cell-style="tableCellStyle">
            <el-table-column prop="id" label="门店ID" width="100">
              <template #default="scope">
                <span class="text-secondary">{{ scope.row.id }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="门店名称" min-width="320">
              <template #default="scope">
                <span class="store-name">{{ scope.row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="areas" label="所属区域" min-width="200">
              <template #default="scope">
                <div class="table-area-tags">
                  <div class="area-tags">
                    <el-tag size="small" class="area-tag" v-for="(area, index) in scope.row.areas" :key="index">
                      {{ area }}
                    </el-tag>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="updateTime" label="置顶时间" width="150">
              <template #default="scope">
                <span class="text-secondary">{{ scope.row.updateTime }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="operator" label="操作人" width="100">
              <template #default="scope">
                <span class="text-secondary">{{ scope.row.operator }}</span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="260">
              <template #default="scope">
                <div class="action-buttons">
                  <el-button size="small" @click="handleView(scope.row)" class="table-action-btn view-btn">
                    <el-icon><View /></el-icon>
                    查看
                  </el-button>
                  <el-button size="small" @click="handleEdit(scope.row)" class="table-action-btn edit-btn">
                    <el-icon><Edit /></el-icon>
                    修改
                  </el-button>
                  <el-button size="small" @click="handleDelete(scope.row)" class="table-action-btn delete-btn">
                    <el-icon><Delete /></el-icon>
                    取消
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 分页栏 - 移出卡片，采用简洁的数字分页形式 -->
        <SimplePagination v-model="pagination.page" :page-size="pagination.pageSize" :total="pagination.total" @change="handleCurrentChange" />

        <!-- 新增/编辑对话框 -->
        <el-dialog :title="dialogType === 'add' ? '新增门店置顶' : '编辑门店置顶'" v-model="dialogVisible" width="500px">
          <el-form :model="formData" :rules="formRules" ref="formRef" label-width="80px">
            <el-form-item label="门店名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入门店名称"></el-input>
            </el-form-item>

            <el-form-item label="所属区域" prop="areas">
              <el-select v-model="formData.areas" multiple placeholder="请选择所属区域" class="w-100">
                <el-option v-for="item in areaProvinceOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-form>

          <template #footer>
            <span class="dialog-footer">
              <el-button @click="dialogVisible = false">取消</el-button>
              <el-button type="primary" @click="submitForm">确定</el-button>
            </span>
          </template>
        </el-dialog>

        <!-- 查看详情对话框 -->
        <el-dialog title="门店详情" v-model="detailVisible" width="500px">
          <div class="detail-content">
            <div class="detail-item">
              <span class="detail-label">门店ID:</span>
              <span class="detail-value">{{ detailData.id }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">门店名称:</span>
              <span class="detail-value">{{ detailData.name }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">所属区域:</span>
              <div class="detail-value">
                <div class="area-tags detail-area-tags">
                  <el-tag size="small" class="area-tag" v-for="(area, index) in detailData.areas" :key="index">
                    {{ area }}
                  </el-tag>
                </div>
              </div>
            </div>
            <div class="detail-item">
              <span class="detail-label">置顶时间:</span>
              <span class="detail-value">{{ detailData.updateTime }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">操作人:</span>
              <span class="detail-value">{{ detailData.operator }}</span>
            </div>
          </div>

          <template #footer>
            <span class="dialog-footer">
              <el-button @click="detailVisible = false">关闭</el-button>
            </span>
          </template>
        </el-dialog>
      </div>

      <StoreTopForm v-else key="add-form" :initial-selected-stores="selectedStores" :is-edit-mode="isEditMode" :store-to-edit="storeToEdit" @cancel="handleCancelAdd" @submit="handleStoreTopFormSubmit" @update:selectedStores="updateSelectedStores" />
    </transition>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, View, Edit, Delete, Plus, Refresh } from '@element-plus/icons-vue'
import { getStoreList, getStoreTopList, getProvinces, getCities, getDistricts, addStoreTop, updateStoreTop, deleteStoreTop } from '@/api/store'
import StoreTopForm from '@/views/store/components/StoreTopForm.vue'
import SimplePagination from '@/components/SimplePagination.vue'

// 搜索表单
const searchForm = reactive({
  province: '',
  city: '',
  district: '',
  name: ''
})

// 省份选项
const provinceOptions = ref([])

// 城市选项
const cityOptions = ref([])

// 区县选项
const districtOptions = ref([])

// 门店列表
const storeList = ref([])

// 加载状态
const loading = ref(false)

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 5,
  total: 0
})

// 表格表头样式
const tableHeaderStyle = {
  backgroundColor: '#FAFBFC',
  color: '#89929E',
  fontSize: '14px',
  fontWeight: '600',
  height: '50px'
}

// 已选择表格表头样式
const selectedTableHeaderStyle = {
  backgroundColor: '#FAFBFC',
  color: '#89929E',
  fontSize: '14px',
  fontWeight: '600',
  height: '50px'
}

// 表格单元格样式
const tableCellStyle = {
  padding: '12px 0',
  fontSize: '14px'
}

// 是否重置按钮禁用状态
const isResetDisabled = ref(false)

// 对话框相关
const dialogVisible = ref(false)
const dialogType = ref('add') // 'add' 或 'edit'
const formRef = ref(null)
const formData = reactive({
  id: '',
  name: '',
  areas: []
})

// 用于选择区域的引用
const areaProvinceOptions = ref([])

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入门店名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  areas: [
    { required: true, message: '请选择所属区域', trigger: 'change' },
    { type: 'array', min: 1, message: '至少选择一个区域', trigger: 'change' }
  ]
}

// 详情对话框相关
const detailVisible = ref(false)
const detailData = reactive({
  id: '',
  name: '',
  areas: [],
  updateTime: '',
  operator: ''
})

// 新增表单相关
const showAddForm = ref(false)
const selectedStores = ref([])
const isEditMode = ref(false) // 添加编辑模式标识
const storeToEdit = ref(null) // 添加要编辑的门店数据

// 获取省份数据
const fetchProvinces = async () => {
  try {
    const res = await getProvinces()
    provinceOptions.value = res.data
  } catch (error) {
    console.error('获取省份数据失败:', error)
  }
}

// 获取城市数据
const fetchCities = async (provinceCode) => {
  try {
    const res = await getCities(provinceCode)
    cityOptions.value = res.data
  } catch (error) {
    console.error('获取城市数据失败:', error)
  }
}

// 获取区县数据
const fetchDistricts = async (cityCode) => {
  try {
    const res = await getDistricts(cityCode)
    districtOptions.value = res.data
  } catch (error) {
    console.error('获取区县数据失败:', error)
  }
}

// 监听省份变化
watch(
  () => searchForm.province,
  async (val) => {
    searchForm.city = ''
    searchForm.district = ''
    cityOptions.value = []
    districtOptions.value = []

    if (val) {
      await fetchCities(val)
    }
  }
)

// 监听城市变化
watch(
  () => searchForm.city,
  async (val) => {
    searchForm.district = ''
    districtOptions.value = []

    if (val && searchForm.province) {
      await fetchDistricts(val)
    }
  }
)

// 获取门店列表
const fetchStoreList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      name: searchForm.name,
      province: searchForm.province,
      city: searchForm.city,
      district: searchForm.district
    }

    console.log('发送请求参数:', params)

    const res = await getStoreTopList(params)

    console.log('API响应完整数据:', res)
    console.log('API响应状态码:', res?.code)
    console.log('API响应数据类型:', typeof res?.data)

    if (res?.data) {
      if (Array.isArray(res.data)) {
        console.log('数据是数组, 长度:', res.data.length)
        if (res.data.length > 0) {
          console.log('第一项数据:', res.data[0])
          console.log('第一项数据字段:', Object.keys(res.data[0]))
        }
      } else if (typeof res.data === 'object') {
        console.log('数据是对象, 字段:', Object.keys(res.data))
        if (res.data.list) {
          console.log('list字段类型:', typeof res.data.list)
          if (Array.isArray(res.data.list)) {
            console.log('list是数组, 长度:', res.data.list.length)
            if (res.data.list.length > 0) {
              console.log('list第一项:', res.data.list[0])
              console.log('list第一项字段:', Object.keys(res.data.list[0]))
            }
          }
        }
      }
    }

    if (res && res.code === 200) {
      if (res.data && Array.isArray(res.data.list)) {
        storeList.value = res.data.list
        pagination.total = res.data.total || 0
        console.log('门店列表数据(data.list):', storeList.value)
      } else if (Array.isArray(res.data)) {
        storeList.value = res.data
        pagination.total = res.data.length
        console.log('门店列表数据(data数组):', storeList.value)
      } else {
        console.error('无法识别的数据格式:', res.data)
        ElMessage.warning('数据格式不正确，显示默认数据')
        setDefaultData()
      }
    } else {
      console.error('API返回错误状态:', res)
      ElMessage.warning('获取数据失败，显示默认数据')
      setDefaultData()
    }
  } catch (error) {
    console.error('获取门店列表失败, 错误详情:', error)
    ElMessage.error('获取门店列表失败，显示默认数据')
    setDefaultData()
  } finally {
    loading.value = false
  }
}

// 设置默认数据的辅助函数
const setDefaultData = () => {
  storeList.value = [
    {
      id: 'DEFAULT-001',
      name: '李白眼镜默认门店',
      areas: ['示例地区'],
      updateTime: new Date()
        .toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
        .replace(/\//g, '-'),
      operator: '系统管理员'
    }
  ]
  pagination.total = 1
}

// 重置搜索条件
const handleReset = () => {
  // 确保按钮不会被禁用
  isResetDisabled.value = false

  searchForm.province = ''
  searchForm.city = ''
  searchForm.district = ''
  searchForm.name = ''
  pagination.page = 1

  cityOptions.value = []
  districtOptions.value = []

  fetchStoreList()

  // 强制移除焦点
  document.activeElement.blur()
}

// 查询
const handleSearch = () => {
  pagination.page = 1
  fetchStoreList()
}

// 新增
const handleAdd = () => {
  dialogType.value = 'add'
  formData.id = ''
  formData.name = ''
  formData.areas = []
  dialogVisible.value = true
}

// 查看
const handleView = (row) => {
  detailData.id = row.id
  detailData.name = row.name
  detailData.areas = row.areas
  detailData.updateTime = row.updateTime
  detailData.operator = row.operator
  detailVisible.value = true
}

// 编辑
const handleEdit = (row) => {
  // 设置编辑模式和数据
  isEditMode.value = true
  storeToEdit.value = row

  // 准备要编辑的门店数据
  const storeData = {
    id: row.id,
    name: row.name,
    areas: row.areas || []
    // 添加其他可能需要的字段
  }

  // 将选中的门店放入selectedStores
  selectedStores.value = [storeData]

  // 显示编辑表单
  showAddForm.value = true
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认取消门店 "${row.name}" 的置顶信息吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        await deleteStoreTop(row.id)
        ElMessage.success('取消成功')
        fetchStoreList()

        // 添加延时处理，确保DOM更新后重置所有按钮的hover状态
        setTimeout(() => {
          // 移除所有可能存在的hover状态
          document.querySelectorAll('.table-action-btn').forEach((btn) => {
            btn.blur()
            // 移除可能的hover相关类
            btn.classList.remove('hover')
            btn.classList.remove('is-hover')
          })
        }, 10)
      } catch (error) {
        console.error('取消失败:', error)
        ElMessage.error('取消失败')
      }
    })
    .catch(() => {})
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (dialogType.value === 'add') {
          await addStoreTop(formData)
          ElMessage.success('添加成功')
        } else {
          await updateStoreTop(formData.id, formData)
          ElMessage.success('更新成功')
        }

        dialogVisible.value = false
        fetchStoreList()
      } catch (error) {
        console.error('提交失败:', error)
        ElMessage.error('提交失败')
      }
    } else {
      return false
    }
  })
}

// 分页大小变化
const handleSizeChange = (val) => {
  pagination.pageSize = val
  fetchStoreList()
}

// 页码变化
const handleCurrentChange = (val) => {
  pagination.page = val
  fetchStoreList()
}

// 处理StoreTopForm组件的提交事件
const handleStoreTopFormSubmit = async (formData) => {
  // 使用提交的门店数据
  console.log('提交的门店数据:', formData)
  console.log('选中的门店:', formData.stores)

  try {
    if (isEditMode.value && storeToEdit.value) {
      // 编辑模式：更新现有门店
      if (formData.stores && formData.stores.length > 0) {
        await updateStoreTop(storeToEdit.value.id, {
          name: formData.stores[0].name,
          areas: formData.stores[0].areas || []
        })
        ElMessage.success('门店置顶修改成功')
      } else {
        throw new Error('没有选择门店')
      }
    } else {
      // 新增模式：添加新的置顶门店
      if (!formData.stores || formData.stores.length === 0) {
        ElMessage.warning('请至少选择一个门店')
        return
      }

      // 确保提交的数据包含完整的门店信息
      const storesData = formData.stores.map((store) => ({
        id: store.id,
        name: store.name,
        areas: store.areas || []
      }))

      console.log('处理后的门店数据:', storesData)

      await addStoreTop({
        stores: storesData,
        province: formData.province,
        city: formData.city,
        district: formData.district
      })
      ElMessage.success(`成功置顶${storesData.length}个门店`)
    }

    // 隐藏表单
    showAddForm.value = false
    // 重置编辑模式
    isEditMode.value = false
    // 清空编辑数据
    storeToEdit.value = null
    // 刷新列表
    fetchStoreList()
    // 重置选中的门店
    selectedStores.value = []
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  }
}

// 处理取消添加/编辑
const handleCancelAdd = () => {
  // 隐藏表单
  showAddForm.value = false
  // 重置编辑模式
  isEditMode.value = false
  // 清空编辑数据
  storeToEdit.value = null
  // 清空选中的门店
  selectedStores.value = []
}

// 更新选中的门店列表
const updateSelectedStores = (stores) => {
  // 使用深拷贝避免引用问题
  selectedStores.value = JSON.parse(JSON.stringify(stores))
}

// 初始化
onMounted(async () => {
  // 获取省份数据
  await fetchProvinces()

  // 获取门店列表
  fetchStoreList()

  // 添加全局点击事件，确保重置按钮在任何点击后都能恢复正常
  document.addEventListener('click', () => {
    isResetDisabled.value = false
  })
})

// 组件卸载时移除事件监听
onBeforeUnmount(() => {
  document.removeEventListener('click', () => {
    isResetDisabled.value = false
  })
})
</script>

<style lang="scss" scoped>
@use 'sass:color';

.store-top-page {
  max-width: 1280px;
  margin: 0 auto;
  min-height: 500px;
  padding-bottom: 20px;
}

.page-header {
  margin-bottom: 24px;

  h2 {
    font-size: 24px;
    margin: 0;
    font-weight: 600;
    color: #1f2937;
  }

  p {
    color: #6b7280;
    margin: 5px 0 0;
  }
}

.filter-card,
.store-card {
  margin-bottom: 24px;
  border-radius: 8px;

  :deep(.el-card__body) {
    padding: 20px;
    overflow: hidden;
  }
}

.filter-row {
  display: flex;
  align-items: flex-end;
  width: 100%;
  gap: 14px;
  flex-wrap: wrap;
  margin-bottom: 14px;

  &:last-child {
    margin-bottom: 0;
  }
}

.filter-item {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 200px;
}

.filter-label {
  font-size: 14px;
  color: #86909c;
  margin-bottom: 4px;
}

.filter-select,
.el-input {
  width: 100%;

  :deep(.el-input__inner) {
    height: 42px;
    border-radius: 6px;
  }
}

// 搜索栏按钮样式已移至全局样式，此处无需重复定义

.table-area-tags {
  padding: 5px 0;
}

.area-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-width: 100%;

  .area-tag {
    margin-right: 0;
    background-color: #e9eeff !important;
    border-color: #e9eeff !important;
    color: #165dff !important;
    font-size: 12px;
    padding: 0 8px;
    height: 24px;
    line-height: 24px;
    border-radius: 15px;
    white-space: normal;
    max-width: 100%;
    margin-bottom: 4px;
    display: inline-block;
  }
}

.action-buttons {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  white-space: nowrap;
}

.table-action-btn {
  height: 32px;
  padding: 0 12px;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  margin: 0 4px;
  border-radius: 6px;
  border: none;

  &.view-btn {
    background-color: #e9eeff;
    color: #165dff;
    &:hover {
      background-color: #d0dbff;
    }
  }

  &.edit-btn {
    background-color: #fff2e9;
    color: #ff7d00;
    &:hover {
      background-color: #ffe9d9;
    }
  }

  &.delete-btn {
    background-color: #ffecec;
    color: #f53f3f;
    &:hover {
      background-color: #ffd8d8;
    }
  }

  .el-icon {
    margin-right: 4px;
  }
}

.pagination-container {
  padding: 20px;
  display: flex;
  justify-content: flex-end;
}

.detail-content {
  padding: 10px 0;
}

.detail-item {
  margin-bottom: 15px;
  display: flex;
  align-items: flex-start;
}

.detail-label {
  width: 100px;
  font-weight: 500;
  color: #606266;
}

.detail-value {
  flex: 1;
  color: #303133;
}

.w-100 {
  width: 100%;
}

.store-name {
  color: #1f2937;
  font-weight: 500;
}

.detail-area-tags {
  margin-top: 5px;
}

.text-secondary {
  color: #86909c;
}

.store-card {
  margin-bottom: 24px;
  border-radius: 8px;

  :deep(.el-card__body) {
    padding: 0;
    overflow: hidden;
  }

  :deep(.el-table) {
    border-radius: 8px;
    overflow: hidden;
  }

  :deep(.el-table th) {
    background-color: #fafbfc;
    color: #89929e;
    font-weight: 600;
    height: 50px;
  }

  :deep(.el-table td) {
    padding: 12px 0;
  }

  :deep(.el-table__row) {
    height: 62px;
  }
}

.add-top-form {
  max-width: 1280px;
  margin: 0 auto;
}

.form-card {
  margin-bottom: 24px;
  border-radius: 8px;

  :deep(.el-card__body) {
    padding: 20px;
    overflow: hidden;
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 分页样式已封装到SimplePagination组件中
</style>
