<template>
  <div class="activate-page">
    <div class="page-header">
      <h2>激活列表</h2>
      <p>管理系统内所有待激活的记录</p>
    </div>

    <!-- 搜索表单 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <el-row :gutter="14">
          <!-- 第一行 -->
          <el-col :span="8">
            <div class="search-item">
              <div class="search-label">防伪码</div>
              <el-input
                v-model="searchForm.antiCounterfeitCode"
                placeholder="请输入防伪码"
                clearable
                class="search-input"
                prefix-icon="Search"
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="search-item">
              <div class="search-label">微信昵称</div>
              <el-input
                v-model="searchForm.wechatNickname"
                placeholder="请输入微信昵称"
                clearable
                class="search-input"
                prefix-icon="Search"
              />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="search-item">
              <div class="search-label">审批状态</div>
              <el-select
                v-model="searchForm.approvalStatus"
                placeholder="全部"
                clearable
                class="search-input"
              >
                <!-- 采用插槽来放置ICON -->
                <template #prefix>
                  <el-icon color="#86909C" size="16"><Filter /></el-icon>
                </template>
                <el-option label="全部" value="" />
                <el-option label="待审批" value="pending" />
                <el-option label="审批通过" value="approved" />
                <el-option label="审批驳回" value="rejected" />
              </el-select>
            </div>
          </el-col>
        </el-row>

        <!-- 操作按钮 -->
        <div class="search-actions">
          <CustomBtn type="reset" @click="handleReset" />
          <CustomBtn type="blue" @click="handleQuery" />
        </div>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        :data="tableData"
        v-loading="loading"
        style="width: 100%"
        :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column prop="wechatNickname" label="微信昵称" width="120" />
        <el-table-column
          prop="antiCounterfeitCode"
          label="防伪码"
          width="140"
        />
        <el-table-column prop="verificationImage" label="凭证照片" width="120">
          <template #default="{ row }">
            <el-image
              style="width: 60px; height: 60px; border-radius: 8px"
              :src="row.verificationImage"
              :preview-src-list="[row.verificationImage]"
              fit="cover"
              preview-teleported
            />
          </template>
        </el-table-column>
        <el-table-column
          prop="description"
          label="备注说明"
          width="200"
          show-overflow-tooltip
        />
        <el-table-column prop="approvalStatus" label="审批状态" width="120">
          <template #default="{ row }">
            <span
              :class="['custom-status-tag', `status-${row.approvalStatus}`]"
            >
              {{ getStatusText(row.approvalStatus) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          min-width="280"
          header-align="right"
          align="right"
        >
          <template #default="{ row }">
            <CustomBtn type="delete" @click="handleDelete(row)" />
            <CustomBtn
              style="margin-right: 2px"
              type="check"
              @click="handleView(row)"
            />
            <CustomBtn
              style="margin: 0px 2px"
              type="green"
              v-if="row.approvalStatus === 'pending'"
              @click="handleApprove(row)"
            />
            <CustomBtn
              style="margin: 0px 2px"
              type="red"
              v-if="row.approvalStatus === 'pending'"
              @click="handleReject(row)"
            />
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
    </el-card>
    <div class="pagination-container">
      <SimplePagination
        v-model="pagination.currentPage"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        @change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getActivateList, updateActivateStatus } from '@/api/service';
import CustomBtn from '@/components/custom-btn.vue';
import SimplePagination from '@/components/SimplePagination.vue';
const router = useRouter();

// 搜索表单
const searchForm = reactive({
  antiCounterfeitCode: '',
  wechatNickname: '',
  approvalStatus: '',
});

// 表格数据
const tableData = ref([]);
const loading = ref(false);

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: '待审批',
    approved: '审批通过',
    rejected: '审批驳回',
  };
  return statusMap[status] || '未知';
};

// 查询数据
const fetchData = async () => {
  loading.value = true;
  try {
    const params = {
      ...searchForm,
      page: pagination.currentPage,
      size: pagination.pageSize,
    };
    const response = await getActivateList(params);
    tableData.value = response.data.list;
    pagination.total = response.data.total;
  } catch (error) {
    console.error('获取激活列表失败:', error);
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

// 重置搜索
const handleReset = () => {
  Object.keys(searchForm).forEach((key) => {
    searchForm[key] = '';
  });
  pagination.currentPage = 1;
  fetchData();
};

// 查询
const handleQuery = () => {
  pagination.currentPage = 1;
  fetchData();
};

// 查看详情
const handleView = (row) => {
  router.push(`/service/activate-list/detail/${row.id}`);
};

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确认删除该激活记录吗？', '确认删除', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).catch((err) => {
    if (err === 'cancel') {
      ElMessage({ message: '取消成功', type: 'success' });
    }
  });
};

// 审批通过
const handleApprove = (row) => {
  ElMessageBox.confirm(
    `确认审批通过 "${row.wechatNickname}" 的激活申请吗？`,
    '确认审批',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    },
  )
    .then(async () => {
      try {
        await updateActivateStatus({
          id: row.id,
          status: 'approved',
        });
        ElMessage.success('审批成功');
        fetchData();
      } catch (error) {
        console.error('审批失败:', error);
        ElMessage.error('审批失败');
      }
    })
    .catch((err) => {
      if (err === 'cancel') {
        ElMessage({ message: '取消成功', type: 'success' });
      }
    });
};

// 审批驳回
const handleReject = (row) => {
  ElMessageBox.prompt('请输入驳回原因', '审批驳回', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    inputPattern: /.+/,
    inputErrorMessage: '请输入驳回原因',
  })
    .then(async ({ value }) => {
      try {
        await updateActivateStatus({
          id: row.id,
          status: 'rejected',
          reason: value,
        });
        ElMessage.success('驳回成功');
        fetchData();
      } catch (error) {
        console.error('驳回失败:', error);
        ElMessage.error('驳回失败');
      }
    })
    .catch((err) => {
      if (err === 'cancel') {
        ElMessage({ message: '取消成功', type: 'success' });
      }
    });
};

// 当前页改变
const handleCurrentChange = (page) => {
  pagination.currentPage = page;
  fetchData();
};

// 初始化
onMounted(() => {
  fetchData();
});
</script>

<style lang="scss" scoped>
.activate-page {
  padding: $spacing-xl;
  background-color: $bg-color;
  min-height: 100vh;
  max-width: 1280px;
  margin: 0 auto;
  .page-header {
    margin-bottom: $spacing-xl;

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: $text-color-dark;
      margin-bottom: $spacing-xs;
    }

    p {
      font-size: 14px;
      color: $text-color-light;
    }
  }

  .search-card {
    margin-bottom: $spacing-lg;
    border-radius: $border-radius;

    .search-form {
      padding: $spacing-xl;

      .search-item {
        margin-bottom: $search-spacing-vertical;

        .search-label {
          font-size: $search-label-size;
          color: $search-label-color;
          margin-bottom: $search-label-margin;
        }

        .search-input {
          width: 100%;
        }
      }

      .search-actions {
        display: flex;
        margin-top: 8px;
        justify-content: flex-end;

        .btn-reset {
          background-color: $reset-btn-color;
          border-color: $reset-btn-color;
          color: $reset-btn-text-color;

          &:hover {
            background-color: $reset-btn-hover-color;
            border-color: $reset-btn-hover-color;
          }
        }

        .btn-query {
          background-color: $query-btn-color;
          border-color: $query-btn-color;
        }
      }
    }
  }

  .table-card {
    border-radius: $border-radius;
  }
}
.pagination-container {
  margin-top: 0px;
  display: flex;
  justify-content: center;
}
// 自定义状态标签样式
.custom-status-tag {
  display: inline-block;
  padding: 4px 12px;
  font-size: 14px;
  border-radius: 16px;
  font-weight: 500;
  text-align: center;
  min-width: 70px;
  border: 1px solid;

  &.status-pending {
    background-color: #fff7e6;
    color: #fa8c16;
    border-color: #fff7e6;
  }

  &.status-approved {
    background-color: #f6ffed;
    color: #52c41a;
    border-color: #f6ffed;
  }

  &.status-rejected {
    background-color: #fff2f0;
    color: #ff4d4f;
    border-color: #fff2f0;
  }
}

// Element Plus 样式覆盖

:deep(.el-input__wrapper) {
  border-radius: $border-radius;
  padding: 8px 12px;
  height: 42px;
}

:deep(.el-select) {
  .el-input__wrapper {
    border-radius: $border-radius;
    padding: 8px 12px;
    height: 42px;
  }
}

:deep(.el-button) {
  border-radius: $border-radius;
  padding: 8px 12px;
  height: 40px;
  font-size: 16px;
}

:deep(.el-card__body) {
  padding: 0;
}

:deep(.el-pagination) {
  .el-pager li {
    border-radius: 6px;
    margin: 0 4px;

    &.is-active {
      background-color: #409eff;
      color: white;
    }
  }

  .btn-prev,
  .btn-next {
    border-radius: 6px;
    margin: 0 4px;
  }
}
/* 调整表头样式 */
:deep(.el-table__header th) {
  background-color: #f9fafb;
  height: 50px;
  line-height: 50px;
  font-weight: 600 !important;
  color: #86909c !important;
}
</style>
