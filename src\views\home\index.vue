<template>
  <div class="home-page">
    <div class="page-header">
      <h2>首页</h2>
      <p>欢迎回来，管理员！</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-container">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6" :lg="4" v-for="(item, index) in statsCards" :key="index" class="stats-card-col">
          <el-card shadow="hover" class="stats-card">
            <div class="stats-card-content">
              <div class="stats-number">{{ item.value }}</div>
              <div class="stats-title">{{ item.title }}</div>
            </div>
            <div class="stats-icon-wrapper" :class="item.iconClass">
              <el-icon class="stats-icon"><component :is="item.icon" /></el-icon>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 用户趋势图 -->
    <el-card class="chart-card" shadow="never">
      <template #header>
        <div class="card-header no-border">
          <span class="card-title">用户增长趋势</span>
          <div class="header-right">
            <div class="time-range-selector">
              <div class="time-range-item" :class="{ active: chartTimeRange === 'week' }" @click="chartTimeRange = 'week'">周</div>
              <div class="time-range-item" :class="{ active: chartTimeRange === 'month' }" @click="chartTimeRange = 'month'">月</div>
              <div class="time-range-item" :class="{ active: chartTimeRange === 'year' }" @click="chartTimeRange = 'year'">年</div>
            </div>
          </div>
        </div>
      </template>
      <div class="chart-container" ref="userTrendChart"></div>
    </el-card>

    <!-- 最新用户和订单 -->
    <el-row :gutter="20" class="data-row">
      <!-- 最新注册用户 -->
      <el-col :xs="24" :sm="24" :md="12">
        <el-card class="data-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">最近注册用户</span>
            </div>
          </template>
          <div class="table-container">
            <!-- 表头 -->
            <div class="table-header">
              <div class="column user-column">用户名</div>
              <div class="column time-column">注册时间</div>
              <div class="column user-column">邀请人</div>
              <div class="column time-column">邀请时间</div>
            </div>
            <!-- 表格内容 -->
            <div class="table-body">
              <div v-for="(user, index) in latestUsers" :key="index" class="table-row">
                <!-- 用户名 -->
                <div class="column user-column">
                  <div class="user-info">
                    <div class="avatar-circle" :style="{ backgroundColor: user.color }">
                      <el-avatar :size="32" :src="user.avatar">{{ user.name.charAt(0) }}</el-avatar>
                    </div>
                    <span>{{ user.name }}</span>
                  </div>
                </div>
                <!-- 注册时间 -->
                <div class="column time-column">{{ user.time }}</div>
                <!-- 邀请人 -->
                <div class="column user-column">
                  <div class="user-info" v-if="user.inviter">
                    <div class="avatar-circle" :style="{ backgroundColor: user.inviter.color }">
                      <el-avatar :size="32" :src="user.inviter.avatar">{{ user.inviter.name.charAt(0) }}</el-avatar>
                    </div>
                    <span>{{ user.inviter.name }}</span>
                  </div>
                </div>
                <!-- 邀请时间 -->
                <div class="column time-column">{{ user.inviteTime || '' }}</div>
              </div>
            </div>
            <!-- 分页 -->
            <div class="pagination">
              <div class="pagination-info">显示 {{ (userCurrentPage - 1) * 5 + 1 }}-{{ Math.min(userCurrentPage * 5, userTotal) }} 条，共 {{ userTotal }} 条</div>
              <el-pagination background layout="prev, pager, next" :total="userTotal" :page-size="5" :current-page="userCurrentPage" @current-change="handleUserPageChange" />
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 最近积分订单 -->
      <el-col :xs="24" :sm="24" :md="12">
        <el-card class="data-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">最近积分订单</span>
            </div>
          </template>
          <div class="table-container">
            <!-- 表头 -->
            <div class="table-header">
              <div class="column order-id-column">订单号</div>
              <div class="column user-column">用户</div>
              <div class="column product-column">商品</div>
              <div class="column points-column">积分</div>
              <div class="column status-column">状态</div>
            </div>
            <!-- 表格内容 -->
            <div class="table-body">
              <div v-for="(order, index) in latestOrders" :key="index" class="table-row">
                <!-- 订单号 -->
                <div class="column order-id-column">{{ order.id }}</div>
                <!-- 用户 -->
                <div class="column user-column">
                  <div class="user-info">
                    <div class="avatar-circle" :style="{ backgroundColor: order.user.color }">
                      <el-avatar :size="32" :src="order.user.avatar">{{ order.user.name.charAt(0) }}</el-avatar>
                    </div>
                    <span>{{ order.user.name }}</span>
                  </div>
                </div>
                <!-- 商品 -->
                <div class="column product-column">{{ order.product }}</div>
                <!-- 积分 -->
                <div class="column points-column">{{ order.points.toLocaleString() }}</div>
                <!-- 状态 -->
                <div class="column status-column">
                  <el-tag :type="getStatusType(order.status)" size="small">{{ order.status }}</el-tag>
                </div>
              </div>
            </div>
            <!-- 分页 -->
            <div class="pagination">
              <div class="pagination-info">显示 {{ (orderCurrentPage - 1) * 5 + 1 }}-{{ Math.min(orderCurrentPage * 5, orderTotal) }} 条，共 {{ orderTotal }} 条</div>
              <el-pagination background layout="prev, pager, next" :total="orderTotal" :page-size="5" :current-page="orderCurrentPage" @current-change="handleOrderPageChange" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick, watch } from 'vue'
import { getHomeStats, getUserTrend, getLatestUsers, getLatestOrders } from '@/api/home'
import * as echarts from 'echarts'

// 统计数据
const statsData = reactive({
  userTotal: 0,
  todayRegister: 0,
  todayVisit: 0,
  monthOrderCount: 0,
  todayIncome: 0,
  pendingOrder: 0
})

// 统计卡片配置
const statsCards = computed(() => [
  {
    title: '用户总数',
    value: statsData.userTotal,
    icon: 'User',
    iconClass: 'blue-icon'
  },
  {
    title: '今日注册',
    value: statsData.todayRegister,
    icon: 'Calendar',
    iconClass: 'green-icon'
  },
  {
    title: '今日访问',
    value: statsData.todayVisit,
    icon: 'View',
    iconClass: 'orange-icon'
  },
  {
    title: '月订单量',
    value: statsData.monthOrderCount,
    icon: 'ShoppingCart',
    iconClass: 'cyan-icon'
  },
  {
    title: '今日收入',
    value: statsData.todayIncome,
    icon: 'Money',
    iconClass: 'red-icon'
  },
  {
    title: '待处理订单',
    value: statsData.pendingOrder,
    icon: 'Document',
    iconClass: 'purple-icon'
  }
])

// 图表相关
const userTrendChart = ref(null)
const chartTimeRange = ref('week')
let chart = null

// 用户相关
const latestUsers = ref([])
const userTotal = ref(0)
const userCurrentPage = ref(1)

// 订单相关
const latestOrders = ref([])
const orderTotal = ref(0)
const orderCurrentPage = ref(1)

// 获取首页统计数据
const fetchHomeStats = async () => {
  try {
    const res = await getHomeStats()
    Object.assign(statsData, res.data)
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 初始化趋势图表
const initChart = async () => {
  try {
    const res = await getUserTrend()

    // 确保DOM已渲染
    await nextTick()

    // 初始化图表
    if (chart) {
      chart.dispose()
    }

    chart = echarts.init(userTrendChart.value)

    // 设置图表选项
    const option = {
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: res.data.days,
        axisLine: {
          lineStyle: {
            color: '#E4E7ED'
          }
        },
        axisLabel: {
          color: '#797979'
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#E4E7ED'
          }
        },
        axisLabel: {
          color: '#797979'
        }
      },
      series: [
        {
          name: '用户数',
          type: 'line',
          smooth: true,
          data: res.data.data,
          areaStyle: {
            opacity: 0.3,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(128, 155, 255, 0.8)'
              },
              {
                offset: 1,
                color: 'rgba(128, 155, 255, 0.1)'
              }
            ])
          },
          itemStyle: {
            color: '#6979f8'
          },
          lineStyle: {
            width: 3,
            color: '#6979f8'
          }
        }
      ]
    }

    chart.setOption(option)

    // 窗口大小变化时重绘图表
    window.addEventListener('resize', () => {
      chart && chart.resize()
    })
  } catch (error) {
    console.error('初始化图表失败:', error)
  }
}

// 获取最新注册用户
const fetchLatestUsers = async () => {
  try {
    const res = await getLatestUsers({
      page: userCurrentPage.value,
      pageSize: 5
    })
    latestUsers.value = res.data
    userTotal.value = res.total
  } catch (error) {
    console.error('获取最新注册用户失败:', error)
  }
}

// 获取最近积分订单
const fetchLatestOrders = async () => {
  try {
    const res = await getLatestOrders({
      page: orderCurrentPage.value,
      pageSize: 5
    })
    latestOrders.value = res.data
    orderTotal.value = res.total
  } catch (error) {
    console.error('获取最近积分订单失败:', error)
  }
}

// 获取订单状态对应的标签类型
const getStatusType = (status) => {
  switch (status) {
    case '已完成':
      return 'success'
    case '处理中':
      return 'warning'
    case '已取消':
      return 'danger'
    default:
      return 'info'
  }
}

// 用户分页变化处理
const handleUserPageChange = (page) => {
  userCurrentPage.value = page
  fetchLatestUsers()
}

// 订单分页变化处理
const handleOrderPageChange = (page) => {
  orderCurrentPage.value = page
  fetchLatestOrders()
}

// 监听图表时间范围变化
watch(chartTimeRange, () => {
  // 这里可以根据不同的时间范围加载不同的数据
  initChart()
})

// 页面加载时获取数据
onMounted(() => {
  fetchHomeStats()
  initChart()
  fetchLatestUsers()
  fetchLatestOrders()
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

.home-page {
  /* padding: 20px; */
  max-width: 1280px;
  margin: 0 auto;
  min-height: 500px; /* 设置最小高度，避免页面切换时晃动 */

  .page-header {
    margin-bottom: $card-spacing;

    h2 {
      font-size: 24px;
      margin: 0;
      font-weight: 600;
      color: $text-color-dark;
    }

    p {
      color: $text-color-light;
      margin: 5px 0 0;
    }
  }
}

.stats-container {
  margin-bottom: 10px;
}

.stats-card-col {
  padding: 0 10px !important;
  box-sizing: border-box;
  margin-bottom: 20px;
}

/* 响应式调整 */
@media (max-width: 767px) {
  .stats-card-col {
    margin-bottom: 15px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .stats-card-col {
    margin-bottom: 20px;
  }
}

/* 设置所有el-card的圆角 */
:deep(.el-card) {
  border-radius: $border-radius;
  overflow: hidden;
}

/* 只针对统计卡片的样式 */
.stats-container {
  :deep(.el-card) {
    height: 84px;
  }

  :deep(.el-card__body) {
    padding: 16px !important;
    height: 84px !important;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.stats-card {
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
  border-radius: $border-radius;

  &:hover {
    transform: translateY(-5px);
    box-shadow: $box-shadow;
  }

  .stats-card-content {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
  }

  .stats-number {
    font-size: 28px;
    font-weight: bold;
  }

  .stats-title {
    font-size: 14px;
    color: $text-color-light;
  }

  .stats-icon-wrapper {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .stats-icon {
    font-size: 20px;
    color: #ffffff;
  }
}

/* 图标背景色 */
.blue-icon {
  background-color: $primary-color;
}

.green-icon {
  background-color: $success-color;
}

.orange-icon {
  background-color: $warning-color;
}

.cyan-icon {
  background-color: #17a2b8;
}

.red-icon {
  background-color: $danger-color;
}

.purple-icon {
  background-color: $brand-color;
}

.chart-card {
  margin-bottom: 32px;
  border-radius: $border-radius;

  :deep(.el-card__body) {
    padding: 0;
  }

  :deep(.el-card__header) {
    border-bottom: none;
    padding-bottom: 0;
  }
}

.chart-container {
  height: 400px;
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: none !important;
}

.no-border {
  border-bottom: none !important;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: $text-color-dark;
}

.time-range-selector {
  display: flex;
  border-radius: $border-radius-btn;
  overflow: hidden;
}

.time-range-item {
  padding: 4px 12px;
  cursor: pointer;
  font-size: 14px;
  background-color: $bg-color;
  color: $text-color-light;
  text-align: center;
  min-width: auto;
  transition: all 0.3s;
  user-select: none;
  margin-left: 8px;
  height: 24px;
  line-height: 16px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  border-radius: $border-radius-btn;

  &:first-child {
    margin-left: 0;
  }

  &:last-child {
    border-radius: $border-radius-btn;
  }

  &.active {
    background-color: $primary-color;
    color: white;
  }
}

// .data-row {
//   margin-bottom: 32px;
// }

.data-card {
  height: 100%;
  border-radius: $border-radius;

  :deep(.el-card__body) {
    padding: 0;
  }
}

.table-container {
  width: 100%;
}

.table-header {
  display: flex;
  background-color: $bg-color-light;
  border-bottom: none;
  font-weight: 500;
  color: $text-color;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
}

.table-body {
  font-size: 14px;
}

.table-row {
  display: flex;
  border-bottom: 1px solid $border-color;
  height: 55px;
  align-items: center;
}

.column {
  padding: 0 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-column {
  flex: 2;
  display: flex;
  align-items: center;
  padding-left: 15px;
}

.time-column {
  flex: 2;
  color: $text-color-light;
}

.order-id-column {
  flex: 2;
  color: $text-color-light;
  padding-left: 15px;
}

.product-column {
  flex: 2;
}

.points-column {
  flex: 1;
  text-align: center;
  font-weight: bold;
  color: $text-color-light;
}

.status-column {
  flex: 1;
  text-align: center;
  padding-right: 15px;
}

.user-info {
  display: flex;
  align-items: center;

  .avatar-circle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;

  .pagination-info {
    color: $text-color-light;
    font-size: 13px;
  }
}
</style>
