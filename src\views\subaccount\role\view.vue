<template>
  <div class="role-view-container">
    <!-- 页面标题和返回按钮 -->
    <div class="page-header">
      <div class="header-left">
        <div class="title-info">
          <h1 class="page-title">角色权限详情</h1>
          <p class="page-description">查看角色的详细权限信息</p>
        </div>
      </div>
    </div>

    <!-- 详情内容 -->
    <el-card class="detail-card" shadow="never" v-loading="loading">
      <div class="detail-content">
        <!-- 基本信息 -->
        <div class="info-section">
          <h3 class="section-title">基本信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">角色ID</span>
              <span class="info-value">{{ roleData.roleId }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">角色名称</span>
              <span class="info-value role-name">{{ roleData.roleName }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">权限描述</span>
              <span class="info-value">{{ roleData.description }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">最后修改人</span>
              <span class="info-value">{{ roleData.modifier }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">最后修改时间</span>
              <span class="info-value">{{ roleData.updateTime }}</span>
            </div>
          </div>
        </div>

        <!-- 权限类型 -->
        <div class="info-section">
          <h3 class="section-title">权限类型</h3>
          <div class="permission-tags">
            <el-tag
              v-for="(permission, index) in rolePermissions"
              :key="index"
              :type="getPermissionTagType(permission)"
              effect="light"
              class="permission-tag"
            >
              {{ permission }}
            </el-tag>
          </div>
        </div>

        <!-- 权限菜单 -->
        <div class="info-section">
          <h3 class="section-title">权限菜单</h3>
          <el-tree
            :data="permissionMenus"
            :props="defaultProps"
            :default-expand-all="true"
            node-key="id"
            :expand-on-click-node="false"
            show-checkbox
            :default-checked-keys="checkedPermissions"
            disabled
          />
        </div>
      </div>
      <CustomBtn style="margin-top: 20px" type="back" @click="handleBack" />
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { getRoleDetail, getPermissionMenu } from '@/api/subaccount';
import { ElMessage } from 'element-plus';
import CustomBtn from '@/components/custom-btn.vue';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();

// 获取路由参数
const roleId = route.params.id;

// 数据
const loading = ref(false);
const roleData = ref({});
const permissionMenus = ref([]);

// 树形控件配置
const defaultProps = {
  children: 'children',
  label: 'label',
};

// 选中的权限
const checkedPermissions = ref([]);

// 根据角色名称判断权限类型
const rolePermissions = computed(() => {
  const permissions = [];

  if (roleData.value.roleName?.includes('管理员')) {
    permissions.push('管理员权限');
  }

  if (roleData.value.roleName?.includes('普通')) {
    permissions.push('普通权限');
  }

  if (roleData.value.roleName?.includes('访客')) {
    permissions.push('访客权限');
  }

  if (roleData.value.description?.includes('财务')) {
    permissions.push('财务权限');
  }

  return permissions.length > 0 ? permissions : ['普通权限'];
});

// 获取权限标签类型
const getPermissionTagType = (permission) => {
  switch (permission) {
    case '管理员权限':
      return 'danger';
    case '普通权限':
      return 'primary';
    case '访客权限':
      return 'info';
    case '财务权限':
      return 'warning';
    default:
      return '';
  }
};

// 获取角色详情
const fetchRoleDetail = async () => {
  loading.value = true;
  try {
    const res = await getRoleDetail(roleId);
    if (res.code === 200) {
      roleData.value = res.data;

      // 模拟选中的权限
      if (roleData.value.roleName?.includes('管理员')) {
        checkedPermissions.value = [
          'home-view',
          'home-add',
          'home-edit',
          'home-delete',
          'user-view',
          'user-add',
          'user-edit',
          'user-delete',
          'activity-view',
          'activity-add',
          'activity-edit',
          'activity-delete',
          'product-list-view',
          'product-list-add',
          'product-list-edit',
          'product-list-delete',
          'product-category-view',
          'product-category-add',
          'product-category-edit',
          'product-category-delete',
        ];
      } else if (roleData.value.roleName?.includes('普通')) {
        checkedPermissions.value = [
          'home-view',
          'user-view',
          'activity-view',
          'product-list-view',
          'product-category-view',
        ];

        if (roleData.value.description?.includes('财务')) {
          checkedPermissions.value.push('user-add', 'user-edit');
        }
      } else if (roleData.value.roleName?.includes('访客')) {
        checkedPermissions.value = ['home-view', 'product-list-view'];
      }
    } else {
      ElMessage.error(res.message || '获取角色详情失败');
    }
  } catch (error) {
    console.error('获取角色详情出错:', error);
    ElMessage.error('获取角色详情失败');
  } finally {
    loading.value = false;
  }
};

// 获取权限菜单
const fetchPermissionMenu = async () => {
  try {
    const res = await getPermissionMenu();
    if (res.code === 200) {
      permissionMenus.value = res.data;
    } else {
      ElMessage.error(res.message || '获取权限菜单失败');
    }
  } catch (error) {
    console.error('获取权限菜单出错:', error);
    ElMessage.error('获取权限菜单失败');
  }
};

// 返回列表
const handleBack = () => {
  router.push('/account/roles');
};

// 页面加载时获取数据
onMounted(async () => {
  await fetchRoleDetail();
  await fetchPermissionMenu();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables.scss' as *;

.role-view-container {
  padding: 20px;
  min-height: 100vh;
  max-width: 1280px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 16px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 12px;

    .title-info {
      .page-title {
        font-size: 20px;
        font-weight: 600;
        color: $text-color;
        margin: 0 0 6px 0;
      }

      .page-description {
        font-size: 13px;
        color: $text-color-light;
        margin: 0;
      }
    }
  }
}

.detail-card {
  border-radius: 10px;

  .detail-content {
    .info-section {
      margin-bottom: 32px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: $text-color;
        margin: 0 0 16px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #f0f0f0;
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px 24px;

        .info-item {
          display: flex;
          align-items: center;

          .info-label {
            font-size: 14px;
            color: $search-label-color;
            min-width: 80px;
            margin-right: 12px;
          }

          .info-value {
            font-size: 14px;
            color: $text-color;
            flex: 1;

            &.role-name {
              font-weight: 600;
              color: #165dff;
            }
          }
        }
      }

      .permission-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;

        .permission-tag {
          padding: 6px 12px;
          font-size: 14px;
        }
      }
    }
  }
}

:deep(.el-card) {
  border-radius: 10px;
  overflow: hidden;
}

:deep(.el-tree-node__content) {
  height: 36px;
}

:deep(.el-tree-node__label) {
  font-size: 14px;
}
</style>
