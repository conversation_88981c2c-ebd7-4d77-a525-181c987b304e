import Mock from 'mockjs';
const Random = Mock.Random;

// 协议列表数据
const agreementList = [
  {
    id: '1',
    name: '用户协议',
    updateTime: '2023-06-28 10:30:45',
    content:
      '<h2>用户协议</h2><p>欢迎使用我们的服务。本协议是用户与本平台之间关于使用本平台服务所订立的契约。</p><p>一、服务内容</p><p>1.1 本平台向用户提供丰富的在线服务，包括但不限于商品浏览、购买、支付等。</p><p>1.2 用户在使用本平台服务时，应当遵守中华人民共和国相关法律法规。</p><p>二、用户账号</p><p>2.1 用户注册成功后，将获得一个账号及相应的密码，用户应妥善保管账号及密码信息。</p><p>2.2 用户应对账号下的所有行为负责，因账号或密码保管不善造成的损失由用户自行承担。</p>',
  },
  {
    id: '2',
    name: '隐私保护协议',
    updateTime: '2023-07-15 14:22:33',
    content:
      '<h2>隐私保护协议</h2><p>本隐私政策描述了我们如何收集、使用和披露您的个人信息。</p><p>一、信息收集</p><p>1.1 我们收集的信息类型包括：个人身份信息、联系方式、设备信息等。</p><p>1.2 我们通过以下方式收集信息：您直接提供的信息、自动收集的信息、第三方来源的信息。</p><p>二、信息使用</p><p>2.1 我们使用收集的信息来提供、维护和改进我们的服务。</p><p>2.2 我们可能使用您的信息向您发送通知、更新和促销信息。</p><p>三、信息共享</p><p>3.1 我们不会出售您的个人信息。</p><p>3.2 在某些情况下，我们可能会与第三方共享您的信息，如服务提供商、业务合作伙伴等。</p>',
  },
  {
    id: '3',
    name: '会员协议',
    updateTime: '2023-08-05 09:15:20',
    content:
      '<h2>会员协议</h2><p>本会员协议规定了会员的权利与义务。</p><p>一、会员权益</p><p>1.1 会员可享受专属折扣和优惠活动。</p><p>1.2 会员可参与特定的会员专享活动。</p><p>1.3 会员可获得积分奖励，并可使用积分兑换礼品或服务。</p><p>二、会员义务</p><p>2.1 会员应遵守本平台的各项规定和政策。</p><p>2.2 会员不得利用会员身份进行违法或不当行为。</p><p>2.3 会员应保护个人账号安全，不得将账号借给他人使用。</p><p>三、会员等级</p><p>3.1 会员等级根据消费金额和活跃度进行划分。</p><p>3.2 不同等级的会员享有不同的权益和优惠。</p>',
  },
];

export default [
  // 协议列表接口
  {
    url: '/agreement/list',
    method: 'get',
    response: () => {
      return {
        code: 200,
        data: agreementList.map((item) => ({
          id: item.id,
          name: item.name,
          updateTime: item.updateTime,
        })),
        message: '获取协议列表成功',
      };
    },
  },

  // 协议详情接口
  {
    url: /\/agreement\/detail\/\d+/,
    method: 'get',
    response: (req) => {
      const id = req.url.match(/\/agreement\/detail\/(\d+)/)[1];
      const agreement = agreementList.find((item) => item.id === id);

      if (agreement) {
        return {
          code: 200,
          data: agreement,
          message: '获取协议详情成功',
        };
      } else {
        return {
          code: 404,
          data: null,
          message: '协议不存在',
        };
      }
    },
  },

  // 更新协议接口
  {
    url: '/agreement/update',
    method: 'post',
    response: (req) => {
      const body = req.body;
      const index = agreementList.findIndex((item) => item.id === body.id);

      if (index !== -1) {
        if (body.name) {
          agreementList[index].name = body.name;
        }
        if (body.content) {
          agreementList[index].content = body.content;
        }
        agreementList[index].updateTime = Random.datetime(
          'yyyy-MM-dd HH:mm:ss',
        );

        return {
          code: 200,
          data: null,
          message: '更新协议成功',
        };
      } else {
        return {
          code: 404,
          data: null,
          message: '协议不存在',
        };
      }
    },
  },
];
