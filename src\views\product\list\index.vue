<template>
  <div class="product-list-page">
    <transition name="fade" mode="out-in">
      <div v-if="!showAddForm" key="list">
        <div class="page-header">
          <h2>商品列表</h2>
          <p>管理系统内所有商品</p>
        </div>

        <!-- 搜索筛选区域 -->
        <el-card class="filter-card" shadow="never">
          <!-- 搜索栏第一行 - 分类 -->
          <div class="filter-row">
            <div class="filter-item">
              <div class="filter-label">一级分类</div>
              <el-select v-model="searchForm.level1" placeholder="请选择一级分类" clearable class="filter-select" @change="handleLevel1Change">
                <el-option v-for="item in level1Options" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </div>

            <div class="filter-item">
              <div class="filter-label">二级分类</div>
              <el-select v-model="searchForm.level2" placeholder="请选择二级分类" clearable class="filter-select" @change="handleLevel2Change" :disabled="!searchForm.level1">
                <el-option v-for="item in level2Options" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </div>

            <div class="filter-item">
              <div class="filter-label">三级分类</div>
              <el-select v-model="searchForm.level3" placeholder="请选择三级分类" clearable class="filter-select" :disabled="!searchForm.level2">
                <el-option v-for="item in level3Options" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </div>

            <div class="filter-item">
              <div class="filter-label">是否上架</div>
              <el-select v-model="searchForm.status" placeholder="全部" clearable class="filter-select">
                <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>
          </div>

          <!-- 搜索栏第二行 - 名称和编号 -->
          <div class="filter-row">
            <div class="filter-item">
              <div class="filter-label">商品名称</div>
              <el-input v-model="searchForm.name" placeholder="请输入商品名称" clearable></el-input>
            </div>

            <div class="filter-item">
              <div class="filter-label">商品编号</div>
              <el-input v-model="searchForm.id" placeholder="请输入商品编号" clearable></el-input>
            </div>

            <div class="filter-actions">
              <el-button @click="handleReset" plain class="reset-button" :disabled="isResetDisabled">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
              <el-button type="primary" @click="handleSearch" class="search-button">
                <el-icon><Search /></el-icon>
                查询
              </el-button>
              <el-button type="success" @click="showAddForm = true" class="add-button">
                <el-icon><Plus /></el-icon>
                新增
              </el-button>
            </div>
          </div>
        </el-card>

        <!-- 商品列表 -->
        <el-card class="product-card" shadow="never">
          <el-table :data="productList" style="width: 100%" v-loading="loading" :header-cell-style="tableHeaderStyle" :cell-style="tableCellStyle">
            <el-table-column prop="id" label="商品编号" width="100">
              <template #default="scope">
                <span class="text-secondary">{{ scope.row.id }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="商品名称" min-width="180">
              <template #default="scope">
                <span class="text-secondary">{{ scope.row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="商品主图" width="100">
              <template #default="scope">
                <div class="image-container">
                  <el-image :src="scope.row.image" fit="cover" class="product-image" :preview-src-list="[scope.row.image]" :initial-index="0" preview-teleported></el-image>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="分类" min-width="180">
              <template #default="scope">
                <div class="category-path">
                  <span class="text-secondary">{{ scope.row.category.level1.name }} - {{ scope.row.category.level2.name }} - {{ scope.row.category.level3.name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="store" label="关联门店" width="120">
              <template #default="scope">
                <span class="text-secondary">{{ scope.row.store }}</span>
              </template>
            </el-table-column>
            <el-table-column label="是否上架" width="100" align="center">
              <template #default="scope">
                <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" @change="(val) => handleStatusChange(scope.row, val)"></el-switch>
              </template>
            </el-table-column>
            <el-table-column prop="modifier" label="最后修改人" width="100">
              <template #default="scope">
                <span class="text-secondary">{{ scope.row.modifier }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="modifyTime" label="最后修改时间" width="160">
              <template #default="scope">
                <span class="text-secondary">{{ scope.row.modifyTime }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="260" fixed="right">
              <template #default="scope">
                <div class="action-buttons">
                  <el-button size="small" @click="handleView(scope.row)" class="table-action-btn view-btn">
                    <el-icon><View /></el-icon>
                    查看
                  </el-button>
                  <el-button size="small" @click="handleEdit(scope.row)" class="table-action-btn edit-btn">
                    <el-icon><Edit /></el-icon>
                    修改
                  </el-button>
                  <el-button size="small" @click="handleDelete(scope.row)" class="table-action-btn delete-btn">
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 分页栏 - 移出卡片，采用简洁的数字分页形式 -->
        <SimplePagination v-model="pagination.page" :page-size="pagination.pageSize" :total="pagination.total" @change="handleCurrentChange" />
      </div>

      <ProductForm v-else key="add-form" :is-edit="isEdit" :edit-data="editData" @cancel="handleCancelAdd" @submit="handleSubmitProduct" />
    </transition>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getProductList, getProductCategories, getSecondLevelCategories, getThirdLevelCategories, toggleProductStatus, deleteProduct, addProduct, updateProduct, getProductDetail } from '@/api/product'
import { Refresh, Search, View, Edit, Delete, Plus } from '@element-plus/icons-vue'
import ProductForm from '@/views/product/list/components/ProductForm.vue'
import SimplePagination from '@/components/SimplePagination.vue'

// 搜索表单
const searchForm = reactive({
  level1: '',
  level2: '',
  level3: '',
  status: '',
  name: '',
  id: ''
})

// 分类选项
const level1Options = ref([])
const level2Options = ref([])
const level3Options = ref([])

// 状态选项
const statusOptions = ref([
  { value: '', label: '全部' },
  { value: 1, label: '已上架' },
  { value: 0, label: '已下架' }
])

// 商品列表
const productList = ref([])
const loading = ref(false)

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 5,
  total: 0
})

// 是否重置按钮禁用状态
const isResetDisabled = ref(false)

// 表格表头样式
const tableHeaderStyle = {
  backgroundColor: '#FAFBFC',
  color: '#89929E',
  fontSize: '14px',
  fontWeight: '600',
  height: '50px'
}

// 表格单元格样式
const tableCellStyle = {
  padding: '12px 0',
  fontSize: '14px'
}

// 新增/编辑商品表单显示控制
const showAddForm = ref(false)
const isEdit = ref(false)
const editData = ref({})

// 获取一级分类
const fetchLevel1Categories = async () => {
  try {
    const res = await getProductCategories()
    level1Options.value = res.data
  } catch (error) {
    console.error('获取一级分类失败:', error)
  }
}

// 获取二级分类
const fetchLevel2Categories = async (parentId) => {
  try {
    const res = await getSecondLevelCategories(parentId)
    level2Options.value = res.data
  } catch (error) {
    console.error('获取二级分类失败:', error)
  }
}

// 获取三级分类
const fetchLevel3Categories = async (parentId) => {
  try {
    const res = await getThirdLevelCategories(parentId)
    level3Options.value = res.data
  } catch (error) {
    console.error('获取三级分类失败:', error)
  }
}

// 一级分类变化处理
const handleLevel1Change = async (val) => {
  searchForm.level2 = ''
  searchForm.level3 = ''
  level2Options.value = []
  level3Options.value = []

  if (val) {
    await fetchLevel2Categories(val)
  }
}

// 二级分类变化处理
const handleLevel2Change = async (val) => {
  searchForm.level3 = ''
  level3Options.value = []

  if (val) {
    await fetchLevel3Categories(val)
  }
}

// 获取商品列表
const fetchProductList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      level1: searchForm.level1,
      level2: searchForm.level2,
      level3: searchForm.level3,
      status: searchForm.status,
      name: searchForm.name,
      id: searchForm.id
    }

    const res = await getProductList(params)
    productList.value = res.data.list
    pagination.total = res.data.total
  } catch (error) {
    console.error('获取商品列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置搜索条件
const handleReset = () => {
  // 确保按钮不会被禁用
  isResetDisabled.value = false

  searchForm.level1 = ''
  searchForm.level2 = ''
  searchForm.level3 = ''
  searchForm.status = ''
  searchForm.name = ''
  searchForm.id = ''

  level2Options.value = []
  level3Options.value = []

  pagination.page = 1
  fetchProductList()

  // 强制移除焦点
  document.activeElement.blur()
}

// 查询
const handleSearch = () => {
  pagination.page = 1
  fetchProductList()
}

// 新增商品
const handleAdd = () => {
  showAddForm.value = true
  isEdit.value = false
  editData.value = {}
}

// 查看详情
const handleView = (row) => {
  ElMessage.info(`查看商品: ${row.name}`)
  // 此处可以实现查看详情的逻辑
}

// 编辑商品
const handleEdit = async (row) => {
  try {
    // 获取商品详情
    const res = await getProductDetail(row.id)

    // 设置编辑状态和数据
    editData.value = { ...res.data }
    isEdit.value = true
    showAddForm.value = true
  } catch (error) {
    console.error('获取商品详情失败:', error)
    ElMessage.error('获取商品详情失败')
  }
}

// 删除商品
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认删除商品 "${row.name}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      try {
        await deleteProduct(row.id)
        ElMessage.success('删除成功')

        // 从本地列表中删除
        const index = productList.value.findIndex((item) => item.id === row.id)
        if (index !== -1) {
          productList.value.splice(index, 1)

          // 如果当前页没有数据了且不是第一页，则回到上一页
          if (productList.value.length === 0 && pagination.page > 1) {
            pagination.page--
            fetchProductList()
          } else {
            // 更新总数
            pagination.total--
          }
        }
      } catch (error) {
        ElMessage.error('删除失败')
        console.error('删除商品失败:', error)
      }
    })
    .catch(() => {})
}

// 切换上架状态
const handleStatusChange = async (row, val) => {
  try {
    await toggleProductStatus(row.id, val)
    ElMessage.success(val === 1 ? '商品已上架' : '商品已下架')
  } catch (error) {
    // 恢复原状态
    row.status = val === 1 ? 0 : 1
    ElMessage.error('操作失败')
    console.error('切换商品状态失败:', error)
  }
}

// 分页大小变化
const handleSizeChange = (val) => {
  pagination.pageSize = val
  fetchProductList()
}

// 页码变化
const handleCurrentChange = (val) => {
  pagination.page = val
  fetchProductList()
}

// 取消新增/编辑商品
const handleCancelAdd = () => {
  showAddForm.value = false
  // 重置编辑状态
  setTimeout(() => {
    isEdit.value = false
    editData.value = {}
  }, 300) // 等待过渡动画结束后再重置
}

// 提交商品表单
const handleSubmitProduct = async (formData) => {
  try {
    if (isEdit.value) {
      // 编辑模式
      await updateProduct(formData)

      // 更新本地数据
      const index = productList.value.findIndex((item) => item.id === editData.value.id)
      if (index !== -1) {
        // 保留原有的一些数据
        const originalData = {
          id: productList.value[index].id,
          modifier: productList.value[index].modifier,
          modifyTime: productList.value[index].modifyTime
        }

        // 更新商品信息
        productList.value[index] = {
          ...formData,
          ...originalData
        }
      }
    } else {
      // 新增模式
      const res = await addProduct(formData)
      ElMessage.success('添加成功')

      // 如果在第一页，直接添加到列表顶部
      if (pagination.page === 1) {
        const newProduct = {
          ...formData,
          id: res.data.id,
          modifier: '管理员',
          modifyTime: new Date().toISOString().split('T')[0]
        }

        // 添加到本地列表顶部
        productList.value.unshift(newProduct)

        // 如果列表超出了每页显示数量，移除最后一项
        if (productList.value.length > pagination.pageSize) {
          productList.value.pop()
        }

        // 更新总数
        pagination.total++
      } else {
        // 不在第一页时，跳转到第一页查看新添加的商品
        pagination.page = 1
        await fetchProductList()
      }
    }

    // 关闭表单
    showAddForm.value = false

    // 重置编辑状态
    setTimeout(() => {
      isEdit.value = false
      editData.value = {}
    }, 300)
  } catch (error) {
    console.error('提交表单失败:', error)
    ElMessage.error(isEdit.value ? '更新失败' : '添加失败')
  }
}

// 初始化
onMounted(async () => {
  // 获取一级分类
  await fetchLevel1Categories()

  // 获取商品列表
  fetchProductList()

  // 添加全局点击事件，确保重置按钮在任何点击后都能恢复正常
  document.addEventListener('click', () => {
    isResetDisabled.value = false
  })
})

// 组件卸载时移除事件监听
onBeforeUnmount(() => {
  document.removeEventListener('click', () => {
    isResetDisabled.value = false
  })
})
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables' as *;

.product-list-page {
  max-width: 1280px;
  margin: 0 auto;
  box-sizing: border-box;
  min-height: 500px;
  padding-bottom: 20px;
}

.page-header {
  margin-bottom: 24px;

  h2 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    padding: 0;
    line-height: 32px;
    color: #1d2129;
  }

  p {
    font-size: 14px;
    color: #86909c;
    margin: 5px 0 0;
    padding: 0;
    line-height: 22px;
  }
}

.filter-card {
  margin-bottom: 24px;
  border-radius: 8px;

  :deep(.el-card__body) {
    padding: 20px;
    overflow: hidden;
  }
}

.product-card {
  margin-bottom: 24px;
  border-radius: 8px;

  :deep(.el-card__body) {
    padding: 0;
    overflow: hidden;
  }

  :deep(.el-table__row) {
    height: 62px; /* 调整表格行高与其他页面保持一致 */
  }
}

.filter-row {
  display: flex;
  align-items: flex-end;
  width: 100%;
  gap: 14px;
  flex-wrap: wrap;
  margin-bottom: 14px;

  &:last-child {
    margin-bottom: 0;
  }

  .filter-item {
    flex: 1;
    min-width: 200px;
  }
}

.filter-label {
  font-size: 14px;
  color: #86909c;
  margin-bottom: 4px;
}

.filter-select,
.el-input {
  width: 100%;

  :deep(.el-input__inner) {
    height: 42px;
    border-radius: 6px;
  }
}

// 搜索栏按钮样式已移至全局样式，此处无需重复定义

.image-container {
  width: 60px;
  height: 60px;
  overflow: hidden;
  border-radius: 4px;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.text-secondary {
  color: #86909c;
}

.table-action-btn {
  margin: 0 2px;
}

.pagination-container {
  padding: 20px;
  display: flex;
  justify-content: flex-end;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.table-action-btn {
  height: 32px;
  padding: 0 12px;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  margin: 0 4px;
  border-radius: 4px;
  border: none;
  flex-shrink: 0;

  &.view-btn {
    background-color: #e5f7e9;
    color: #00b42a;

    &:hover {
      background-color: #c7ecd1 !important;
      color: #00b42a !important;
    }
  }

  &.edit-btn {
    background-color: #e9eeff;
    color: #165dff;

    &:hover {
      background-color: #d0dbff !important;
      color: #165dff !important;
    }
  }

  &.delete-btn {
    background-color: #ffece8;
    color: #f53f3f;

    &:hover {
      background-color: #ffd4cc !important;
      color: #f53f3f !important;
    }
  }

  .el-icon {
    margin-right: 4px;
  }
}

// 添加渐入渐出过渡效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
  transform: translateY(0);
}

// 分页样式已封装到SimplePagination组件中
</style>
