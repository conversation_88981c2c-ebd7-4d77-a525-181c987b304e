<template>
  <div class="product-edit-container">
    <!-- 页面标题和返回按钮 -->
    <div class="page-header">
      <div class="header-left">
        <div class="title-info">
          <h1 class="page-title">修改积分商品</h1>
          <p class="page-description">在此页面修改积分商品信息</p>
        </div>
      </div>
      <CustomBtn style="margin-top: 20px" type="back" @click="handleBack" />
    </div>

    <!-- 编辑表单 -->
    <ProductAdd
      v-if="!loading"
      :initial-data="productData"
      @save="handleSave"
      @cancel="handleBack"
    />

    <!-- 加载中 -->
    <el-card v-else class="loading-card" shadow="never">
      <div class="loading-container">
        <el-skeleton :rows="10" animated />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getPointsProductDetail, updatePointsProduct } from '@/api/points-mall';
import { ElMessage } from 'element-plus';
import CustomBtn from '@/components/custom-btn.vue';
import ProductAdd from './ProductAdd.vue';

// 接收参数
const props = defineProps({
  productId: {
    type: [String, Number],
    required: true,
  },
});

// 定义事件
const emit = defineEmits(['save', 'back']);

// 数据
const loading = ref(true);
const productData = ref({});

// 获取商品详情
const fetchProductDetail = async () => {
  loading.value = true;
  try {
    const response = await getPointsProductDetail(props.productId);
    if (response.code === 200) {
      productData.value = response.data;

      // 确保数据格式正确
      if (!productData.value.productGallery) {
        productData.value.productGallery = [];
      } else if (Array.isArray(productData.value.productGallery)) {
        // 将字符串数组转换为对象数组
        productData.value.productGallery = productData.value.productGallery.map(
          (url) => ({
            url,
            name: url.split('/').pop(),
          }),
        );
      }

      if (!productData.value.specifications) {
        productData.value.specifications = [
          {
            specName: '',
            requiredPoints: '',
            stockCount: '',
            image: '',
            imagePreview: '',
          },
        ];
      }
    } else {
      ElMessage.error(response.message || '获取商品详情失败');
    }
  } catch (error) {
    console.error('获取商品详情失败:', error);
    ElMessage.error('获取详情失败');
  } finally {
    loading.value = false;
  }
};

// 保存修改
const handleSave = async (data) => {
  try {
    // 添加ID
    const updateData = {
      ...data,
      id: props.productId,
    };

    // 处理productGallery数据，确保格式正确
    if (Array.isArray(updateData.productGallery)) {
      updateData.productGallery = updateData.productGallery.filter(
        (item) => item && typeof item === 'string',
      );
    }

    await updatePointsProduct(updateData);
    ElMessage.success('修改商品成功');
    emit('save', updateData);
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('保存失败，请检查输入信息');
  }
};

// 返回列表
const handleBack = () => {
  emit('back');
};

// 组件挂载时获取数据
onMounted(() => {
  fetchProductDetail();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables.scss' as *;

.product-edit-container {
  padding: $spacing-xl;
  background-color: $bg-color;
  min-height: 100vh;
  max-width: 1280px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;

  .header-left {
    .title-info {
      .page-title {
        font-size: 20px;
        font-weight: 600;
        color: $text-color;
        margin: 0 0 6px 0;
      }

      .page-description {
        font-size: 13px;
        color: $text-color-light;
        margin: 0;
      }
    }
  }
}

.loading-card {
  border-radius: 10px;

  .loading-container {
    padding: 40px;
  }
}

:deep(.el-card) {
  border-radius: 10px;
  overflow: hidden;
}
</style>
