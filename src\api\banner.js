import request from '@/utils/request'

// 获取轮播图列表
export function getBannerList(params) {
  return request({
    url: '/mock/banner/list',
    method: 'get',
    params
  })
}

// 获取轮播图详情
export function getBannerDetail(id) {
  return request({
    url: `/banner/detail/${id}`,
    method: 'get'
  })
}

// 添加轮播图
export function addBanner(data) {
  return request({
    url: '/banner/add',
    method: 'post',
    data
  })
}

// 更新轮播图
export function updateBanner(data) {
  return request({
    url: '/banner/update',
    method: 'put',
    data
  })
}

// 删除轮播图
export function deleteBanner(id) {
  return request({
    url: `/banner/delete/${id}`,
    method: 'delete'
  })
}

// 保存轮播图配置
export function saveBannerConfig(data) {
  return request({
    url: '/banner/save',
    method: 'post',
    data
  })
}

// 获取轮播图详细配置
export function getBannerConfig(id) {
  return request({
    url: `/banner/config/${id}`,
    method: 'get'
  })
}
