import request from '@/utils/request';

/**
 * 获取协议列表
 */
export function getAgreementList() {
  return request({
    url: '/agreement/list',
    method: 'get',
  });
}

/**
 * 获取协议详情
 * @param {string} id 协议ID
 */
export function getAgreementDetail(id) {
  return request({
    url: `/agreement/detail/${id}`,
    method: 'get',
  });
}

/**
 * 更新协议内容
 * @param {object} data 协议数据，包含id、name和content
 */
export function updateAgreement(data) {
  return request({
    url: '/agreement/update',
    method: 'post',
    data,
  });
}
