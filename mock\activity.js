// 活动分类相关的mock接口

// 使用全局数据存储，支持修改操作
let nextId = 100 // 新增分类的起始ID

// 生成活动分类树结构数据
let categoryTree = [
  {
    id: 1,
    name: '促销活动',
    level: 1,
    children: [
      {
        id: 2,
        name: '限时折扣',
        level: 2,
        parentId: 1,
        children: [
          {
            id: 3,
            name: '新用户专享',
            level: 3,
            parentId: 2
          },
          {
            id: 4,
            name: '老用户回馈',
            level: 3,
            parentId: 2
          },
          {
            id: 5,
            name: '节假日特别活动',
            level: 3,
            parentId: 2
          }
        ]
      },
      {
        id: 6,
        name: '满减优惠',
        level: 2,
        parentId: 1,
        children: []
      }
    ]
  },
  {
    id: 7,
    name: '会员活动',
    level: 1,
    children: [
      {
        id: 9,
        name: '会员专享',
        level: 2,
        parentId: 7,
        children: [
          {
            id: 10,
            name: '银卡会员',
            level: 3,
            parentId: 9
          },
          {
            id: 11,
            name: '金卡会员',
            level: 3,
            parentId: 9
          },
          {
            id: 12,
            name: '钻石会员',
            level: 3,
            parentId: 9
          }
        ]
      },
      {
        id: 13,
        name: '会员积分',
        level: 2,
        parentId: 7,
        children: [
          {
            id: 14,
            name: '积分兑换',
            level: 3,
            parentId: 13
          },
          {
            id: 15,
            name: '积分加速',
            level: 3,
            parentId: 13
          }
        ]
      }
    ]
  },
  {
    id: 8,
    name: '品牌活动',
    level: 1,
    children: [
      {
        id: 16,
        name: '品牌联合',
        level: 2,
        parentId: 8,
        children: [
          {
            id: 17,
            name: '跨界合作',
            level: 3,
            parentId: 16
          },
          {
            id: 18,
            name: '联名活动',
            level: 3,
            parentId: 16
          }
        ]
      },
      {
        id: 19,
        name: '品牌推广',
        level: 2,
        parentId: 8,
        children: [
          {
            id: 20,
            name: '新品发布',
            level: 3,
            parentId: 19
          },
          {
            id: 21,
            name: '品牌故事',
            level: 3,
            parentId: 19
          }
        ]
      }
    ]
  }
]

// 辅助函数 - 根据ID查找分类
const findCategoryById = (id, tree = categoryTree) => {
  for (const category of tree) {
    if (category.id === id) {
      return category
    }

    if (category.children && category.children.length > 0) {
      const found = findCategoryById(id, category.children)
      if (found) return found
    }
  }

  return null
}

// 辅助函数 - 删除分类
const deleteCategory = (id, tree = categoryTree) => {
  for (let i = 0; i < tree.length; i++) {
    if (tree[i].id === id) {
      tree.splice(i, 1)
      return true
    }

    if (tree[i].children && tree[i].children.length > 0) {
      const deleted = deleteCategory(id, tree[i].children)
      if (deleted) return true
    }
  }

  return false
}

// 生成一级分类列表 - 动态从分类树生成
const getLevel1Categories = () => {
  return categoryTree.map((item) => ({
    id: item.id,
    name: item.name,
    level: item.level
  }))
}

// 生成二级分类列表（根据一级分类ID） - 动态从分类树生成
const getLevel2Categories = (parentId) => {
  const parent = findCategoryById(parentId)
  if (!parent || !parent.children) return []

  return parent.children.map((item) => ({
    id: item.id,
    name: item.name,
    level: item.level,
    parentId: item.parentId
  }))
}

// 生成三级分类列表（根据二级分类ID） - 动态从分类树生成
const getLevel3Categories = (parentId) => {
  const parent = findCategoryById(parentId)
  if (!parent || !parent.children) return []

  return parent.children.map((item) => ({
    id: item.id,
    name: item.name,
    level: item.level,
    parentId: item.parentId
  }))
}

// 生成活动列表数据
let activityList = [
  {
    id: 1,
    name: '新人首单8折',
    theme: '新用户专享优惠',
    description: '新用户首单享8折',
    type: '促销',
    typeId: 3,
    status: 1,
    sort: 1,
    store: '旗舰店',
    category: '首页 - 促销活动',
    creator: '管理员',
    createTime: '2023-06-23',
    image: 'https://cdn.pixabay.com/photo/2016/11/23/18/14/men-1854191_1280.jpg',
    content: '<p>新用户专享8折优惠活动，首次购买即可享受超值折扣！</p>',
    jumpType: '跳转图文详情',
    miniappPath: ''
  },
  {
    id: 2,
    name: '会员积分兑换',
    theme: '积分好礼等你拿',
    description: '500积分兑换优惠券',
    type: '会员',
    typeId: 14,
    status: 1,
    sort: 2,
    store: '北京店',
    category: '首页 - 品牌活动',
    creator: '李四',
    createTime: '2023-06-22',
    image: 'https://cdn.pixabay.com/photo/2016/11/29/09/41/bag-1868758_1280.jpg',
    content: '<p>会员积分兑换活动，500积分即可兑换超值优惠券！</p>',
    jumpType: '跳转图文详情',
    miniappPath: ''
  },
  {
    id: 3,
    name: '夏季新品发布',
    theme: '2023夏季新品系列',
    description: '2023夏季新品上市',
    type: '品牌',
    typeId: 20,
    status: 0,
    sort: 3,
    store: '上海店',
    category: '首页 - 促销活动',
    creator: '王五',
    createTime: '2023-06-21',
    image: 'https://cdn.pixabay.com/photo/2017/08/06/12/52/woman-2592247_1280.jpg',
    content: '<p>夏季新品发布，全新设计理念，引领时尚潮流！</p>',
    jumpType: '跳转小程序路径',
    miniappPath: '/pages/product/list'
  },
  {
    id: 4,
    name: '五一特惠',
    theme: '劳动节感恩回馈',
    description: '五一全场商品8折起',
    type: '促销',
    typeId: 5,
    status: 0,
    sort: 1,
    store: '全部门店',
    category: '首页 - 品牌活动',
    creator: '赵六',
    createTime: '2023-04-30',
    image: 'https://cdn.pixabay.com/photo/2016/11/19/15/40/clothes-1839935_1280.jpg',
    content: '<p>五一劳动节特别回馈，全场商品8折起，感恩有你！</p>',
    jumpType: '跳转图文详情',
    miniappPath: ''
  },
  {
    id: 5,
    name: '学生专享优惠',
    theme: '开学季特别活动',
    description: '凭学生证享7折',
    type: '会员',
    typeId: 10,
    status: 1,
    sort: 2,
    store: '广州店',
    category: '首页 - 促销活动',
    creator: '钱七',
    createTime: '2023-06-20',
    image: 'https://cdn.pixabay.com/photo/2016/11/22/19/08/hangers-1850082_1280.jpg',
    content: '<p>学生专享优惠活动，凭有效学生证即可享受7折优惠！</p>',
    jumpType: '跳转图文详情',
    miniappPath: ''
  },
  {
    id: 6,
    name: '父亲节特惠',
    theme: '感恩父亲节',
    description: '献给爸爸的礼物',
    type: '促销',
    typeId: 5,
    status: 0,
    sort: 3,
    store: '旗舰店',
    category: '首页 - 品牌活动',
    creator: '管理员',
    createTime: '2023-06-19',
    image: 'https://cdn.pixabay.com/photo/2017/05/13/12/40/fashion-2309519_1280.jpg',
    content: '<p>父亲节特别活动，为爸爸挑选最贴心的礼物！</p>',
    jumpType: '跳转小程序路径',
    miniappPath: '/pages/gift/father'
  },
  {
    id: 7,
    name: '618购物节',
    theme: '年中大促狂欢',
    description: '全场满300减50',
    type: '促销',
    typeId: 3,
    status: 1,
    sort: 1,
    store: '旗舰店',
    category: '首页 - 促销活动',
    creator: '管理员',
    createTime: '2023-06-18',
    image: 'https://cdn.pixabay.com/photo/2016/11/23/18/14/men-1854191_1280.jpg',
    content: '<p>618购物节年中大促，全场满300减50，更多优惠等你来！</p>',
    jumpType: '跳转图文详情',
    miniappPath: ''
  },
  {
    id: 8,
    name: '母亲节感恩回馈',
    theme: '献给妈妈的爱',
    description: '全场女装8折起',
    type: '会员',
    typeId: 11,
    status: 1,
    sort: 2,
    store: '北京店',
    category: '首页 - 品牌活动',
    creator: '李四',
    createTime: '2023-05-14',
    image: 'https://cdn.pixabay.com/photo/2016/11/29/09/41/bag-1868758_1280.jpg',
    content: '<p>母亲节感恩活动，全场女装8折起，为妈妈送上最温暖的爱！</p>',
    jumpType: '跳转图文详情',
    miniappPath: ''
  },
  {
    id: 9,
    name: '春季新品预售',
    theme: '春天的时尚宣言',
    description: '新品预售享9折',
    type: '品牌',
    typeId: 20,
    status: 0,
    sort: 3,
    store: '上海店',
    category: '首页 - 促销活动',
    creator: '王五',
    createTime: '2023-03-15',
    image: 'https://cdn.pixabay.com/photo/2017/08/06/12/52/woman-2592247_1280.jpg',
    content: '<p>春季新品预售活动，新品预售享9折优惠，抢先体验春季时尚！</p>',
    jumpType: '跳转小程序路径',
    miniappPath: '/pages/product/presale'
  },
  {
    id: 10,
    name: '情人节特别活动',
    theme: '甜蜜情人节',
    description: '情侣套装特价',
    type: '促销',
    typeId: 5,
    status: 1,
    sort: 1,
    store: '广州店',
    category: '首页 - 品牌活动',
    creator: '赵六',
    createTime: '2023-02-14',
    image: 'https://cdn.pixabay.com/photo/2016/11/19/15/40/clothes-1839935_1280.jpg',
    content: '<p>情人节特别活动，情侣套装特价优惠，让爱更甜蜜！</p>',
    jumpType: '跳转图文详情',
    miniappPath: ''
  },
  {
    id: 11,
    name: '双11购物狂欢',
    theme: '全年最大优惠',
    description: '全场5折起',
    type: '促销',
    typeId: 3,
    status: 0,
    sort: 2,
    store: '全部门店',
    category: '首页 - 促销活动',
    creator: '钱七',
    createTime: '2022-11-11',
    image: 'https://cdn.pixabay.com/photo/2016/11/22/19/08/hangers-1850082_1280.jpg',
    content: '<p>双11购物狂欢节，全年最大优惠，全场5折起！</p>',
    jumpType: '跳转图文详情',
    miniappPath: ''
  },
  {
    id: 12,
    name: '中秋节团圆礼',
    theme: '中秋佳节送好礼',
    description: '精美礼盒装',
    type: '会员',
    typeId: 12,
    status: 1,
    sort: 3,
    store: '旗舰店',
    category: '首页 - 品牌活动',
    creator: '管理员',
    createTime: '2023-09-29',
    image: 'https://cdn.pixabay.com/photo/2017/05/13/12/40/fashion-2309519_1280.jpg',
    content: '<p>中秋节特别活动，精美礼盒装，送给最重要的人！</p>',
    jumpType: '跳转小程序路径',
    miniappPath: '/pages/gift/midautumn'
  },
  {
    id: 13,
    name: '国庆黄金周',
    theme: '国庆七天乐',
    description: '连续7天每天惊喜',
    type: '促销',
    typeId: 5,
    status: 1,
    sort: 1,
    store: '北京店',
    category: '首页 - 促销活动',
    creator: '李四',
    createTime: '2023-10-01',
    image: 'https://cdn.pixabay.com/photo/2016/11/23/18/14/men-1854191_1280.jpg',
    content: '<p>国庆黄金周活动，连续7天每天都有惊喜等你来发现！</p>',
    jumpType: '跳转图文详情',
    miniappPath: ''
  },
  {
    id: 14,
    name: '圣诞节狂欢',
    theme: '圣诞快乐',
    description: '圣诞礼物大放送',
    type: '品牌',
    typeId: 18,
    status: 0,
    sort: 2,
    store: '上海店',
    category: '首页 - 品牌活动',
    creator: '王五',
    createTime: '2023-12-25',
    image: 'https://cdn.pixabay.com/photo/2016/11/29/09/41/bag-1868758_1280.jpg',
    content: '<p>圣诞节狂欢活动，圣诞礼物大放送，让这个圣诞更加精彩！</p>',
    jumpType: '跳转小程序路径',
    miniappPath: '/pages/christmas/gifts'
  },
  {
    id: 15,
    name: '元旦新年礼',
    theme: '新年新气象',
    description: '迎接2024年',
    type: '会员',
    typeId: 10,
    status: 1,
    sort: 3,
    store: '广州店',
    category: '首页 - 促销活动',
    creator: '赵六',
    createTime: '2024-01-01',
    image: 'https://cdn.pixabay.com/photo/2017/08/06/12/52/woman-2592247_1280.jpg',
    content: '<p>元旦新年礼活动，迎接2024年，新年新气象，新的开始！</p>',
    jumpType: '跳转图文详情',
    miniappPath: ''
  }
]

// 店铺列表
const storeList = [
  { value: '全部门店', label: '全部门店' },
  { value: '旗舰店', label: '旗舰店' },
  { value: '北京店', label: '北京店' },
  { value: '上海店', label: '上海店' },
  { value: '广州店', label: '广州店' }
]

// 分类列表
const categoryList = [
  { value: '全部', label: '全部' },
  { value: '首页 - 促销活动', label: '首页 - 促销活动' },
  { value: '首页 - 品牌活动', label: '首页 - 品牌活动' }
]

// 活动下一个ID
let nextActivityId = 16

// 定义接口数组
export default [
  // 获取活动分类树结构
  {
    url: '/mock/activity/category/tree',
    method: 'get',
    response: () => {
      return {
        code: 200,
        data: categoryTree,
        message: '获取成功'
      }
    }
  },

  // 获取活动分类列表
  {
    url: '/api/activity/categories',
    method: 'get',
    response: (req) => {
      const level1Id = req.query.level1Id ? parseInt(req.query.level1Id) : null
      const level2Id = req.query.level2Id ? parseInt(req.query.level2Id) : null

      let data = []

      if (level2Id) {
        // 获取三级分类
        data = getLevel3Categories(level2Id)
      } else if (level1Id) {
        // 获取二级分类
        data = getLevel2Categories(level1Id)
      } else {
        // 获取一级分类
        data = getLevel1Categories()
      }

      return {
        code: 200,
        data,
        message: '获取成功'
      }
    }
  },

  // 添加活动分类
  {
    url: '/mock/activity/category',
    method: 'post',
    response: (req) => {
      const { name, level, parentId } = req.body

      // 参数验证
      if (!name || !level) {
        return {
          code: 400,
          message: '参数不完整'
        }
      }

      // 创建新分类
      const newCategory = {
        id: nextId++,
        name,
        level,
        parentId: parentId || null,
        children: []
      }

      // 添加到分类树中
      if (level === 1) {
        // 添加一级分类
        categoryTree.push(newCategory)
      } else {
        // 添加二级或三级分类
        const parent = findCategoryById(parentId)
        if (parent) {
          if (!parent.children) {
            parent.children = []
          }
          parent.children.push(newCategory)
        } else {
          return {
            code: 404,
            message: '父分类不存在'
          }
        }
      }

      return {
        code: 200,
        data: { id: newCategory.id },
        message: '添加成功'
      }
    }
  },

  // 更新活动分类
  {
    url: '/mock/activity/category/:id',
    method: 'put',
    response: (req) => {
      // 从URL中提取ID
      const idStr = req.url.split('/').pop()
      const id = parseInt(idStr)
      const { name } = req.body

      // 参数验证
      if (!name) {
        return {
          code: 400,
          message: '参数不完整'
        }
      }

      // 查找并更新分类
      const category = findCategoryById(id)
      if (category) {
        category.name = name
        return {
          code: 200,
          message: '更新成功'
        }
      } else {
        return {
          code: 404,
          message: '分类不存在'
        }
      }
    }
  },

  // 删除活动分类
  {
    url: '/mock/activity/category/:id',
    method: 'delete',
    response: (req) => {
      // 从URL中提取ID
      const idStr = req.url.split('/').pop()
      const id = parseInt(idStr)

      // 查找分类
      const category = findCategoryById(id)
      if (!category) {
        return {
          code: 404,
          message: '分类不存在'
        }
      }

      // 检查是否有子分类
      if (category.children && category.children.length > 0) {
        return {
          code: 403,
          message: '该分类下有子分类，无法删除'
        }
      }

      // 删除分类
      const deleted = deleteCategory(id)
      if (deleted) {
        return {
          code: 200,
          message: '删除成功'
        }
      } else {
        return {
          code: 500,
          message: '删除失败'
        }
      }
    }
  },

  // 获取活动列表
  {
    url: '/mock/activities',
    method: 'get',
    response: (req) => {
      const { page = 1, pageSize = 10, name, store, category } = req.query

      let filteredList = [...activityList]

      // 根据活动名称筛选
      if (name) {
        filteredList = filteredList.filter((item) => item.name.includes(name))
      }

      // 根据店铺筛选
      if (store && store !== '全部门店') {
        filteredList = filteredList.filter((item) => item.store === store)
      }

      // 根据分类筛选
      if (category && category !== '全部') {
        filteredList = filteredList.filter((item) => item.category === category)
      }

      // 分页
      const startIndex = (page - 1) * pageSize
      const endIndex = startIndex + parseInt(pageSize)
      const pageData = filteredList.slice(startIndex, endIndex)

      return {
        code: 200,
        data: {
          list: pageData,
          total: filteredList.length,
          page: parseInt(page),
          pageSize: parseInt(pageSize)
        },
        message: '获取成功'
      }
    }
  },

  // 获取店铺列表
  {
    url: '/mock/activity/stores',
    method: 'get',
    response: () => {
      return {
        code: 200,
        data: storeList,
        message: '获取成功'
      }
    }
  },

  // 获取分类列表
  {
    url: '/api/categories',
    method: 'get',
    response: () => {
      return {
        code: 200,
        data: categoryList,
        message: '获取成功'
      }
    }
  },

  // 获取活动详情
  {
    url: '/api/activities/:id',
    method: 'get',
    response: (req) => {
      // 从URL中提取ID
      const idStr = req.url.split('/').pop()
      const id = parseInt(idStr)

      const activity = activityList.find((item) => item.id === id)

      if (activity) {
        return {
          code: 200,
          data: activity,
          message: '获取成功'
        }
      } else {
        return {
          code: 404,
          message: '活动不存在'
        }
      }
    }
  },

  // 添加活动
  {
    url: '/api/activities',
    method: 'post',
    response: (req) => {
      // 创建新活动
      const newActivity = {
        ...req.body,
        id: nextActivityId++,
        creator: '管理员',
        createTime: new Date().toISOString().split('T')[0]
      }

      // 添加到活动列表
      activityList.unshift(newActivity)

      return {
        code: 200,
        data: { id: newActivity.id },
        message: '添加成功'
      }
    }
  },

  // 更新活动
  {
    url: '/api/activities/:id',
    method: 'put',
    response: (req) => {
      // 从URL中提取ID
      const idStr = req.url.split('/').pop()
      const id = parseInt(idStr)

      // 查找活动
      const index = activityList.findIndex((item) => item.id === id)
      if (index === -1) {
        return {
          code: 404,
          message: '活动不存在'
        }
      }

      // 保留原有的一些数据
      const originalData = {
        id: activityList[index].id,
        creator: activityList[index].creator,
        createTime: activityList[index].createTime
      }

      // 更新活动信息
      activityList[index] = {
        ...req.body,
        ...originalData
      }

      return {
        code: 200,
        data: { id },
        message: '更新成功'
      }
    }
  },

  // 删除活动
  {
    url: '/api/activities/:id',
    method: 'delete',
    response: (req) => {
      // 从URL中提取ID
      const idStr = req.url.split('/').pop()
      const id = parseInt(idStr)

      // 查找并删除活动
      const index = activityList.findIndex((item) => item.id === id)
      if (index === -1) {
        return {
          code: 404,
          message: '活动不存在'
        }
      }

      // 删除活动
      activityList.splice(index, 1)

      return {
        code: 200,
        message: '删除成功'
      }
    }
  },

  // 切换活动状态
  {
    url: '/api/activities/:id/status',
    method: 'put',
    response: (req) => {
      // 从URL中提取ID
      const idStr = req.url.split('/')[3] // /api/activities/:id/status
      const id = parseInt(idStr)
      const { status } = req.body

      // 查找活动
      const index = activityList.findIndex((item) => item.id === id)
      if (index === -1) {
        return {
          code: 404,
          message: '活动不存在'
        }
      }

      // 更新状态
      activityList[index].status = status

      return {
        code: 200,
        message: status === 1 ? '上架成功' : '下架成功'
      }
    }
  }
]
// 删除活动
export function deleteActivity(id) {
  return request({
    url: `/api/activities/${id}`,
    method: 'delete'
  })
}
