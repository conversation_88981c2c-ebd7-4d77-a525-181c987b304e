# 李白眼镜小程序后台管理系统

## 项目概述

李白眼镜小程序后台管理系统是一个基于Vue 3 + Vite构建的现代化管理平台，用于管理李白眼镜小程序的各项功能，包括用户管理、商品管理、门店管理、活动管理等。系统采用Element Plus作为UI组件库，提供了直观、美观的用户界面。

## 技术栈

- **前端框架**：Vue 3 + Vite
- **UI组件库**：Element Plus
- **状态管理**：Pinia
- **路由管理**：Vue Router
- **HTTP请求**：Axios
- **图表库**：ECharts
- **富文本编辑器**：WangEditor
- **CSS预处理器**：SASS

## 目录结构

```
├── src/                    # 源代码目录
│   ├── api/                # API接口定义
│   ├── assets/             # 静态资源文件
│   │   ├── styles/         # 样式文件
│   │   └── images/         # 图片资源
│   ├── components/         # 可复用组件
│   ├── layout/             # 布局组件
│   ├── router/             # 路由配置
│   ├── store/              # 状态管理
│   ├── utils/              # 工具函数
│   ├── views/              # 页面视图组件
│   ├── App.vue             # 根组件
│   └── main.js             # 应用入口文件
├── mock/                   # 模拟数据
├── public/                 # 公共静态资源
├── index.html              # HTML模板
├── vite.config.js          # Vite配置
└── package.json            # 项目依赖
```

## 功能模块

系统包含以下主要功能模块：

1. **用户管理** - 管理系统用户信息
2. **活动管理** - 包含活动列表和活动分类
3. **门店管理** - 包含门店置顶、门店列表、审核列表和门店类型标签
4. **轮播图管理** - 管理首页轮播图
5. **产品管理** - 包含商品列表和分类管理
6. **售后激活** - 包含激活列表和真伪数据
7. **红包领取列表** - 管理红包领取记录
8. **积分商城** - 包含积分商品、积分订单和积分规则
9. **协议管理** - 管理用户协议
10. **子账号管理** - 包含角色权限和账号列表
11. **其他配置** - 包含小程序设置和生成邀请商家链接

## 开发规范

- 组件命名采用PascalCase命名法
- 组件内部变量和方法使用小驼峰命名法
- 样式采用SASS开发，全局样式统一管理
- 按钮、输入框、搜索栏等有明确的样式规范
- 路由结构与视图结构相对应，采用层级嵌套结构

## 快速开始

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产构建
npm run preview
```

## 浏览器支持

- Chrome
- Firefox
- Safari
- Edge
