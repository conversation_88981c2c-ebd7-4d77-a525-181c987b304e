// 售后激活相关的mock接口
export default [
  // 获取激活列表
  {
    url: '/service/activate/list',
    method: 'get',
    response: (req) => {
      const {
        antiCounterfeitCode,
        wechatNickname,
        approvalStatus,
        page = 1,
        size = 10,
      } = req.query;

      // 模拟的激活数据
      const allActivateData = [
        {
          id: 1,
          wechatNickname: '张三',
          antiCounterfeitCode: '123456789',
          verificationImage: 'https://picsum.photos/200/200?random=1',
          description: '黑色时尚数码镜镜框',
          approvalStatus: 'pending',
          createTime: '2024-01-15 10:24:00',
        },
        {
          id: 2,
          wechatNickname: '李四',
          antiCounterfeitCode: '987654321',
          verificationImage: 'https://picsum.photos/200/200?random=2',
          description: '复古风格眼镜镜框',
          approvalStatus: 'approved',
          createTime: '2024-01-15 09:15:00',
        },
        {
          id: 3,
          wechatNickname: '王五',
          antiCounterfeitCode: '564897321',
          verificationImage: 'https://picsum.photos/200/200?random=3',
          description: '运动专用眼镜镜框',
          approvalStatus: 'rejected',
          createTime: '2024-01-14 18:32:00',
        },
        {
          id: 4,
          wechatNickname: '赵六',
          antiCounterfeitCode: '125478963',
          verificationImage: 'https://picsum.photos/200/200?random=4',
          description: '防蓝光护目镜框',
          approvalStatus: 'pending',
          createTime: '2024-01-14 15:47:00',
        },
        {
          id: 5,
          wechatNickname: '钱七',
          antiCounterfeitCode: '741852963',
          verificationImage: 'https://picsum.photos/200/200?random=5',
          description: '商务精英眼镜镜框',
          approvalStatus: 'approved',
          createTime: '2024-01-14 11:00:00',
        },
        {
          id: 6,
          wechatNickname: '孙八',
          antiCounterfeitCode: '369258147',
          verificationImage: 'https://picsum.photos/200/200?random=6',
          description: '学生专用护眼镜框',
          approvalStatus: 'pending',
          createTime: '2024-01-13 16:45:00',
        },
        {
          id: 7,
          wechatNickname: '周九',
          antiCounterfeitCode: '147258369',
          verificationImage: 'https://picsum.photos/200/200?random=7',
          description: '时尚轻薄眼镜镜框',
          approvalStatus: 'approved',
          createTime: '2024-01-13 14:20:00',
        },
        {
          id: 8,
          wechatNickname: '吴十',
          antiCounterfeitCode: '951753486',
          verificationImage: 'https://picsum.photos/200/200?random=8',
          description: '抗疲劳护目镜框',
          approvalStatus: 'rejected',
          createTime: '2024-01-13 11:30:00',
        },
        {
          id: 9,
          wechatNickname: '郑十一',
          antiCounterfeitCode: '852741963',
          verificationImage: 'https://picsum.photos/200/200?random=9',
          description: '老花镜专用镜框',
          approvalStatus: 'pending',
          createTime: '2024-01-12 19:45:00',
        },
        {
          id: 10,
          wechatNickname: '王十二',
          antiCounterfeitCode: '963852741',
          verificationImage: 'https://picsum.photos/200/200?random=10',
          description: '儿童专用护眼镜框',
          approvalStatus: 'approved',
          createTime: '2024-01-12 15:10:00',
        },
        {
          id: 11,
          wechatNickname: '李十三',
          antiCounterfeitCode: '456789123',
          verificationImage: 'https://picsum.photos/200/200?random=11',
          description: '高端定制眼镜镜框',
          approvalStatus: 'pending',
          createTime: '2024-01-12 10:30:00',
        },
        {
          id: 12,
          wechatNickname: '赵十四',
          antiCounterfeitCode: '789456123',
          verificationImage: 'https://picsum.photos/200/200?random=12',
          description: '超轻材质眼镜镜框',
          approvalStatus: 'approved',
          createTime: '2024-01-11 17:20:00',
        },
        {
          id: 13,
          wechatNickname: '钱十五',
          antiCounterfeitCode: '321654987',
          verificationImage: 'https://picsum.photos/200/200?random=13',
          description: '变色镜片眼镜镜框',
          approvalStatus: 'rejected',
          createTime: '2024-01-11 13:45:00',
        },
        {
          id: 14,
          wechatNickname: '孙十六',
          antiCounterfeitCode: '654987321',
          verificationImage: 'https://picsum.photos/200/200?random=14',
          description: '防滑运动眼镜镜框',
          approvalStatus: 'pending',
          createTime: '2024-01-11 09:15:00',
        },
        {
          id: 15,
          wechatNickname: '周十七',
          antiCounterfeitCode: '987321654',
          verificationImage: 'https://picsum.photos/200/200?random=15',
          description: '多功能智能眼镜镜框',
          approvalStatus: 'approved',
          createTime: '2024-01-10 20:30:00',
        },
      ];

      // 过滤数据
      const filteredData = allActivateData.filter((item) => {
        let match = true;

        if (
          antiCounterfeitCode &&
          !item.antiCounterfeitCode.includes(antiCounterfeitCode)
        ) {
          match = false;
        }

        if (wechatNickname && !item.wechatNickname.includes(wechatNickname)) {
          match = false;
        }

        if (approvalStatus && item.approvalStatus !== approvalStatus) {
          match = false;
        }

        return match;
      });

      // 分页处理
      const total = filteredData.length;
      const start = (page - 1) * size;
      const end = start + parseInt(size);
      const list = filteredData.slice(start, end);

      return {
        code: 200,
        message: '获取激活列表成功',
        data: {
          list,
          total,
          page: parseInt(page),
          size: parseInt(size),
        },
      };
    },
  },

  // 更新激活状态
  {
    url: '/service/activate/status',
    method: 'post',
    response: (req) => {
      const { id, status, reason } = req.body;

      return {
        code: 200,
        message: status === 'approved' ? '审批通过成功' : '审批驳回成功',
        data: {
          id,
          status,
          reason: reason || null,
          updateTime: new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
          }),
        },
      };
    },
  },

  // 获取激活详情
  {
    url: '/service/activate/detail/:id',
    method: 'get',
    response: (req) => {
      // 从URL中提取ID参数
      const url = req.url;
      const id = url.split('/').pop() || '1';

      // 模拟不同ID的详情数据
      const detailsMap = {
        1: {
          wechatNickname: '张三',
          antiCounterfeitCode: '123456789',
          description: '黑色时尚款眼镜镜框',
          approvalStatus: 'pending',
        },
        2: {
          wechatNickname: '李四',
          antiCounterfeitCode: '987654321',
          description: '复古风格眼镜镜框',
          approvalStatus: 'approved',
        },
        3: {
          wechatNickname: '王五',
          antiCounterfeitCode: '564897321',
          description: '运动专用眼镜镜框',
          approvalStatus: 'rejected',
        },
      };

      const detail = detailsMap[id] || detailsMap[1];

      return {
        code: 200,
        message: '获取激活详情成功',
        data: {
          id: parseInt(id),
          wechatNickname: detail.wechatNickname,
          antiCounterfeitCode: detail.antiCounterfeitCode,
          verificationImages: [
            `https://picsum.photos/200/200?random=${id}1`,
            `https://picsum.photos/200/200?random=${id}2`,
            `https://picsum.photos/200/200?random=${id}3`,
            `https://picsum.photos/200/200?random=${id}4`,
            `https://picsum.photos/200/200?random=${id}5`,
            `https://picsum.photos/200/200?random=${id}6`,
          ],
          description: detail.description,
          approvalStatus: detail.approvalStatus,
          createTime: '2025-01-01 12:00:00',
          updateTime:
            detail.approvalStatus !== 'pending' ? '2025-01-02 14:30:00' : null,
          approver: detail.approvalStatus !== 'pending' ? '管理员' : null,
          rejectReason:
            detail.approvalStatus === 'rejected'
              ? '图片不清晰，请重新上传'
              : null,
          userInfo: {
            phone: '13800138000',
            address: '北京市朝阳区XXX街道XXX号',
            purchaseTime: '2024-01-10 14:30:00',
          },
        },
      };
    },
  },

  // 获取真伪数据列表
  {
    url: '/service/data/list',
    method: 'get',
    response: (req) => {
      const {
        antiCounterfeitCode,
        productBatch,
        verifyStatus,
        page = 1,
        size = 10,
      } = req.query;

      // 模拟的真伪数据
      const allDataList = [
        {
          id: 1,
          antiCounterfeitCode: 'LB2023001001',
          productName: '时尚防蓝光眼镜',
          productBatch: 'BT202301',
          productionDate: '2023-01-15',
          verifyStatus: 'verified',
          verifyCount: 3,
          lastVerifyTime: '2023-06-23 10:24:00',
          createTime: '2023-01-15 09:00:00',
        },
        {
          id: 2,
          antiCounterfeitCode: 'LB2023001002',
          productName: '商务精英眼镜',
          productBatch: 'BT202301',
          productionDate: '2023-01-15',
          verifyStatus: 'unverified',
          verifyCount: 0,
          lastVerifyTime: '-',
          createTime: '2023-01-15 09:01:00',
        },
        {
          id: 3,
          antiCounterfeitCode: 'LB2023001003',
          productName: '运动专用眼镜',
          productBatch: 'BT202302',
          productionDate: '2023-02-20',
          verifyStatus: 'failed',
          verifyCount: 2,
          lastVerifyTime: '2023-06-22 15:30:00',
          createTime: '2023-02-20 10:00:00',
        },
        {
          id: 4,
          antiCounterfeitCode: 'LB2023001004',
          productName: '学生专用护眼镜',
          productBatch: 'BT202302',
          productionDate: '2023-02-20',
          verifyStatus: 'verified',
          verifyCount: 1,
          lastVerifyTime: '2023-06-21 14:15:00',
          createTime: '2023-02-20 10:01:00',
        },
        {
          id: 5,
          antiCounterfeitCode: 'LB2023001005',
          productName: '老花镜专用镜框',
          productBatch: 'BT202303',
          productionDate: '2023-03-10',
          verifyStatus: 'unverified',
          verifyCount: 0,
          lastVerifyTime: '-',
          createTime: '2023-03-10 11:00:00',
        },
        {
          id: 6,
          antiCounterfeitCode: 'LB2023001006',
          productName: '高端定制眼镜',
          productBatch: 'BT202303',
          productionDate: '2023-03-10',
          verifyStatus: 'verified',
          verifyCount: 5,
          lastVerifyTime: '2023-06-20 09:30:00',
          createTime: '2023-03-10 11:01:00',
        },
        {
          id: 7,
          antiCounterfeitCode: 'LB2023001007',
          productName: '抗疲劳护目镜',
          productBatch: 'BT202304',
          productionDate: '2023-04-05',
          verifyStatus: 'failed',
          verifyCount: 1,
          lastVerifyTime: '2023-06-19 16:45:00',
          createTime: '2023-04-05 14:30:00',
        },
        {
          id: 8,
          antiCounterfeitCode: 'LB2023001008',
          productName: '儿童专用护眼镜',
          productBatch: 'BT202304',
          productionDate: '2023-04-05',
          verifyStatus: 'verified',
          verifyCount: 2,
          lastVerifyTime: '2023-06-18 11:20:00',
          createTime: '2023-04-05 14:31:00',
        },
        {
          id: 9,
          antiCounterfeitCode: 'LB2023001009',
          productName: '变色镜片眼镜',
          productBatch: 'BT202305',
          productionDate: '2023-05-15',
          verifyStatus: 'unverified',
          verifyCount: 0,
          lastVerifyTime: '-',
          createTime: '2023-05-15 10:00:00',
        },
        {
          id: 10,
          antiCounterfeitCode: 'LB2023001010',
          productName: '防滑运动眼镜',
          productBatch: 'BT202305',
          productionDate: '2023-05-15',
          verifyStatus: 'verified',
          verifyCount: 4,
          lastVerifyTime: '2023-06-17 13:15:00',
          createTime: '2023-05-15 10:01:00',
        },
        {
          id: 11,
          antiCounterfeitCode: 'LB2023001011',
          productName: '超轻材质眼镜',
          productBatch: 'BT202306',
          productionDate: '2023-06-01',
          verifyStatus: 'failed',
          verifyCount: 1,
          lastVerifyTime: '2023-06-16 15:30:00',
          createTime: '2023-06-01 09:30:00',
        },
        {
          id: 12,
          antiCounterfeitCode: 'LB2023001012',
          productName: '多功能智能眼镜',
          productBatch: 'BT202306',
          productionDate: '2023-06-01',
          verifyStatus: 'verified',
          verifyCount: 6,
          lastVerifyTime: '2023-06-15 17:45:00',
          createTime: '2023-06-01 09:31:00',
        },
        {
          id: 13,
          antiCounterfeitCode: 'LB2023001013',
          productName: '时尚轻薄眼镜',
          productBatch: 'BT202307',
          productionDate: '2023-07-10',
          verifyStatus: 'unverified',
          verifyCount: 0,
          lastVerifyTime: '-',
          createTime: '2023-07-10 14:00:00',
        },
        {
          id: 14,
          antiCounterfeitCode: 'LB2023001014',
          productName: '商务经典眼镜',
          productBatch: 'BT202307',
          productionDate: '2023-07-10',
          verifyStatus: 'verified',
          verifyCount: 3,
          lastVerifyTime: '2023-06-14 10:20:00',
          createTime: '2023-07-10 14:01:00',
        },
        {
          id: 15,
          antiCounterfeitCode: 'LB2023001015',
          productName: '休闲时尚眼镜',
          productBatch: 'BT202308',
          productionDate: '2023-08-05',
          verifyStatus: 'failed',
          verifyCount: 2,
          lastVerifyTime: '2023-06-13 12:45:00',
          createTime: '2023-08-05 11:15:00',
        },
      ];

      // 过滤数据
      const filteredData = allDataList.filter((item) => {
        let match = true;

        if (
          antiCounterfeitCode &&
          !item.antiCounterfeitCode.includes(antiCounterfeitCode)
        ) {
          match = false;
        }

        if (productBatch && !item.productBatch.includes(productBatch)) {
          match = false;
        }

        if (verifyStatus && item.verifyStatus !== verifyStatus) {
          match = false;
        }

        return match;
      });

      // 分页处理
      const total = filteredData.length;
      const start = (page - 1) * size;
      const end = start + parseInt(size);
      const list = filteredData.slice(start, end);

      return {
        code: 200,
        message: '获取真伪数据列表成功',
        data: {
          list,
          total,
          page: parseInt(page),
          size: parseInt(size),
        },
      };
    },
  },

  // 新增真伪数据
  {
    url: '/service/data',
    method: 'post',
    response: (req) => {
      const data = req.body;

      return {
        code: 200,
        message: '新增真伪数据成功',
        data: {
          id: Math.floor(Math.random() * 10000) + 100,
          ...data,
          createTime: new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
          }),
        },
      };
    },
  },

  // 更新真伪数据
  {
    url: '/service/data/:id',
    method: 'put',
    response: (req) => {
      // 从URL中提取ID参数
      const url = req.url;
      const id = url.split('/').pop() || '1';
      const data = req.body;

      return {
        code: 200,
        message: '更新真伪数据成功',
        data: {
          id: parseInt(id),
          ...data,
          updateTime: new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
          }),
        },
      };
    },
  },

  // 删除真伪数据
  {
    url: '/service/data/:id',
    method: 'delete',
    response: (req) => {
      // 从URL中提取ID参数
      const url = req.url;
      const id = url.split('/').pop() || '1';

      return {
        code: 200,
        message: '删除真伪数据成功',
        data: {
          id: parseInt(id),
        },
      };
    },
  },

  // 获取真伪验证数据列表
  {
    url: '/service/verificationData/list',
    method: 'get',
    response: (req) => {
      const {
        antiCounterfeitCode,
        wechatNickname,
        scanResult,
        page = 1,
        size = 10,
      } = req.query;

      // 模拟的真伪验证数据
      const allVerificationData = [
        {
          id: 1,
          wechatNickname: '张三',
          antiCounterfeitCode: '123456789',
          scanCount: 5,
          productName: '黑色时尚眼镜镜框',
          scanResult: 'authentic',
          lastScanTime: '2024-10-01 10:00',
          firstScanTime: '2024-09-15 14:30',
          userPhone: '13800138001',
        },
        {
          id: 2,
          wechatNickname: '李四',
          antiCounterfeitCode: '987654321',
          scanCount: 3,
          productName: '复古眼镜镜框',
          scanResult: 'fake',
          lastScanTime: '2024-10-02 14:30',
          firstScanTime: '2024-09-20 09:15',
          userPhone: '13800138002',
        },
        {
          id: 3,
          wechatNickname: '王五',
          antiCounterfeitCode: '564897321',
          scanCount: 7,
          productName: '运动眼镜镜框',
          scanResult: 'authentic',
          lastScanTime: '2024-10-03 09:15',
          firstScanTime: '2024-09-10 16:45',
          userPhone: '13800138003',
        },
        {
          id: 4,
          wechatNickname: '赵六',
          antiCounterfeitCode: '125478963',
          scanCount: 2,
          productName: '防蓝光眼镜镜框',
          scanResult: 'authorized',
          lastScanTime: '2024-10-04 16:45',
          firstScanTime: '2024-09-25 11:20',
          userPhone: '13800138004',
        },
        {
          id: 5,
          wechatNickname: '钱七',
          antiCounterfeitCode: '741852963',
          scanCount: 1,
          productName: '商务精英眼镜',
          scanResult: 'fake',
          lastScanTime: '2024-10-05 11:30',
          firstScanTime: '2024-10-05 11:30',
          userPhone: '13800138005',
        },
        {
          id: 6,
          wechatNickname: '孙八',
          antiCounterfeitCode: '369258147',
          scanCount: 4,
          productName: '学生专用护眼镜',
          scanResult: 'authentic',
          lastScanTime: '2024-10-06 15:20',
          firstScanTime: '2024-09-28 10:15',
          userPhone: '13800138006',
        },
        {
          id: 7,
          wechatNickname: '周九',
          antiCounterfeitCode: '147258369',
          scanCount: 6,
          productName: '时尚轻薄眼镜',
          scanResult: 'authorized',
          lastScanTime: '2024-10-07 09:45',
          firstScanTime: '2024-09-22 16:30',
          userPhone: '13800138007',
        },
        {
          id: 8,
          wechatNickname: '吴十',
          antiCounterfeitCode: '951753486',
          scanCount: 2,
          productName: '抗疲劳护目镜',
          scanResult: 'fake',
          lastScanTime: '2024-10-08 14:10',
          firstScanTime: '2024-10-01 08:20',
          userPhone: '13800138008',
        },
      ];

      // 过滤数据
      const filteredData = allVerificationData.filter((item) => {
        let match = true;

        if (
          antiCounterfeitCode &&
          !item.antiCounterfeitCode.includes(antiCounterfeitCode)
        ) {
          match = false;
        }

        if (wechatNickname && !item.wechatNickname.includes(wechatNickname)) {
          match = false;
        }

        if (scanResult && item.scanResult !== scanResult) {
          match = false;
        }

        return match;
      });

      // 分页处理
      const total = filteredData.length;
      const start = (page - 1) * size;
      const end = start + parseInt(size);
      const list = filteredData.slice(start, end);

      return {
        code: 200,
        message: '获取真伪验证数据列表成功',
        data: {
          list,
          total,
          page: parseInt(page),
          size: parseInt(size),
        },
      };
    },
  },

  // 获取真伪验证数据详情
  {
    url: '/service/verificationData/detail/:id',
    method: 'get',
    response: (req) => {
      // const { id } = req.params
      const url = req.url;
      const id = url.split('/').pop() || '1';
      // 根据ID获取对应的详情数据
      const verificationDetailData = {
        1: {
          id: 1,
          scanResult: 'authentic',
          scanTime: '2024-10-01 10:00',
          productName: '黑色时尚眼镜框',
          antiCounterfeitCode: '123456789',
          dealer: 'XX眼镜经销商',
          outboundTime: '2024-09-01 08:00',
          retailStore: 'XX眼镜店',
          verificationTime: '2024-10-01 10:30',
          scanCount: 5,
          wechatNickname: '张三',
          userId: '123456',
          userPhone: '13800138001',
          createTime: '2024-09-15 14:30',
          firstScanTime: '2024-09-15 14:30',
          lastScanTime: '2024-10-01 10:00',
        },
        2: {
          id: 2,
          scanResult: 'fake',
          scanTime: '2024-10-02 14:30',
          productName: '复古眼镜镜框',
          antiCounterfeitCode: '987654321',
          dealer: 'YY眼镜经销商',
          outboundTime: '2024-09-02 09:00',
          retailStore: 'YY眼镜店',
          verificationTime: '2024-10-02 15:00',
          scanCount: 3,
          wechatNickname: '李四',
          userId: '654321',
          userPhone: '13800138002',
          createTime: '2024-09-20 09:15',
          firstScanTime: '2024-09-20 09:15',
          lastScanTime: '2024-10-02 14:30',
        },
        3: {
          id: 3,
          scanResult: 'authentic',
          scanTime: '2024-10-03 09:15',
          productName: '运动眼镜镜框',
          antiCounterfeitCode: '564897321',
          dealer: 'ZZ眼镜经销商',
          outboundTime: '2024-09-03 10:00',
          retailStore: 'ZZ眼镜店',
          verificationTime: '2024-10-03 10:45',
          scanCount: 7,
          wechatNickname: '王五',
          userId: '789123',
          userPhone: '13800138003',
          createTime: '2024-09-10 16:45',
          firstScanTime: '2024-09-10 16:45',
          lastScanTime: '2024-10-03 09:15',
        },
        4: {
          id: 4,
          scanResult: 'authorized',
          scanTime: '2024-10-04 16:45',
          productName: '防蓝光眼镜镜框',
          antiCounterfeitCode: '125478963',
          dealer: 'AA眼镜经销商',
          outboundTime: '2024-09-04 11:00',
          retailStore: 'AA眼镜店',
          verificationTime: '2024-10-04 17:15',
          scanCount: 2,
          wechatNickname: '赵六',
          userId: '456789',
          userPhone: '13800138004',
          createTime: '2024-09-25 11:20',
          firstScanTime: '2024-09-25 11:20',
          lastScanTime: '2024-10-04 16:45',
        },
      };

      const detail = verificationDetailData[id] || verificationDetailData[1];

      return {
        code: 200,
        message: '获取真伪验证数据详情成功',
        data: detail,
      };
    },
  },
];
