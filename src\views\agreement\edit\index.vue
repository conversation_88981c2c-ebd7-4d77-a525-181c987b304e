<template>
  <div class="agreement-edit-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>编辑协议</h2>
      <p>在此页面编辑协议内容</p>
    </div>

    <!-- 编辑表单卡片 -->
    <el-card class="edit-card" shadow="never" v-loading="loading">
      <!-- 协议名称 -->
      <div class="form-section">
        <div class="section-label">协议名称</div>
        <el-input
          v-model="agreementData.name"
          placeholder="请输入协议名称"
          maxlength="50"
          show-word-limit
          class="name-input"
        />
      </div>

      <!-- 协议内容 -->
      <div class="form-section">
        <div class="section-label">协议内容 (50000字以内)</div>
        <div class="editor-container">
          <Toolbar
            style="border-bottom: 1px solid #ccc"
            :editor="editorRef"
            :defaultConfig="toolbarConfig"
            :mode="mode"
          />
          <Editor
            style="height: 500px; overflow-y: hidden"
            v-model="agreementData.content"
            :defaultConfig="editorConfig"
            :mode="mode"
            @onCreated="handleCreated"
          />
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <CustomBtn type="reset" @click="handleCancel">取消</CustomBtn>
        <CustomBtn type="save" @click="handleSave" />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, shallowRef, onMounted, onBeforeUnmount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import '@wangeditor/editor/dist/css/style.css';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import { getAgreementDetail, updateAgreement } from '@/api/agreement';
import CustomBtn from '@/components/custom-btn.vue';

const route = useRoute();
const router = useRouter();
const agreementId = route.params.id;

// 编辑器相关
const editorRef = shallowRef();
const mode = 'default';
const loading = ref(false);
const saving = ref(false);

// 编辑器配置
const toolbarConfig = {
  excludeKeys: ['uploadVideo', 'insertTable', 'codeBlock', 'todo'],
};

const editorConfig = {
  placeholder: '请输入协议内容',
  MENU_CONF: {},
};

// 协议数据
const agreementData = reactive({
  id: '',
  name: '',
  content: '',
  updateTime: '',
});

// 编辑器创建完成时的回调
const handleCreated = (editor) => {
  editorRef.value = editor;
};

// 获取协议详情
const fetchAgreementDetail = async () => {
  loading.value = true;
  try {
    const res = await getAgreementDetail(agreementId);
    if (res.code === 200) {
      agreementData.id = res.data.id;
      agreementData.name = res.data.name;
      agreementData.content = res.data.content;
      agreementData.updateTime = res.data.updateTime;
    } else {
      ElMessage.error(res.message || '获取协议详情失败');
      router.push('/agreement/list');
    }
  } catch (error) {
    console.error('获取协议详情出错:', error);
    ElMessage.error('获取协议详情失败');
    router.push('/agreement/list');
  } finally {
    loading.value = false;
  }
};

// 保存协议
const handleSave = async () => {
  if (!agreementData.name.trim()) {
    ElMessage.warning('协议名称不能为空');
    return;
  }

  saving.value = true;
  try {
    const res = await updateAgreement({
      id: agreementData.id,
      name: agreementData.name,
      content: agreementData.content,
    });
    if (res.code === 200) {
      ElMessage.success('保存成功');
      router.push('/agreement/list');
    } else {
      ElMessage.error(res.message || '保存失败');
    }
  } catch (error) {
    console.error('保存协议出错:', error);
    ElMessage.error('保存失败');
  } finally {
    saving.value = false;
  }
};

// 取消编辑
const handleCancel = () => {
  router.push('/agreement/list');
};

// 页面加载时获取协议详情
onMounted(() => {
  fetchAgreementDetail();
});

// 组件卸载前，销毁编辑器实例
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor === null) return;
  editor.destroy();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables.scss' as *;

.agreement-edit-page {
  padding: $spacing-xl;
  background-color: $bg-color;
  min-height: 100vh;
  max-width: 1280px;
  margin: 0 auto;

  .page-header {
    margin-bottom: $spacing-xl;

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: $text-color-dark;
      margin-bottom: $spacing-xs;
    }

    p {
      font-size: 14px;
      color: $text-color-light;
    }
  }

  .edit-card {
    border-radius: 10px;

    .form-section {
      margin-bottom: 24px;

      .section-label {
        font-size: 16px;
        font-weight: 600;
        color: #86909c;
        margin-bottom: 12px;
      }

      .name-input {
        width: 100%;
      }

      .editor-container {
        border: 1px solid #dcdfe6;
        border-radius: 4px;
      }
    }

    .form-actions {
      margin-top: 40px;
      display: flex;
      justify-content: flex-end;
      gap: 14px;

      .el-button {
        min-width: 100px;
      }
    }
  }
}

// Element Plus 样式覆盖
:deep(.el-card__body) {
  padding: $spacing-xl;
}

:deep(.w-e-toolbar) {
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  border-bottom: none !important;
}

:deep(.w-e-text-container) {
  border: none !important;
}

:deep(.w-e-text-placeholder) {
  color: #c0c4cc;
  font-style: normal;
}
</style>
