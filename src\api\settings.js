import request from '@/utils/request';

// 获取邀请链接统计数据
export function getInviteStats() {
  return request({
    url: '/api/settings/invite/stats',
    method: 'get',
  });
}

// 获取邀请链接列表
export function getInviteList(params) {
  return request({
    url: '/api/settings/invite/list',
    method: 'get',
    params,
  });
}

// 获取专属邀请链接
export function getExclusiveLink() {
  return request({
    url: '/api/settings/invite/exclusive-link',
    method: 'get',
  });
}

// 获取小程序设置
export function getMiniprogramSettings() {
  return request({
    url: '/api/settings/miniprogram',
    method: 'get',
  });
}
