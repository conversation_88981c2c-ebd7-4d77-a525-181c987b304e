// 红包领取相关的mock接口
export default [
  // 获取红包领取列表
  {
    url: '/redpacket/list',
    method: 'get',
    response: (req) => {
      const {
        wechatNickname,
        redpacketType,
        startTime,
        endTime,
        page = 1,
        size = 10,
      } = req.query;

      // 模拟的红包领取数据
      const allRedpacketData = [
        {
          id: 1,
          wechatNickname: '张三',
          userId: '123456',
          redpacketType: 'merchant',
          redpacketTypeText: '商家红包',
          prize: '5元红包',
          receiveTime: '2024-10-01 10:00:00',
        },
        {
          id: 2,
          wechatNickname: '李四',
          userId: '123457',
          redpacketType: 'consumer',
          redpacketTypeText: '消费者红包',
          prize: '10元红包',
          receiveTime: '2024-10-01 10:30:00',
        },
        {
          id: 3,
          wechatNickname: '王五',
          userId: '123458',
          redpacketType: 'merchant',
          redpacketTypeText: '商家红包',
          prize: '2元红包',
          receiveTime: '2024-10-01 11:15:00',
        },
        {
          id: 4,
          wechatNickname: '赵六',
          userId: '123459',
          redpacketType: 'consumer',
          redpacketTypeText: '消费者红包',
          prize: '8元红包',
          receiveTime: '2024-10-01 13:45:00',
        },
        {
          id: 5,
          wechatNickname: '孙七',
          userId: '123460',
          redpacketType: 'merchant',
          redpacketTypeText: '商家红包',
          prize: '3元红包',
          receiveTime: '2024-10-01 14:20:00',
        },
        {
          id: 6,
          wechatNickname: '周八',
          userId: '123461',
          redpacketType: 'consumer',
          redpacketTypeText: '消费者红包',
          prize: '15元红包',
          receiveTime: '2024-10-01 15:30:00',
        },
        {
          id: 7,
          wechatNickname: '吴九',
          userId: '123462',
          redpacketType: 'merchant',
          redpacketTypeText: '商家红包',
          prize: '6元红包',
          receiveTime: '2024-10-01 16:45:00',
        },
        {
          id: 8,
          wechatNickname: '郑十',
          userId: '123463',
          redpacketType: 'consumer',
          redpacketTypeText: '消费者红包',
          prize: '12元红包',
          receiveTime: '2024-10-01 17:20:00',
        },
        {
          id: 9,
          wechatNickname: '陈十一',
          userId: '123464',
          redpacketType: 'merchant',
          redpacketTypeText: '商家红包',
          prize: '4元红包',
          receiveTime: '2024-10-01 18:10:00',
        },
        {
          id: 10,
          wechatNickname: '刘十二',
          userId: '123465',
          redpacketType: 'consumer',
          redpacketTypeText: '消费者红包',
          prize: '20元红包',
          receiveTime: '2024-10-01 19:30:00',
        },
        {
          id: 11,
          wechatNickname: '林十三',
          userId: '123466',
          redpacketType: 'merchant',
          redpacketTypeText: '商家红包',
          prize: '7元红包',
          receiveTime: '2024-10-02 09:15:00',
        },
        {
          id: 12,
          wechatNickname: '黄十四',
          userId: '123467',
          redpacketType: 'consumer',
          redpacketTypeText: '消费者红包',
          prize: '9元红包',
          receiveTime: '2024-10-02 10:45:00',
        },
        {
          id: 13,
          wechatNickname: '何十五',
          userId: '123468',
          redpacketType: 'merchant',
          redpacketTypeText: '商家红包',
          prize: '5元红包',
          receiveTime: '2024-10-02 11:30:00',
        },
        {
          id: 14,
          wechatNickname: '罗十六',
          userId: '123469',
          redpacketType: 'consumer',
          redpacketTypeText: '消费者红包',
          prize: '18元红包',
          receiveTime: '2024-10-02 14:20:00',
        },
        {
          id: 15,
          wechatNickname: '高十七',
          userId: '123470',
          redpacketType: 'merchant',
          redpacketTypeText: '商家红包',
          prize: '3元红包',
          receiveTime: '2024-10-02 16:50:00',
        },
      ];

      // 过滤数据
      const filteredData = allRedpacketData.filter((item) => {
        let match = true;

        if (wechatNickname && !item.wechatNickname.includes(wechatNickname)) {
          match = false;
        }

        if (redpacketType && item.redpacketType !== redpacketType) {
          match = false;
        }

        if (startTime && item.receiveTime.split(' ')[0] < startTime) {
          match = false;
        }

        if (endTime && item.receiveTime.split(' ')[0] > endTime) {
          match = false;
        }

        return match;
      });

      // 分页处理
      const total = filteredData.length;
      const start = (page - 1) * size;
      const end = start + parseInt(size);
      const pageData = filteredData.slice(start, end);

      return {
        code: 200,
        message: '获取红包列表成功',
        data: {
          list: pageData,
          total: total,
          page: parseInt(page),
          size: parseInt(size),
        },
      };
    },
  },

  // 获取红包详情
  {
    url: '/redpacket/detail/:id',
    method: 'get',
    response: (req) => {
      // 从URL中提取ID参数
      const url = req.url;
      const id = url.split('/').pop() || '1';

      // 模拟红包详情数据
      const detailsMap = {
        1: {
          wechatNickname: '张三',
          userId: '123456',
          redpacketType: 'merchant',
          redpacketTypeText: '商家红包',
          prize: '5元红包',
          receiveTime: '2024-10-01 10:00:00',
        },
        2: {
          wechatNickname: '李四',
          userId: '123457',
          redpacketType: 'consumer',
          redpacketTypeText: '消费者红包',
          prize: '10元红包',
          receiveTime: '2024-10-01 10:30:00',
        },
        3: {
          wechatNickname: '王五',
          userId: '123458',
          redpacketType: 'merchant',
          redpacketTypeText: '商家红包',
          prize: '2元红包',
          receiveTime: '2024-10-01 11:15:00',
        },
        4: {
          wechatNickname: '赵六',
          userId: '123459',
          redpacketType: 'consumer',
          redpacketTypeText: '消费者红包',
          prize: '8元红包',
          receiveTime: '2024-10-01 13:45:00',
        },
        5: {
          wechatNickname: '孙七',
          userId: '123460',
          redpacketType: 'merchant',
          redpacketTypeText: '商家红包',
          prize: '3元红包',
          receiveTime: '2024-10-01 14:20:00',
        },
      };

      const detail = detailsMap[id] || detailsMap[1];

      return {
        code: 200,
        message: '获取红包详情成功',
        data: {
          id: parseInt(id),
          wechatNickname: detail.wechatNickname,
          userId: detail.userId,
          redpacketType: detail.redpacketType,
          redpacketTypeText: detail.redpacketTypeText,
          prize: detail.prize,
          receiveTime: detail.receiveTime,
          userInfo: {
            phone:
              detail.userId === '123456'
                ? '13800138000'
                : detail.userId === '123457'
                  ? '13900139000'
                  : detail.userId === '123458'
                    ? '13700137000'
                    : detail.userId === '123459'
                      ? '13600136000'
                      : '13500135000',
            address:
              detail.userId === '123456'
                ? '北京市朝阳区XXX街道XXX号'
                : detail.userId === '123457'
                  ? '上海市浦东新区YYY路YYY号'
                  : detail.userId === '123458'
                    ? '广州市天河区ZZZ大道ZZZ号'
                    : detail.userId === '123459'
                      ? '深圳市南山区AAA街BBB号'
                      : '杭州市西湖区CCC路DDD号',
          },
          redpacketInfo: {
            totalAmount:
              detail.redpacketType === 'merchant' ? '500元' : '1000元',
            remainAmount:
              detail.redpacketType === 'merchant' ? '385元' : '720元',
            source:
              detail.redpacketType === 'merchant'
                ? '商家推广活动'
                : '消费者回馈活动',
            expiryTime: '2024-12-31 23:59:59',
          },
        },
      };
    },
  },
];
