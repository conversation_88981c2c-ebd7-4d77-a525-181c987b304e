import axios from 'axios'
import { ElMessage } from 'element-plus'

const service = axios.create({
  // baseURL: '/api',
  timeout: 5000
})

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    console.error('请求错误：', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const res = response.data
    console.log('API响应原始数据:', response.config.url, res)

    // 检查响应是否有效
    if (!res) {
      console.error('响应数据为空')
      ElMessage.error('响应数据为空')
      return Promise.reject(new Error('响应数据为空'))
    }

    if (res.code !== 200) {
      ElMessage.error(res.message || '系统错误')

      if (res.code === 401) {
        // token过期或未登录
        localStorage.removeItem('token')
        window.location.href = '/login'
      }

      return Promise.reject(new Error(res.message || '系统错误'))
    } else {
      return res
    }
  },
  (error) => {
    console.error('响应错误：', error)
    if (error.response) {
      console.error('响应状态：', error.response.status)
      console.error('响应数据：', error.response.data)
    }
    ElMessage.error(error.message || '请求失败')
    return Promise.reject(error)
  }
)

export default service
