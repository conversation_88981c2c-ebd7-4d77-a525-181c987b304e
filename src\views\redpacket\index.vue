<template>
  <div class="redpacket-container">
    <!-- 列表页面 -->
    <div v-if="!showDetail" class="list-view">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">红包领取列表</h1>
        <p class="page-description">管理系统内所有红包领取记录</p>
      </div>

      <!-- 搜索区域 -->
      <el-card class="search-card" shadow="never">
        <div class="search-form">
          <el-row :gutter="14">
            <el-col :span="6">
              <div class="search-item">
                <div class="search-label">微信昵称</div>
                <el-input
                  v-model="searchForm.wechatNickname"
                  placeholder="请输入微信昵称"
                  clearable
                  class="search-input"
                  prefix-icon="Search"
                />
              </div>
            </el-col>

            <el-col :span="6">
              <div class="search-item">
                <div class="search-label">红包类型</div>
                <el-select
                  v-model="searchForm.redpacketType"
                  placeholder="全部"
                  clearable
                  class="search-input"
                  style="width: 100%"
                >
                  <template #prefix>
                    <el-icon color="#86909C" size="16"><Filter /></el-icon>
                  </template>
                  <el-option label="全部" value="" />
                  <el-option label="商家红包" value="merchant" />
                  <el-option label="消费者红包" value="consumer" />
                </el-select>
              </div>
            </el-col>

            <el-col :span="12">
              <div class="search-item">
                <div class="search-label">领取时间</div>
                <el-date-picker
                  v-model="searchForm.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  style="width: 100%"
                  clearable
                  prefix-icon="Calendar"
                  format="YYYY/MM/DD"
                  value-format="YYYY-MM-DD"
                />
              </div>
            </el-col>
          </el-row>

          <!-- 操作按钮 -->
          <div class="search-actions">
            <CustomBtn type="reset" @click="handleReset" />
            <CustomBtn type="blue" @click="handleQuery" />
          </div>
        </div>
      </el-card>

      <!-- 数据表格 -->
      <el-card class="table-card" shadow="never" :body-style="{ padding: '0' }">
        <el-table :data="redpacketList" style="width: 100%" :loading="loading">
          <el-table-column prop="wechatNickname" label="微信昵称" width="120" />
          <el-table-column
            prop="userId"
            label="用户ID"
            width="250"
            align="center"
          />
          <el-table-column prop="redpacketType" label="红包类型" width="120">
            <template #default="{ row }">
              <span
                :class="[
                  'custom-tag',
                  row.redpacketType === 'merchant'
                    ? 'merchant-tag'
                    : 'consumer-tag',
                ]"
              >
                {{ row.redpacketTypeText }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="prize"
            label="获得奖品"
            width="250"
            align="center"
          />
          <el-table-column
            prop="receiveTime"
            label="扫码领取时间"
            width="160"
            align="center"
          />
          <el-table-column
            label="操作"
            min-width="120"
            header-align="right"
            align="right"
          >
            <template #default="{ row }">
              <CustomBtn type="check" @click="handleView(row)" />
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <!-- 分页 -->
      <div class="pagination-container">
        <SimplePagination
          v-model="currentPage"
          :page-size="pageSize"
          :total="total"
          @change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 详情页面 -->
    <LopTransition>
      <RedpacketDetail
        v-if="showDetail"
        :redpacket-id="selectedRedpacketId"
        @back="handleBackToList"
      />
    </LopTransition>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { Filter } from '@element-plus/icons-vue';
import { getRedpacketList } from '@/api/redpacket';
import { ElMessage } from 'element-plus';
import CustomBtn from '@/components/custom-btn.vue';
import SimplePagination from '@/components/SimplePagination.vue';
import RedpacketDetail from './detail.vue';
import LopTransition from '@/components/LopTransition.vue';

// 搜索表单
const searchForm = reactive({
  wechatNickname: '',
  redpacketType: '',
  dateRange: [],
});

// 分页数据
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 列表数据
const redpacketList = ref([]);
const loading = ref(false);

// 组件切换状态
const showDetail = ref(false);
const selectedRedpacketId = ref(null);

// 获取列表数据
const fetchRedpacketList = async () => {
  loading.value = true;
  try {
    const params = {
      wechatNickname: searchForm.wechatNickname,
      redpacketType: searchForm.redpacketType,
      startTime: searchForm.dateRange?.[0] || '',
      endTime: searchForm.dateRange?.[1] || '',
      page: currentPage.value,
      size: pageSize.value,
    };

    const response = await getRedpacketList(params);
    if (response.code === 200) {
      redpacketList.value = response.data.list;
      total.value = response.data.total;
    }
  } catch (error) {
    console.error('获取红包列表失败:', error);
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

// 查询
const handleQuery = () => {
  currentPage.value = 1;
  fetchRedpacketList();
};

// 重置
const handleReset = () => {
  // 重置搜索表单
  searchForm.wechatNickname = '';
  searchForm.redpacketType = '';
  searchForm.dateRange = [];

  // 重置分页
  currentPage.value = 1;

  // 重新获取数据
  fetchRedpacketList();
};

// 分页变化
const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchRedpacketList();
};

// 查看详情
const handleView = (item) => {
  selectedRedpacketId.value = item.id;
  showDetail.value = true;
};

// 返回列表
const handleBackToList = () => {
  showDetail.value = false;
  selectedRedpacketId.value = null;
};

// 页面加载时获取数据
onMounted(() => {
  fetchRedpacketList();
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/variables.scss' as *;

.redpacket-container {
  padding: 20px;
  min-height: 100vh;
  max-width: 1280px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 16px;

  .page-title {
    font-size: 20px;
    font-weight: 600;
    color: $text-color;
    margin: 0 0 6px 0;
  }

  .page-description {
    font-size: 13px;
    color: $text-color-light;
    margin: 0;
  }
}

.search-card {
  margin-bottom: 16px;
  border-radius: 10px;

  .search-form {
    .search-item {
      margin-bottom: $search-spacing-vertical;

      .search-label {
        display: block;
        font-size: $search-label-size;
        color: $search-label-color;
        margin-bottom: $search-label-margin;
      }

      :deep(.el-input__wrapper) {
        padding: $search-padding;
        height: 40px;
      }

      :deep(.el-input__inner) {
        &::placeholder {
          color: $placeholder-color;
        }
      }

      :deep(.el-select .el-input__wrapper) {
        padding: $search-padding;
        height: 40px;
      }

      :deep(.el-date-editor.el-input__wrapper) {
        padding: $search-padding;
        height: 40px;
      }

      :deep(.el-date-editor) {
        height: 40px;
      }

      :deep(.el-select) {
        height: 40px;
      }

      :deep(.el-input) {
        height: 40px;
      }
    }

    .search-actions {
      display: flex;
      margin-top: 5px;
      justify-content: flex-end;
    }
  }
}
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 0px;
}
.table-card {
  border-radius: 10px;

  // 自定义标签样式
  .custom-tag {
    display: inline-block;
    padding: 4px 12px;
    font-size: 14px;
    border-radius: 16px;
    font-weight: 500;
    text-align: center;
    min-width: 70px;

    &.merchant-tag {
      background-color: #e9eeff;
      color: #165dff;
      border: 1px solid #e9eeff;
    }

    &.consumer-tag {
      background-color: #ebf9f9;
      color: #11c6c2;
      border: 1px solid #ebf9f9;
    }
  }
}

:deep(.el-card) {
  border-radius: 10px;
  overflow: hidden;
}

:deep(.el-button .el-icon) {
  margin-right: 4px;
}
/* 调整表头样式 */
:deep(.el-table__header th) {
  background-color: #f9fafb;
  height: 50px;
  line-height: 50px;
  font-weight: 600 !important;
  color: #86909c !important;
}
</style>
